<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>AjmalNow Provider</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>ajmal_now_doctor</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.1011630073270-6uivlsp4hm99gf2otu0gn9kmcst0t797</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.1011630073270-6uivlsp4hm99gf2otu0gn9kmcst0t797</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSCameraUsageDescription</key>
		<string>This app needs camera access to scan QR codes</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>This app needs your location to test the location feature of the Google Maps
			location picker plugin.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs your location to test the location feature of the Google Maps
			location picker plugin.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Access to image gallery allows the user to upload profile image</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>io.flutter.embedded_views_preview</key>
		<true />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fb2950435051767080</string>
			<string>fb-messenger-share-api</string>
			<string>tel</string>
			<string>telprompt</string>
			<string>maps</string>
			<string>comgooglemaps</string>
			<string>baidumap</string>
			<string>iosamap</string>
			<string>waze</string>
			<string>yandexmaps</string>
			<string>yandexnavi</string>
			<string>citymapper</string>
			<string>mapswithme</string>
			<string>osmandmaps</string>
			<string>dgis</string>
			<string>qqmap</string>
			<string>here-location</string>
			<string>tomtomgo</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
	</dict>
</plist>