<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>1011630073270-6uivlsp4hm99gf2otu0gn9kmcst0t797.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.1011630073270-6uivlsp4hm99gf2otu0gn9kmcst0t797</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>1011630073270-8g78nbt4acu93jj2v44lm3f4bcobcil6.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBFv8qfnoEEgpQLYjq-Hsf8UZQLAcW0xRU</string>
	<key>GCM_SENDER_ID</key>
	<string>1011630073270</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.minalouis.ajmalNowDoctor</string>
	<key>PROJECT_ID</key>
	<string>ajmalnow-b04d2</string>
	<key>STORAGE_BUCKET</key>
	<string>ajmalnow-b04d2.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:1011630073270:ios:e55f4721f6b40aeb604d17</string>
</dict>
</plist>