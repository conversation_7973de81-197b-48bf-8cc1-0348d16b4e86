PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/Analytics (10.15.0):
    - Firebase/Core
  - Firebase/Auth (10.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.15.0)
  - Firebase/Core (10.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.15.0)
  - Firebase/CoreOnly (10.15.0):
    - FirebaseCore (= 10.15.0)
  - Firebase/Messaging (10.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.15.0)
  - Firebase/RemoteConfig (10.15.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.15.0)
  - firebase_analytics (10.5.1):
    - Firebase/Analytics (= 10.15.0)
    - firebase_core
    - FlutterMacOS
  - firebase_auth (4.10.1):
    - Firebase/Auth (~> 10.15.0)
    - Firebase/CoreOnly (~> 10.15.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (2.17.0):
    - Firebase/CoreOnly (~> 10.15.0)
    - FlutterMacOS
  - firebase_messaging (14.6.9):
    - Firebase/CoreOnly (~> 10.15.0)
    - Firebase/Messaging (~> 10.15.0)
    - firebase_core
    - FlutterMacOS
  - firebase_remote_config (3.0.15):
    - Firebase/CoreOnly (~> 10.15.0)
    - Firebase/RemoteConfig (~> 10.15.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.15.0):
    - FirebaseAnalytics/AdIdSupport (= 10.15.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.15.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.15.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.15.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.15.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.15.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - flutter_app_badger (1.3.0):
    - FlutterMacOS
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleAppMeasurement (10.15.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.15.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_auth (from `Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - firebase_remote_config (from `Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos`)
  - flutter_app_badger (from `Flutter/ephemeral/.symlinks/plugins/flutter_app_badger/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  firebase_remote_config:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos
  flutter_app_badger:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_app_badger/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  file_selector_macos: 54fdab7caa3ac3fc43c9fac4d7d8d231277f8cf2
  Firebase: 66043bd4579e5b73811f96829c694c7af8d67435
  firebase_analytics: d6e3fd1c91552c01fa6faa084425be6bbd6e1f28
  firebase_auth: cc27deb838ef5f5dd99571b879f62cea79f290b4
  firebase_core: 4d1711af1e10f9907f468e04f995c59f60bdc710
  firebase_messaging: 801a6ebd7785263051db042f1d75f4151cecbad0
  firebase_remote_config: fec49fca99780fae3c290d974809f2f62900eefc
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: 47cef43728f81a839cf1306576bdd77ffa2eac7e
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: a55ec5f7f8a5b1c2dd750235c1bb419bfb642445
  FirebaseCore: 2cec518b43635f96afe7ac3a9c513e47558abd2e
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 0c0ae1eb722ef0c07f7801e5ded8dccd1357d6d4
  FirebaseRemoteConfig: 64b6ada098c649304114a817effd7e5f87229b11
  flutter_app_badger: 55a64b179f8438e89d574320c77b306e327a1730
  flutter_local_notifications: 7062189aabf7f50938a7b8b6614ffa97656eb0bf
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleAppMeasurement: 722db6550d1e6d552b08398b69a975ac61039338
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_macos: 5f437abeda8c85500ceb03f5c1938a8c5a705399

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.16.2
