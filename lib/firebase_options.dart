// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAh2i4OHjUCnYZbb6pnYnsrBLyG4MyEpl0',
    appId: '1:1011630073270:android:17daca8569974b85604d17',
    messagingSenderId: '1011630073270',
    projectId: 'ajmalnow-b04d2',
    storageBucket: 'ajmalnow-b04d2.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBFv8qfnoEEgpQLYjq-Hsf8UZQLAcW0xRU',
    appId: '1:1011630073270:ios:0b21c42ee3dcfe17604d17',
    messagingSenderId: '1011630073270',
    projectId: 'ajmalnow-b04d2',
    storageBucket: 'ajmalnow-b04d2.appspot.com',
    androidClientId:
        '1011630073270-1k7d9dvqku3l9t3hfvd67q5unqm6dmgj.apps.googleusercontent.com',
    iosClientId:
        '1011630073270-6uivlsp4hm99gf2otu0gn9kmcst0t797.apps.googleusercontent.com',
    iosBundleId: 'com.ajmalnow.ajmalNowDoctor',
  );
}
