import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class ANGeoLocatorHelper {
  final Ref ref;

  ANGeoLocatorHelper({required this.ref});

  Position? currentCoordinates;

  Future<void> getCurrentLocation(BuildContext context) async {
    try {
      var status = await Permission.location.request();
      // if (status != PermissionStatus.granted) {
      //   throw 'Location permission not granted';
      // }
      var position = await Geolocator.getCurrentPosition();
      currentCoordinates = position;
      print('Geolocation: ${currentCoordinates.toString()}');
    } catch (error) {
      // print(error.runtimeType);
      if (ref.read(languageProvider).lang!.languageCode == 'ar') {
        if (error.runtimeType == PermissionDeniedException) {
          ANDialogHelper.gShowConfirmationDialog(
                  context: context,
                  message:
                      'رفض المستخدم الإذن بالوصول إلى موقع الجهاز'.hardcoded)
              .then((value) {
            if (value != null && value) {
              getCurrentLocation(context);
            }
          });
        } else if (error.runtimeType == LocationServiceDisabledException) {
          ANDialogHelper.gShowConfirmationDialog(
                  context: context,
                  message: 'تم ايقاف خدمة تحديد الموقع على الجهاز'.hardcoded)
              .then((value) {
            if (value != null && value) {
              getCurrentLocation(context);
            }
          });
        } else {
          ANDialogHelper.gShowConfirmationDialog(
                  context: context, message: 'هناك خطأ ما'.hardcoded)
              .then((value) {
            if (value != null && value) {
              getCurrentLocation(context);
            }
          });
        }
      } else {
        ANDialogHelper.gShowConfirmationDialog(
                context: context, message: error.toString())
            .then((value) {
          if (value != null && value) {
            getCurrentLocation(context);
          }
        });
      }
    }
  }
}

final geoLocatorProvider = Provider((ref) => ANGeoLocatorHelper(ref: ref));
