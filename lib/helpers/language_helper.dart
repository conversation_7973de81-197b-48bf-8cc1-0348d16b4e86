import 'package:ajmal_now_doctor/helpers/shared_preferences_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';

class ANLanguageHelper extends ChangeNotifier {
  final Ref ref;

  final ANSharedPreferencesHelper _prefs = ANSharedPreferencesHelper();
  final Locale _defaultLocale = const Locale('en', 'US');

  Locale? lang;
  String? fontFamily;

  late NumberFormat priceFormat;
  late NumberFormat numberFormat;
  late DateFormat recorderFormat;

  ANLanguageHelper({required this.ref}) {
    _initLang();
  }

  bool get isRTL => lang!.languageCode == 'ar';

  Future<void> switchLang({required String langCode}) async {
    if (langCode == 'en') {
      lang = const Locale('en', 'US');
    } else {
      lang = const Locale('ar', 'EG');
    }

    ANValidationsHelper.locale = lang;
    await initializeDateFormatting(lang!.languageCode, null);
    recorderFormat =
        DateFormat('mm:ss:SS', lang!.languageCode == 'ar' ? "ar_EG" : "en_US");
    priceFormat =
        NumberFormat("###.0#", lang!.languageCode == 'ar' ? "ar_EG" : "en_US");
    numberFormat =
        NumberFormat("#", lang!.languageCode == 'ar' ? "ar_EG" : "en_US");

    _prefs.storeData(
        key: 'X-localization',
        value: lang!.languageCode,
        encoder: (value) {
          return value;
        });

    notifyListeners();
  }

  void _initLang() async {
    String? langCode = (await _prefs.get(
        key: 'X-localization',
        decoder: (value) {
          return value;
        })) as String?;
    langCode ??= _defaultLocale.languageCode;

    if (langCode == 'en') {
      lang = const Locale('en', 'US');
    } else {
      lang = const Locale('ar', 'EG');
    }
    ANValidationsHelper.locale = lang;
    // await initializeDateFormatting(lang!.languageCode, null);
    recorderFormat =
        DateFormat('mm:ss:SS', lang!.languageCode == 'ar' ? "ar_EG" : "en_US");
    priceFormat =
        NumberFormat("###.0#", lang!.languageCode == 'ar' ? "ar_EG" : "en_US");
    numberFormat =
        NumberFormat("#", lang!.languageCode == 'ar' ? "ar_EG" : "en_US");

    // await ref.read(firebaseRemoteConfigProvider).initConfig();
    _prefs.storeData(
        key: 'X-localization',
        value: lang!.languageCode,
        encoder: (value) {
          return value;
        });
    notifyListeners();
  }

  Map<String, String?> toHeader() {
    return {
      'X-localization': lang?.languageCode,
      'locale': lang?.languageCode,
    };
  }
}

final languageProvider =
    ChangeNotifierProvider((ref) => ANLanguageHelper(ref: ref));
