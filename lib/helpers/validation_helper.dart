import 'package:flutter/material.dart';
import 'package:fzregex/fzregex.dart';
import 'package:fzregex/utils/pattern.dart';

class ANValidationsHelper {
  static late Locale? locale;

  static const Pattern emailPattern =
      r"^((([a-zA-Z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$";

  static String? getValidators(
      {required Map<String, dynamic> validation,
      required dynamic value,
      String? confirmPassword}) {
    if (validation['is_required'] && validation['validation_type'] != null) {
      switch (validation['validation_type']) {
        case 'email':
          return validateEmail(value, true, userMsg: validation['error_msg']);
        case 'password':
          return validatePassword(value, validation['level'], true,
              userMsg: validation['error_msg']);
        case 'confirmPassword':
          return validateConfirmPassword(value, confirmPassword!, true,
              userMsg: validation['error_msg']);
        case 'phone':
          return validatePhone(value, true, userMsg: validation['error_msg']);
        case 'positive_number':
          return validatePositiveNumber(value,
              userMsg: validation['error_msg']);
        case 'past_date':
          return validatePastDate(value, true,
              userMsg: validation['error_msg']);
      }
    } else if (validation['is_required'] &&
        validation['validation_type'] == null) {
      return validateNotEmpty(value, userMsg: validation['error_msg']);
    } else if (!validation['is_required'] &&
        validation['validation_type'] != null) {
      switch (validation['validation_type']) {
        case 'email':
          return validateEmail(value, false, userMsg: validation['error_msg']);
        case 'password':
          return validatePassword(value, validation['level'], false,
              userMsg: validation['error_msg']);
        case 'confirmPassword':
          return validateConfirmPassword(value, confirmPassword!, false,
              userMsg: validation['error_msg']);
        case 'phone':
          return validatePhone(value, false, userMsg: validation['error_msg']);
        case 'positive_number':
          return validatePositiveNumber(value,
              userMsg: validation['error_msg']);
        case 'past_date':
          return validatePastDate(value, false,
              userMsg: validation['error_msg']);
      }
    }
    return null; // No validation required
  }

  static bool _validateNotEmpty(String? value) {
    return (value != null && value.isNotEmpty && value.trim().isNotEmpty);
  }

  // Checks whether a string is empty or not
  // Returns null in case of success, i.e the string is not empty
  // Returns a String error message in case the of failure
  static String? validateNotEmpty(String? value, {String? userMsg}) {
    if (_validateNotEmpty(value)) {
      return null;
    } else {
      return userMsg ??
          (locale!.languageCode == "en"
              ? "This field can't be empty"
              : "لا يمكن أن يكون هذا النص فارغًا");
    }
  }

  static String? validatePassword(String? value, String levels, bool isRequired,
      {String? userMsg, bool allowWhiteSpaces = false}) {
    if (isRequired && !_validateNotEmpty(value)) {
      return validateNotEmpty(value, userMsg: userMsg);
    }

    switch (levels) {
      case 'passwordEasy':
        if (Fzregex.hasMatch(value!, FzPattern.passwordEasy)) {
          return null;
        } else {
          return userMsg ??
              (locale!.languageCode == "en"
                  ? 'Password must contains:\n* Minimum characters: 8\n* Without whitespaces'
                  : "يجب أن تحتوي كلمة المرور على: \n* الحد الأدنى من الأحرف: 8 \n* بدون مسافات");
        }
      case 'passwordNormal1':
        if (Fzregex.hasMatch(value!, FzPattern.passwordNormal1)) {
          return null;
        } else {
          return userMsg ??
              (locale!.languageCode == "en"
                  ? 'Password must contains:\n* 1 letter\n* 1 number\n* Minimum characters: 8\n* Without whitespaces'
                  : "يجب أن تحتوي كلمة المرور على: \n* حرف واحد \n* رقم واحد \n *الحد الأدنى من الأحرف: 8 \n* بدون مسافات");
        }
      case 'passwordNormal2':
        if (Fzregex.hasMatch(value!, FzPattern.passwordNormal2)) {
          return null;
        } else {
          return userMsg ??
              (locale!.languageCode == "en"
                  ? 'Password must contains:\n* 1 letter\n* 1 number\n* Minimum characters: 8\n* Without whitespaces'
                  : 'يجب أن تحتوي كلمة المرور على: \n* حرف واحد \n* رقم واحد \n *الحد الأدنى من الأحرف: 8 \n* بدون مسافات');
        }
      case 'passwordNormal3':
        if (Fzregex.hasMatch(value!, FzPattern.passwordNormal3)) {
          return null;
        } else {
          return userMsg ??
              (locale!.languageCode == "en"
                  ? 'Password must contains:\n* 1 uppercase letter\n* 1 lowercase letter\n* 1 number\n* Minimum characters: 8\n* Without whitespaces'
                  : 'يجب أن تحتوي كلمة المرور على: \n *1 حرف كبير \n* حرف صغير واحد \n* رقم واحد \n* الحد الأدنى للأحرف: 8 \n* بدون مسافات');
        }
      case 'passwordHard':
        if (Fzregex.hasMatch(value!, FzPattern.passwordHard)) {
          return null;
        } else {
          return userMsg ??
              (locale!.languageCode == "en"
                  ? 'Password must contains:\n* 1 uppercase letter\n* 1 lowercase letter\n* 1 number\n* 1 special character (symbol)\n* Minimum characters: 8\n* Without whitespaces'
                  : 'يجب أن تحتوي كلمة المرور على: \n *1 حرف كبير \n* حرف صغير واحد \n* رقم واحد \n* حرف خاص واحد (رمز) \n* الحد الأدنى من الأحرف: 8 \n* بدون مسافات');
        }
    }
    return null;
  }

  static String? validateConfirmPassword(
      String? value, String password, bool isRequired,
      {String? userMsg}) {
    if (isRequired && !_validateNotEmpty(value)) {
      return validateNotEmpty(value, userMsg: userMsg);
    }

    if (password.compareTo(value!) != 0) {
      return userMsg ??
          (locale!.languageCode == "en"
              ? "Passwords don't match"
              : "كلمات المرور غير متطابقة");
    } else {
      return null;
    }
  }

  static String? validateEmail(String? value, bool isRequired,
      {String? userMsg}) {
    if (isRequired && !_validateNotEmpty(value)) {
      return validateNotEmpty(value, userMsg: userMsg);
    }
    if (Fzregex.hasMatch(value!, emailPattern)) {
      return null;
    } else {
      return userMsg ??
          (locale!.languageCode == "en"
              ? 'Please provide a valid email format'
              : 'يرجى تقديم بريد إلكتروني صحيح');
    }
  }

  static String? validatePhone(String? value, bool isRequired,
      {String? userMsg}) {
    if (isRequired && !_validateNotEmpty(value)) {
      return validateNotEmpty(value, userMsg: userMsg);
    }

    // check if the number contains only digits
    RegExp exp = RegExp(r"[\d]+", unicode: true);
    if (exp.stringMatch(value!) == value) {
      return null;
    }

    // Mach 11 numeric digits
    // RegExp exp = RegExp(r"[\d]{11}", unicode: true);
    // if (exp.stringMatch(value!) == value) return null;

    // // Check if the number has a + and 12 digits
    // exp = RegExp(r"\+[\d]{12}", unicode: true);
    // if (exp.stringMatch(value) == value) return null;

    return userMsg ??
        (locale!.languageCode == "en"
            ? 'Please provide a valid phone number'
            : 'الرجاء تقديم رقم هاتف صحيح');
  }

  static String? validatePositiveNumber(double value, {String? userMsg}) {
    if (value > 0) {
      return null;
    } else {
      return userMsg ??
          (locale!.languageCode == "en"
              ? 'Please provide a positive number'
              : 'يرجى تقديم رقم موجب');
    }
  }

  static String? validatePastDate(String? date, bool isRequired,
      {String? userMsg}) {
    if (isRequired && !_validateNotEmpty(date)) {
      return validateNotEmpty(date, userMsg: userMsg);
    }

    DateTime? dateTime = DateTime.tryParse(date!);
    if (dateTime != null &&
        dateTime.isBefore(DateTime.now().add(const Duration(days: 1)))) {
      return null;
    } else {
      return userMsg ??
          (locale!.languageCode == "en"
              ? 'This date is in the past\nPlease provide a future date'
              : 'هذا التاريخ في الماضي \n الرجاء تقديم تاريخ مستقبلي');
    }
  }
}
