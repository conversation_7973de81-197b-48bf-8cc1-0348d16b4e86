import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LocationApiService {
  final Ref ref;

  LocationApiService({required this.ref});

  /// Fetch all active city districts
  Future<ANResponseModel> fetchCityDistricts() async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
        path: 'city_districts',
        queryParam: {
          'where[isActive][equals]': 'true',
          'sort': 'city,displayOrder',
          'limit': '1000',
        },
      );
      return response;
    } catch (e) {
      return ANResponseModel(
        statusCode: 0,
        message: 'Error fetching city districts: $e',
      );
    }
  }

  /// Fetch districts for a specific city
  Future<ANResponseModel> fetchDistrictsByCity(String cityCode) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
        path: 'city_districts',
        queryParam: {
          'where[city][equals]': cityCode,
          'where[isActive][equals]': 'true',
          'sort': 'displayOrder',
          'limit': '1000',
        },
      );
      return response;
    } catch (e) {
      return ANResponseModel(
        statusCode: 0,
        message: 'Error fetching districts for city $cityCode: $e',
      );
    }
  }
}

final locationApiServiceProvider = Provider<LocationApiService>(
  (ref) => LocationApiService(ref: ref),
); 