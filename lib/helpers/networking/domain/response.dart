import 'package:ajmal_now_doctor/helpers/networking/domain/response_error.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'response.freezed.dart';
part 'response.g.dart';

@Freezed()
class ANResponseModel with _$ANResponseModel {
  const factory ANResponseModel(
          {String? message,
          required int statusCode,
          Map<String, dynamic>? data,
          // dynamic doc,
          // dynamic docs,
          // dynamic sponsored,
          // dynamic screens,
          // dynamic terms,
          // dynamic conditions,
          // int? totalPages,
          @JsonKey(name: 'error') String? errorMessage,
          @JsonKey(name: 'errors') List<ANResponseErrorModel>? error}) =
      _ANResponseModel;

  factory ANResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ANResponseModelFromJson(json);
}
