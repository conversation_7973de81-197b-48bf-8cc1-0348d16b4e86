// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'response_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANResponseErrorModel _$ANResponseErrorModelFromJson(Map<String, dynamic> json) {
  return _ANResponseErrorModel.fromJson(json);
}

/// @nodoc
mixin _$ANResponseErrorModel {
  String get name => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  List<ANResponseErrorDataModel> get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANResponseErrorModelCopyWith<ANResponseErrorModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANResponseErrorModelCopyWith<$Res> {
  factory $ANResponseErrorModelCopyWith(ANResponseErrorModel value,
          $Res Function(ANResponseErrorModel) then) =
      _$ANResponseErrorModelCopyWithImpl<$Res, ANResponseErrorModel>;
  @useResult
  $Res call({String name, String message, List<ANResponseErrorDataModel> data});
}

/// @nodoc
class _$ANResponseErrorModelCopyWithImpl<$Res,
        $Val extends ANResponseErrorModel>
    implements $ANResponseErrorModelCopyWith<$Res> {
  _$ANResponseErrorModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorDataModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANResponseErrorModelCopyWith<$Res>
    implements $ANResponseErrorModelCopyWith<$Res> {
  factory _$$_ANResponseErrorModelCopyWith(_$_ANResponseErrorModel value,
          $Res Function(_$_ANResponseErrorModel) then) =
      __$$_ANResponseErrorModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String message, List<ANResponseErrorDataModel> data});
}

/// @nodoc
class __$$_ANResponseErrorModelCopyWithImpl<$Res>
    extends _$ANResponseErrorModelCopyWithImpl<$Res, _$_ANResponseErrorModel>
    implements _$$_ANResponseErrorModelCopyWith<$Res> {
  __$$_ANResponseErrorModelCopyWithImpl(_$_ANResponseErrorModel _value,
      $Res Function(_$_ANResponseErrorModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_$_ANResponseErrorModel(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ANResponseErrorDataModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANResponseErrorModel implements _ANResponseErrorModel {
  const _$_ANResponseErrorModel(
      {required this.name,
      required this.message,
      required final List<ANResponseErrorDataModel> data})
      : _data = data;

  factory _$_ANResponseErrorModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANResponseErrorModelFromJson(json);

  @override
  final String name;
  @override
  final String message;
  final List<ANResponseErrorDataModel> _data;
  @override
  List<ANResponseErrorDataModel> get data {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  String toString() {
    return 'ANResponseErrorModel(name: $name, message: $message, data: $data)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANResponseErrorModel &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, message, const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANResponseErrorModelCopyWith<_$_ANResponseErrorModel> get copyWith =>
      __$$_ANResponseErrorModelCopyWithImpl<_$_ANResponseErrorModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANResponseErrorModelToJson(
      this,
    );
  }
}

abstract class _ANResponseErrorModel implements ANResponseErrorModel {
  const factory _ANResponseErrorModel(
          {required final String name,
          required final String message,
          required final List<ANResponseErrorDataModel> data}) =
      _$_ANResponseErrorModel;

  factory _ANResponseErrorModel.fromJson(Map<String, dynamic> json) =
      _$_ANResponseErrorModel.fromJson;

  @override
  String get name;
  @override
  String get message;
  @override
  List<ANResponseErrorDataModel> get data;
  @override
  @JsonKey(ignore: true)
  _$$_ANResponseErrorModelCopyWith<_$_ANResponseErrorModel> get copyWith =>
      throw _privateConstructorUsedError;
}
