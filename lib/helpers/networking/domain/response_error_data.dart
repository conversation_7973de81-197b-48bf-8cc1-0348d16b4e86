import 'package:freezed_annotation/freezed_annotation.dart';

part 'response_error_data.freezed.dart';
part 'response_error_data.g.dart';

@Freezed()
class ANResponseErrorDataModel with _$ANResponseErrorDataModel {
  const factory ANResponseErrorDataModel({
    required String message,
    required String field,
  }) = _ANResponseErrorDataModel;

  factory ANResponseErrorDataModel.fromJson(Map<String, dynamic> json) =>
      _$ANResponseErrorDataModelFromJson(json);
}

/*

      {
          "name": "ValidationError",
          "message": "The following field is invalid: gender",
          "data": [
              {
                  "message": "This field has an invalid selection",
                  "field": "gender"
              }
          ]
      }

*/
