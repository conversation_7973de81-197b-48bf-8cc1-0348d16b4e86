// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'response_error_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANResponseErrorDataModel _$ANResponseErrorDataModelFromJson(
    Map<String, dynamic> json) {
  return _ANResponseErrorDataModel.fromJson(json);
}

/// @nodoc
mixin _$ANResponseErrorDataModel {
  String get message => throw _privateConstructorUsedError;
  String get field => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANResponseErrorDataModelCopyWith<ANResponseErrorDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANResponseErrorDataModelCopyWith<$Res> {
  factory $ANResponseErrorDataModelCopyWith(ANResponseErrorDataModel value,
          $Res Function(ANResponseErrorDataModel) then) =
      _$ANResponseErrorDataModelCopyWithImpl<$Res, ANResponseErrorDataModel>;
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class _$ANResponseErrorDataModelCopyWithImpl<$Res,
        $Val extends ANResponseErrorDataModel>
    implements $ANResponseErrorDataModelCopyWith<$Res> {
  _$ANResponseErrorDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANResponseErrorDataModelCopyWith<$Res>
    implements $ANResponseErrorDataModelCopyWith<$Res> {
  factory _$$_ANResponseErrorDataModelCopyWith(
          _$_ANResponseErrorDataModel value,
          $Res Function(_$_ANResponseErrorDataModel) then) =
      __$$_ANResponseErrorDataModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String field});
}

/// @nodoc
class __$$_ANResponseErrorDataModelCopyWithImpl<$Res>
    extends _$ANResponseErrorDataModelCopyWithImpl<$Res,
        _$_ANResponseErrorDataModel>
    implements _$$_ANResponseErrorDataModelCopyWith<$Res> {
  __$$_ANResponseErrorDataModelCopyWithImpl(_$_ANResponseErrorDataModel _value,
      $Res Function(_$_ANResponseErrorDataModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? field = null,
  }) {
    return _then(_$_ANResponseErrorDataModel(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      field: null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANResponseErrorDataModel implements _ANResponseErrorDataModel {
  const _$_ANResponseErrorDataModel(
      {required this.message, required this.field});

  factory _$_ANResponseErrorDataModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANResponseErrorDataModelFromJson(json);

  @override
  final String message;
  @override
  final String field;

  @override
  String toString() {
    return 'ANResponseErrorDataModel(message: $message, field: $field)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANResponseErrorDataModel &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.field, field) || other.field == field));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, message, field);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANResponseErrorDataModelCopyWith<_$_ANResponseErrorDataModel>
      get copyWith => __$$_ANResponseErrorDataModelCopyWithImpl<
          _$_ANResponseErrorDataModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANResponseErrorDataModelToJson(
      this,
    );
  }
}

abstract class _ANResponseErrorDataModel implements ANResponseErrorDataModel {
  const factory _ANResponseErrorDataModel(
      {required final String message,
      required final String field}) = _$_ANResponseErrorDataModel;

  factory _ANResponseErrorDataModel.fromJson(Map<String, dynamic> json) =
      _$_ANResponseErrorDataModel.fromJson;

  @override
  String get message;
  @override
  String get field;
  @override
  @JsonKey(ignore: true)
  _$$_ANResponseErrorDataModelCopyWith<_$_ANResponseErrorDataModel>
      get copyWith => throw _privateConstructorUsedError;
}
