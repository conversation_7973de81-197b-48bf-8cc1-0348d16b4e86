import 'package:ajmal_now_doctor/helpers/networking/domain/response_error_data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'response_error.freezed.dart';
part 'response_error.g.dart';

@Freezed()
class ANResponseErrorModel with _$ANResponseErrorModel {
  const factory ANResponseErrorModel({
    required String name,
    required String message,
    required List<ANResponseErrorDataModel> data,
  }) = _ANResponseErrorModel;

  factory ANResponseErrorModel.fromJson(Map<String, dynamic> json) =>
      _$ANResponseErrorModelFromJson(json);
}

/*

      {
          "name": "ValidationError",
          "message": "The following field is invalid: gender",
          "data": [
              {
                  "message": "This field has an invalid selection",
                  "field": "gender"
              }
          ]
      }

*/
