import 'dart:io';

import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/helpers/networking/dio_logging_interceptor.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANNetworkController {
  final Ref ref;
  final String baseUrl;
  final bool returnFormattedResponseGlobal;

  late Dio _dio;

  Map<String, dynamic> fixedHeaders = {'Accept': 'application/json'};

  ANNetworkController(
      {required this.ref,
      required this.baseUrl,
      this.returnFormattedResponseGlobal = true,
      Map<String, dynamic>? headers}) {
    var options = BaseOptions(
        connectTimeout: const Duration(milliseconds: 3000),
        receiveTimeout: const Duration(milliseconds: 5000),
        receiveDataWhenStatusError: true);
    _dio = Dio(options);

    // Always the last interceptor to be added, to be able to log all the changes made by previous interceptors
    if (kDebugMode) {
      _dio.interceptors.add(DioLoggingInterceptor());
    }

    if (headers != null) {
      fixedHeaders.addAll(headers);
    }
  }

  void cancelRequest(CancelToken cancelToken) {
    cancelToken.cancel();
  }

  Future<dynamic> getData(
      {required String path,
      bool? returnFormattedResponse,
      CancelToken? cancelToken,
      Map<String, dynamic>? queryParam,
      Map<String, dynamic>? headers,
      int retryCount = 3,
      bool useBaseUrl = true}) async {
    Map<String, dynamic> data = {};
    try {
      headers ??= {};
      fixedHeaders.addAll(headers);

      // Add locale to headers instead of query parameters
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      fixedHeaders['X-localization'] = locale;
      fixedHeaders['locale'] = locale;

      String fullPath;
      if (useBaseUrl) {
        fullPath = baseUrl + path;
      } else {
        fullPath = path;
      }

      Response response = await _dio.get(fullPath,
          cancelToken: cancelToken,
          queryParameters: queryParam,
          options: Options(headers: fixedHeaders));

      data['data'] = response.data;
      data['statusCode'] = response.statusCode;

      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        // Connect and receive timeout errors
        if (retryCount > 0) {
          return await getData(
              path: path,
              cancelToken: cancelToken,
              queryParam: queryParam,
              headers: headers,
              retryCount: retryCount - 1,
              useBaseUrl: useBaseUrl);
        }
        if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
          return const ANResponseModel(
            message: 'Connection Timeout, please try again later',
            statusCode: 0,
          );
        } else {
          return {'message': 'Connection Timeout, please try again later', 'statusCode': 0};
        }
      } else {
        if (e.error is SocketException) {
          // No internet connection
          if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
            return const ANResponseModel(
              message: 'No internet connection found',
              statusCode: 0,
            );
          } else {
            return {'message': 'No internet connection found', 'statusCode': 0};
          }
        }
      }
      // Any other error
      data['data'] = e.response!.data;
      data['statusCode'] = e.response!.statusCode;
      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    }
  }

  // TODO: modify the rest of the requests
  Future<dynamic> postData(
      {required String path,
      bool? returnFormattedResponse,
      CancelToken? cancelToken,
      Map<String, dynamic>? queryParam,
      Map<String, dynamic>? body,
      Map<String, dynamic>? headers,
      Map<String, dynamic>? files,
      bool monitorSendProgress = false,
      bool monitorReceiveProgress = false,
      bool useBaseUrl = true}) async {
    Map<String, dynamic> data = {};
    try {
      headers ??= {};
      fixedHeaders.addAll(headers);

      // Add locale to headers
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      fixedHeaders['X-localization'] = locale;
      fixedHeaders['locale'] = locale;

      String fullPath;
      if (useBaseUrl) {
        fullPath = baseUrl + path;
      } else {
        fullPath = path;
      }

      Map<String, dynamic> map = {};
      if (body != null) {
        map.addAll(body);
      }

      if (files != null) {
        map[files.keys.first] = [];
        for (MultipartFile file in files.values) {
          map[files.keys.first].add(file);
        }
      }

      var formData = FormData.fromMap(map);

      Response response = await _dio.post(fullPath,
          cancelToken: cancelToken,
          queryParameters: queryParam,
          data: formData,
          options: Options(headers: fixedHeaders),
          onSendProgress: monitorSendProgress ? (int sent, int total) {} : null,
          onReceiveProgress:
              monitorReceiveProgress ? (int received, int total) {} : null);

      data['data'] = response.data;
      data['statusCode'] = response.statusCode;
      data['error'] = response.data['error'];

      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    } on DioException catch (e) {
      data['data'] = e.response!.data;
      data['statusCode'] = e.response!.statusCode;
      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    }
  }

  Future<dynamic> postJsonData(
      {required String path,
      bool? returnFormattedResponse,
      CancelToken? cancelToken,
      Map<String, dynamic>? queryParam,
      Map<String, dynamic>? body,
      Map<String, dynamic>? headers,
      bool monitorSendProgress = false,
      bool monitorReceiveProgress = false,
      bool useBaseUrl = true}) async {
    Map<String, dynamic> data = {};
    try {
      headers ??= {};
      fixedHeaders.addAll(headers);
      fixedHeaders['Content-Type'] = 'application/json';

      // Authentication headers removed due to Firebase token issues

      // Add locale to headers
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      fixedHeaders['X-localization'] = locale;
      fixedHeaders['locale'] = locale;

      String fullPath;
      if (useBaseUrl) {
        fullPath = baseUrl + path;
      } else {
        fullPath = path;
      }

      Response response = await _dio.post(fullPath,
          cancelToken: cancelToken,
          queryParameters: queryParam,
          data: body, // Send as JSON, not FormData
          options: Options(headers: fixedHeaders),
          onSendProgress: monitorSendProgress ? (int sent, int total) {} : null,
          onReceiveProgress:
              monitorReceiveProgress ? (int received, int total) {} : null);

      data['data'] = response.data;
      data['statusCode'] = response.statusCode;
      data['error'] = response.data['error'];

      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    } on DioException catch (e) {
      data['data'] = e.response!.data;
      data['statusCode'] = e.response!.statusCode;
      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    }
  }

  Future<dynamic> patchData(
      {required String path,
      bool? returnFormattedResponse,
      CancelToken? cancelToken,
      Map<String, dynamic>? queryParam,
      Map<String, dynamic>? body,
      Map<String, dynamic>? headers,
      Map<String, dynamic>? files,
      bool useBaseUrl = true}) async {
    Map<String, dynamic> data = {};
    try {
      headers ??= {};
      fixedHeaders.addAll(headers);

      // Add locale to headers
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      fixedHeaders['X-localization'] = locale;
      fixedHeaders['locale'] = locale;

      String fullPath;
      if (useBaseUrl) {
        fullPath = baseUrl + path;
      } else {
        fullPath = path;
      }

      Map<String, dynamic> map = {};
      if (body != null) {
        map.addAll(body);
      }

      if (files != null) {
        map[files.keys.first] = [];
        for (MultipartFile file in files.values.first) {
          map[files.keys.first].add(file);
        }
      }

      var formData = FormData.fromMap(map);
      Response response = await _dio.patch(fullPath,
          cancelToken: cancelToken,
          queryParameters: queryParam,
          data: formData,
          options: Options(headers: fixedHeaders));

      data['data'] = response.data;
      data['statusCode'] = response.statusCode;

      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    } on DioException catch (e) {
      data['data'] = e.response!.data;
      data['statusCode'] = e.response!.statusCode;
      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    }
  }

  Future<dynamic> deleteData(
      {required String path,
      bool? returnFormattedResponse,
      CancelToken? cancelToken,
      Map<String, dynamic>? queryParam,
      Map<String, dynamic>? body,
      Map<String, dynamic>? headers,
      bool useBaseUrl = true}) async {
    Map<String, dynamic> data = {};
    try {
      headers ??= {};
      fixedHeaders.addAll(headers);

      // Add locale to headers
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      fixedHeaders['X-localization'] = locale;
      fixedHeaders['locale'] = locale;

      String fullPath;
      if (useBaseUrl) {
        fullPath = baseUrl + path;
      } else {
        fullPath = path;
      }

      Response response = await _dio.delete(fullPath,
          cancelToken: cancelToken,
          queryParameters: queryParam,
          data: body,
          options: Options(headers: fixedHeaders));

      data['data'] = response.data;
      data['statusCode'] = response.statusCode;

      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    } on DioException catch (e) {
      data['data'] = e.response!.data;
      data['statusCode'] = e.response!.statusCode;
      if (returnFormattedResponse ?? returnFormattedResponseGlobal) {
        return ANResponseModel.fromJson(data);
      } else {
        return data;
      }
    }
  }
}

final networkProvider = Provider<ANNetworkController>(
    (ref) => ANNetworkController(ref: ref, baseUrl: Config.baseUrl));
