import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/general_widgets/success.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:google_fonts/google_fonts.dart';

enum DialogType {
  confirmCancel,
  yesNo,
  confirm,
  upgrade,
  agree,
  agreeDisagree,
  error,
}

class ANDialogHelper {
  static String _getButtonText(BuildContext context, DialogType type, int idx) {
    switch (type) {
      case DialogType.confirm:
      case DialogType.confirmCancel:
        return idx == 0 ? S.of(context).kConfirm : S.of(context).kCancel;
      case DialogType.yesNo:
        return idx == 0 ? S.of(context).kYes : S.of(context).kNo;
      case DialogType.upgrade:
        return S.of(context).kUpgradeNow;
      case DialogType.agree:
      case DialogType.agreeDisagree:
        return idx == 0 ? S.of(context).kAgree : S.of(context).kDisagree;
      case DialogType.error:
        return S.of(context).kConfirm;
      default:
        throw Exception('Invalid DialogType');
    }
  }

  static List<Widget> _getButtons(BuildContext context, DialogType type) {
    List<Widget> widgets = [];

    if (type == DialogType.upgrade) {
      widgets.add(
        MaterialButton(
          child:
              Text(S.of(context).kUpgradeNow, style: TextSize.r.regularStyle),
          onPressed: () {
            Navigator.pop(context, true);
            // StoreRedirect.redirect(
            //     androidAppId: kAndroidAppId, iOSAppId: kIOSAppId);
          },
        ),
      );
    } else {
      bool addCancelBtn = true;
      switch (type) {
        case DialogType.confirm:
        case DialogType.agree:
        case DialogType.error:
          addCancelBtn = false;
          break;
        default:
          addCancelBtn = true;
          break;
      }

      widgets.add(
        MaterialButton(
          child: Text(
            _getButtonText(context, type, 0),
          ),
          onPressed: () => Navigator.pop(context, true),
        ),
      );

      if (addCancelBtn) {
        widgets.add(
          MaterialButton(
            child: Text(
              _getButtonText(context, type, 1),
            ),
            onPressed: () => Navigator.pop(context, false),
          ),
        );
      }
    }

    return widgets;
  }

  static Future<bool?> gShowConfirmationDialog({
    required BuildContext context,
    required String message,
    String? content,
    DialogType type = DialogType.confirmCancel,
    TextAlign align = TextAlign.center,
    bool barrierDismissible = true,
  }) async {
    return await showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          // Title message
          title: Center(
            child: Text(
              message,
            ),
          ),

          // Content if applicable
          content: content != null
              ? Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Text(content, textAlign: align),
                )
              : Container(),

          // Action buttons
          actions: _getButtons(context, type),
        );
      },
    );
  }

  static Future<bool?> gShowCustomDialog({
    required BuildContext context,
    required String title,
    Widget? body,
    required String primaryButtonLabel,
    required void Function() primaryButtonCallBack,
    String? secondaryButtonLabel,
    void Function()? secondaryButtonCallBack,
    bool barrierDismissible = true,
  }) async {
    if (secondaryButtonLabel != null) {
      assert(secondaryButtonCallBack != null);
    }
    return await showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: circularBorderR,
          ),
          child: Padding(
            padding: allRoundPaddingXXL,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style:
                      GoogleFonts.philosopher(textStyle: TextSize.xl.boldStyle),
                  textAlign: TextAlign.center,
                ),
                if (body != null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      gapHR,
                      body,
                    ],
                  ),
                gapHXXXL,
                gapHR,
                Center(
                  child: ANElevatedButton(
                    onPressed: () => primaryButtonCallBack.call(),
                    child: Text(
                      primaryButtonLabel,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                if (secondaryButtonLabel != null &&
                    secondaryButtonCallBack != null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      gapHR,
                      Center(
                        child: ANElevatedButton(
                          backgroundColor: Colors.white,
                          onPressed: () => secondaryButtonCallBack.call(),
                          child: Text(
                            secondaryButtonLabel,
                            style: const TextStyle(
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<bool?> gShowSuccessDialog({
    required BuildContext context,
    required String title,
    String? body,
    required String primaryButtonLabel,
    String? secondaryButtonLabel,
    bool barrierDismissible = true,
  }) async {
    return await showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: circularBorderR,
          ),
          child: Padding(
            padding: allRoundPaddingXXL,
            child: ANSuccess(
              title: title,
              primaryButtonLabel: primaryButtonLabel,
              body: body,
              secondaryButtonLabel: secondaryButtonLabel,
              applyMinAxisSize: true,
            ),
          ),
        );
      },
    );
  }

  static Future<bool?> gShowUpgradeDialog(
      {required BuildContext context}) async {
    return gShowConfirmationDialog(
      context: context,
      message: S.of(context).kUnsupportedVersion,
      content: S.of(context).kUpgradeRequiredMessage,
      type: DialogType.upgrade,
      barrierDismissible: false,
    );
  }
}
