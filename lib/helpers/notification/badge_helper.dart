import 'dart:convert';

import 'package:ajmal_now_doctor/common/domain/notification/notification.dart';
import 'package:ajmal_now_doctor/constants/tags.dart';
import 'package:ajmal_now_doctor/helpers/notification/notification_database_helper.dart';
import 'package:flutter_new_badger/flutter_new_badger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// [ANBadgeHelper]
/// A helper class that takes care of the app badge showing
/// the number of unseen notifications.
class ANBadgeHelper {
  /// [updateBadge]
  /// Static method that updates the app badge based on the passed
  /// notifications, if the notifications is not passed, the method
  /// opens the notifications database and checks for unseen notifications
  /// [notifications] list of notifications to use to display the badge
  static Future<void> updateBadge(
      {List<ANNotificationModel>? notifications}) async {
    // Check if the platform supports badges
    // if (!await FlutterAppBadger.isAppBadgeSupported()) {
    //   return;
    // }

    int count = 0;

    // First check if the notifications list is passed
    if (notifications != null) {
      for (ANNotificationModel notification in notifications) {
        if (!notification.isSeen!) count++;
      }
    } else {
      // No notifications list, get the data from the database
      List<Map<String, dynamic>> messages =
          await ANNotificationDBHelper.readAllNotifications();
      // await CocDBHelper.readFromDatabase(
      //     kNtfTableName, kCreateNtfDBCmd, kSelectBtfDBCmd);
      for (Map<String, dynamic> message in messages) {
        Map<String, dynamic> js = json.decode(message[kNtfDataColumnName]);

        ANNotificationModel notification = ANNotificationModel.fromJson(js);
        if (!notification.isSeen!) count++;
      }
    }

    // Update badge with count
    if (count > 0) {
      FlutterNewBadger.setBadge(count);
    } else {
      FlutterNewBadger.removeBadge();
    }

    // Save count to shared preference
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // Store user token in shared preferences
    prefs.setString(Tags.kNotificationCount, count.toString());
  }

  /// [removeBadge]
  /// Helper method that clears the app badge
  static Future<void> removeBadge() async {
    // Check if the platform supports badges
    // if (await FlutterAppBadger.isAppBadgeSupported()) {
      FlutterNewBadger.removeBadge();

      // Save count to shared preference
      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.setString(Tags.kNotificationCount, '0');
    // }
  }

  /// [incrementBadge]
  /// Helper method that increments the badge count
  static Future<void> incrementBadge() async {
    // Check if the platform supports badges
    // if (await FlutterAppBadger.isAppBadgeSupported()) {
      // Get count to shared preference
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? notificationCount = prefs.getString(Tags.kNotificationCount);
      int? count;
      if (notificationCount != null) {
        count = int.tryParse(notificationCount);
      }

      // Increment count
      count = count == null ? 1 : count + 1;

      // Update badge
      FlutterNewBadger.setBadge(count);
    // }
  }
}
