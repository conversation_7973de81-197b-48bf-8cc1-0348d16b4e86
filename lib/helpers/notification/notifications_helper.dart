import 'dart:convert';
import 'dart:io';

import 'package:ajmal_now_doctor/common/domain/notification/notification.dart';
import 'package:ajmal_now_doctor/helpers/notification/badge_helper.dart';
import 'package:ajmal_now_doctor/helpers/notification/notification_database_helper.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mutex/mutex.dart';

/// [_firebaseMessagingBackgroundHandler]
/// Global function that handles background messages
/// Adds the new notification to the notifications database
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Update badge
  ANBadgeHelper.incrementBadge();
  // Initialize firebase
  await Firebase.initializeApp();

  // Create a new notification object and insert it into the database
  // when the app comes to the foreground, the notifications are
  // reinitialized from the database
  final ANNotificationModel notification =
      ANNotificationModel.fromJson(message.data);
  ANNotificationDBHelper.insertNotification(
      {'id': notification.id, 'json': jsonEncode(notification.toJson())});
}

class ANNotificationHelper extends ChangeNotifier {
  final Ref ref;

  ANNotificationHelper({required this.ref});

  static Mutex mutex = Mutex();

  static final List<ANNotificationModel> _notifications = [];

  List<ANNotificationModel> get notifications => _notifications;

  /// Firebase messaging instance
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  /// [init]
  /// Initializes the permissions, listeners and interactive message
  Future<void> init() async {
    bool isAuthorized = true;
    if (Platform.isIOS) {
      // Request notifications permissions on ios
      final NotificationSettings settings =
          await _firebaseMessaging.requestPermission();
      isAuthorized =
          (settings.authorizationStatus == AuthorizationStatus.authorized);
    }

    if (isAuthorized) {
      // set foreground notification presentation
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
              alert: true, badge: true, sound: true);

      initLocalNotifications();

      // Initialize message handlers
      await firebaseCloudMessagingListeners();

      // Run code required to handle interacted messages
      await setupInteractedMessageAndInitialize();
    } else {
      await ANBadgeHelper.removeBadge();
    }
  }

  Future<void> initLocalNotifications() async {
    // Initialize local notifications
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    // initialise the plugin. app_icon needs to be a added as a drawable resource to the Android head project
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings();
    const DarwinInitializationSettings initializationSettingsMacOS =
        DarwinInitializationSettings();
    const InitializationSettings initializationSettings =
        InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
            macOS: initializationSettingsMacOS);
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);

    const AndroidNotificationChannel kChannel = AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // name
      description: 'This channel is used for important notifications.',
      // description
      importance: Importance.max,
    );

    // Create notification channel for Android
    await FlutterLocalNotificationsPlugin()
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(kChannel);
  }

  /// [setupInteractedMessageAndInitialize]
  /// Get any messages which caused the application to open from
  /// a terminated state.
  Future<void> setupInteractedMessageAndInitialize() async {
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      await addNotification(initialMessage);
    }

    FirebaseMessaging.onMessageOpenedApp
        .listen((message) async => await addNotification(message));

    // Read notifications from database
    await initNotifications();
  }

  /// [firebaseCloudMessagingListeners]
  /// Initializes the notifications events callbacks
  Future<void> firebaseCloudMessagingListeners() async {
    FirebaseMessaging.instance.getToken().then((token) {
      debugPrint(token);
    });

    // background notifications handling
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // foreground notifications handling
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      await addNotification(message);

      if (Platform.isAndroid) {
        // If `onMessage` is triggered with a notification, construct our own
        // local notification to show to users using the created channel.
        if (message.notification != null &&
            message.notification?.android != null) {
          FlutterLocalNotificationsPlugin().show(
            message.notification.hashCode,
            message.notification!.title,
            message.notification!.body,
            NotificationDetails(
              android: AndroidNotificationDetails(
                'high_importance_channel', // id
                'High Importance Notifications', // title
                channelDescription:
                    'This channel is used for important notifications.',
                // description
                icon: message.notification!.android!.smallIcon,
              ),
            ),
          );
        }
      }
    });

    Stream<String> fcmStream = _firebaseMessaging.onTokenRefresh;
    fcmStream.listen((token) async {});
  }

  /// [hasNewNotifications]
  /// Checks if any of the notifications was not seen
  bool get hasNewNotifications {
    int found = _notifications.indexWhere(
      (ANNotificationModel n) => n.isSeen == false,
    );
    return found != -1;
  }

  /// [sortNotifications]
  /// Sorts the notifications by date and time
  void sortNotifications() {
    if (_notifications.isNotEmpty && _notifications.length > 1) {
      // Sort the notifications by date
      _notifications.sort((a, b) => b.date!.compareTo(a.date!));
    }
  }

  /// [initNotifications]
  /// Initializes the notifications from the database
  Future<void> initNotifications() async {
    _notifications.clear();

    // Get the data from the database
    List<Map<String, dynamic>> messages =
        await ANNotificationDBHelper.readAllNotifications();

    // Iterate on each row and create a notification object
    for (Map<String, dynamic> message in messages) {
      final ANNotificationModel notification =
          ANNotificationModel.fromJson(jsonDecode(message[kNtfDataColumnName]));
      // if (notification.expiry != null && notification.expiry.isBefore(DateTime.now())) {
      //   // Expired, Remove from database
      //   await UaDBHelper.deleteFromDatabase(
      //     kNtfTableName,
      //     kCreateNtfDBCmd,
      //     kDeleteNtfDBCmd,
      //     notification.id!,
      //   );
      // } else {
      // Add the notification to the list
      _notifications.insert(0, notification);
      // }
    }

    // Sort the notifications
    sortNotifications();

    // Notify listeners
    notifyListeners();

    // Update badge
    await ANBadgeHelper.updateBadge(notifications: _notifications);
  }

  /// [addNotification]
  /// Add a new notification to the model and database
  /// [message] a map containing the message details
  Future<void> addNotification(RemoteMessage message) async {
    await mutex.acquire();

    try {
      // Create notification object
      final ANNotificationModel notification = ANNotificationModel.fromJson(
          message.data,
          nid: message.notification.hashCode);

      // print(notification.id);

      // Look for a notification with the same id
      int found = _notifications
          .indexWhere((ANNotificationModel n) => n.id == notification.id);
      if (found == -1) {
        // Add the notification to the list
        _notifications.insert(0, notification);

        // No onl notification with same id, so Insert the new one into database
        await ANNotificationDBHelper.insertNotification(
            {'id': notification.id, 'json': jsonEncode(notification.toJson())});

        // Sort the notifications
        sortNotifications();

        // Notify listeners
        notifyListeners();

        // Update badge
        await ANBadgeHelper.updateBadge(notifications: _notifications);
      }
    } finally {
      mutex.release();
    }
  }

  Future<void> removeNotification(
      {required ANNotificationModel notification}) async {
    await ANNotificationDBHelper.deleteNotification(notification.id!);

    _notifications.remove(notification);

    notifyListeners();

    if (Platform.isAndroid) {
      // Remove the notification
      if (notification.nid != null) {
        await FlutterLocalNotificationsPlugin().cancel(notification.nid!);
      } else {
        await FlutterLocalNotificationsPlugin().cancelAll();
      }
    }

    // Update badge
    await ANBadgeHelper.removeBadge();
  }

  /// [clearNotifications]
  /// clears all notifications from the model and database
  Future<void> clearNotifications() async {
    // Remove all notifications from database
    await ANNotificationDBHelper.deleteNotificationDatabase();

    _notifications.clear();

    notifyListeners();

    if (Platform.isAndroid) {
      // Remove all notifications
      await FlutterLocalNotificationsPlugin().cancelAll();
    }

    // Update badge
    await ANBadgeHelper.removeBadge();
  }

  /// [updateSeen]
  /// Toggle the isSeen flag of a specific notification
  /// Updates the model and database
  /// [notification] the notification to be updated
  /// [status] new is seen status boolean
  Future<void> updateSeen(ANNotificationModel notification, bool status) async {
    await mutex.acquire();

    try {
      notification = notification.copyWith(isSeen: status);
      int index =
          _notifications.indexWhere((element) => element.id == notification.id);
      _notifications[index] = notification.copyWith(isSeen: status);

      // // Remove old entry
      // await ANNotificationDBHelper.deleteNotification(notification.id!);

      // Add a new entry
      await ANNotificationDBHelper.insertNotification(
          {'id': notification.id, 'json': jsonEncode(notification.toJson())});

      // Sort the notifications
      sortNotifications();

      // Notify listeners
      notifyListeners();

      if (Platform.isAndroid) {
        // Remove the notification
        if (notification.nid != null) {
          await FlutterLocalNotificationsPlugin().cancel(notification.nid!);
        } else {
          await FlutterLocalNotificationsPlugin().cancelAll();
        }
      }

      // Update badge
      await ANBadgeHelper.updateBadge(notifications: _notifications);
    } finally {
      mutex.release();
    }
  }
}

final notificationProvider =
    ChangeNotifierProvider((ref) => ANNotificationHelper(ref: ref));
