import 'package:ajmal_now_doctor/helpers/database_helper.dart';

/// Notifications database
// Table and columns
const String kNtfTableName = 'notifications';
const String kNtfIDColumnName = 'id';
const String kNtfDataColumnName = 'json';

// Commands
const String kCreateNtfDBCmd =
    'CREATE TABLE $kNtfTableName ($kNtfIDColumnName INTEGER PRIMARY KEY AUTOINCREMENT, $kNtfDataColumnName TEXT)';

class ANNotificationDBHelper {
  static final ANDatabaseHelper _dbHelper = ANDatabaseHelper();

  ANNotificationDBHelper();
  // : super(const DatabaseState.data(data: []));

  static Future<void> deleteNotificationDatabase() async {
    // state = const DatabaseState.deleteDatabase();
    try {
      await _dbHelper.deleteDatabase(kNtfTableName, kCreateNtfDBCmd);
      // state = const DatabaseState.data();
    } catch (e) {
      // state = DatabaseState.onError(e.toString());
      throw Error.safeToString(e);
    }
  }

  static Future<void> deleteNotification(int id) async {
    // state = const DatabaseState.deleteData();
    try {
      await _dbHelper.deleteFromDatabase(
          kNtfTableName, kCreateNtfDBCmd, kNtfIDColumnName, id);
      // state = const DatabaseState.data();
    } catch (e) {
      // state = DatabaseState.onError(e.toString());
      throw Error.safeToString(e);
    }
  }

  static Future<void> insertNotification(Map<String, dynamic> values) async {
    // state = const DatabaseState.insertData();
    try {
      await _dbHelper.insertInDatabase(kNtfTableName, kCreateNtfDBCmd, values);
      // state = const DatabaseState.data();
    } catch (e) {
      // state = DatabaseState.onError(e.toString());
      throw Error.safeToString(e);
    }
  }

  static Future<List<Map<String, dynamic>>> readAllNotifications() async {
    // state = const DatabaseState.readAllData();
    try {
      List<Map<String, dynamic>> data =
          await _dbHelper.readAllFromDatabase(kNtfTableName, kCreateNtfDBCmd);
      // state = DatabaseState.data(data: data);
      return data;
    } catch (e) {
      // state = DatabaseState.onError(e.toString());
      throw Error.safeToString(e);
    }
  }

  static Future<List<Map<String, dynamic>>> readNotification(int id) async {
    // state = const DatabaseState.readData();
    try {
      List<Map<String, dynamic>> data = await _dbHelper.readEntryFromDatabase(
          kNtfTableName, kCreateNtfDBCmd, kNtfIDColumnName, id);
      // state = DatabaseState.data(data: data);
      return data;
    } catch (e) {
      // state = DatabaseState.onError(e.toString());
      throw Error.safeToString(e);
    }
  }
}
