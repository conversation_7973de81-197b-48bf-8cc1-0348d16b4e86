import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:rx_shared_preferences/rx_shared_preferences.dart';

class ANSharedPreferencesHelper {
  /// Singleton instance for app
  final _rxPrefs = RxSharedPreferences(
    SharedPreferences.getInstance(),
    kReleaseMode ? null : const RxSharedPreferencesDefaultLogger(),
  );

  Stream observeData<T>(
      {required String key, FutureOr<dynamic> Function(Object?)? decoder}) {
    return _rxPrefs.observe(key, decoder!);
    // return _rxPrefs.getObjectStream(key, decoder);
  }

  /// Stores a data using a key into the shared preferences
  /// Returns success or failure
  Future storeData(
      {required String key,
      required dynamic value,
      required FutureOr<Object?> Function(dynamic) encoder}) async {
    print("Writing $value");
    return await _rxPrefs.write(key, value, encoder);
  }

  /// Remove a key from the shared preferences
  /// Returns success or failure
  Future deleteData({required String key}) async {
    return await _rxPrefs.remove(key);
  }

  /// Gets a key from the shared preferences
  /// Returns success or failure
  Future<Object?> get<T>(
      {required String key, FutureOr<T> Function(Object?)? decoder}) async {
    return _rxPrefs.getObject(key, decoder);
  }

  Future closeStream() async {
    return await _rxPrefs.dispose();
  }
}
