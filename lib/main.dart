import 'dart:async';

import 'package:ajmal_now_doctor/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:stack_trace/stack_trace.dart' as stack_trace;

import 'app.dart';

void main() async {
  await runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    try {
      await dotenv.load(fileName: "lib/.env");
    } catch (e) {
      print("Error loading .env file: $e");
    }

    // print("TOKKKKKKKKEN: ${await FirebaseMessaging.instance.getToken()}");

    runApp(const ProviderScope(child: MyApp()));

    FlutterError.demangleStackTrace = (StackTrace stack) {
      if (stack is stack_trace.Trace) return stack.vmTrace;
      if (stack is stack_trace.Chain) return stack.toTrace().vmTrace;
      return stack;
    };
  }, (error, stack) {
    print(error.toString());
  });
}
