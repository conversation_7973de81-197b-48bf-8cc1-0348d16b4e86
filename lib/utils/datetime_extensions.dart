import 'package:ajmal_now_doctor/utils/string_extensions.dart';

extension Weekday on DateTime {
  String get stringWeekday {
    switch (weekday) {
      case 1:
        return 'monday'.hardcoded;
      case 2:
        return 'tuesday'.hardcoded;
      case 3:
        return 'wednesday'.hardcoded;
      case 4:
        return 'thursday'.hardcoded;
      case 5:
        return 'friday'.hardcoded;
      case 6:
        return 'saturday'.hardcoded;
      default:
        return 'sunday'.hardcoded;
    }
  }
}
