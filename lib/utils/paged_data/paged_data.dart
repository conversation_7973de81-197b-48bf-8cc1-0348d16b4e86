// part 'paged_data.freezed.dart';
//
// part 'paged_data.g.dart';

// @unfreezed
// class ANPagedDataModel<T> with _$ANPagedDataModel<T> {
//   ANPagedDataModel._();
//
//   @JsonSerializable(genericArgumentFactories: true)
//   factory ANPagedDataModel({
//     @JsonKey(defaultValue: []) @Default([]) List<T> list,
//     @Default(1) int currentPage,
//     @Default(-1) int totalPages,
//   }) = _ANPagedDataModel;
//
//   factory ANPagedDataModel.dataFromJson(
//           Map<String, dynamic> json, T Function(Object? json) fromJsonT) =>
//       ANPagedDataModel<T>(
//         list: (json['list'] as List<dynamic>?)?.map(fromJsonT).toList() ?? [],
//         currentPage: json['currentPage'] as int? ?? 1,
//         totalPages: json['totalPages'] as int? ?? -1,
//       );
//
//   Map<String, dynamic> toJson(
//     ANPagedDataModel<T> instance,
//     Object? Function(T value) toJsonT,
//   ) =>
//       <String, dynamic>{
//         'list': instance.list.map(toJsonT).toList(),
//         'currentPage': instance.currentPage,
//         'totalPages': instance.totalPages,
//       };
//
//   bool hasNext() {
//     if (totalPages == -1) return true;
//     return currentPage + 1 <= totalPages;
//   }
//
//   void setData(List<T> data, int totalPages) {
//     this.totalPages = totalPages;
//     currentPage++;
//     list.addAll(data);
//   }
//
//   void resetData() {
//     list.clear();
//     currentPage = 1;
//     totalPages = -1;
//   }
// }

class ANPagedDataModel<T> {
  List<T> list = [];
  int currentPage = 1;
  int totalPages = -1;

  ANPagedDataModel();

  Map<String, dynamic> toJson(
    ANPagedDataModel<T> instance,
    Object? Function(T value) toJsonT,
  ) =>
      <String, dynamic>{
        'list': instance.list.map(toJsonT).toList(),
        'currentPage': instance.currentPage,
        'totalPages': instance.totalPages,
      };

  bool hasNext() {
    if (totalPages == -1) return true;
    return currentPage <= totalPages;
  }

  void setData(List<T> data, int totalPages) {
    this.totalPages = totalPages;
    currentPage++;
    list.addAll(data);
  }

  void resetData() {
    list.clear();
    currentPage = 1;
    totalPages = -1;
  }
}
