/// Time zone utilities for Egypt (UTC+2:00)
/// 
/// Helps ensure consistent time zone handling across the application

// Egypt time zone offset in hours (UTC+2:00 in winter, UTC+3:00 in summer)
const int egyptTimeZoneOffset = 2;

/// Adjusts a DateTime to Egypt time zone (UTC+2:00)
/// 
/// This ensures consistent time display across app and server
DateTime adjustToEgyptTimeZone(DateTime date) {
  // If the date is already in UTC, add the Egypt time zone offset
  if (date.isUtc) {
    return date.add(Duration(hours: egyptTimeZoneOffset));
  }
  
  // For non-UTC dates, we first convert to UTC then add the offset
  // This handles any local time zone differences properly
  final utcDate = date.toUtc();
  return utcDate.add(Duration(hours: egyptTimeZoneOffset));
}

/// Creates a DateTime object in Egypt time zone with the given components
/// 
/// Useful for creating time slots with a specific local time in Egypt
DateTime createEgyptDateTime(
  int year,
  int month,
  int day,
  int hour,
  int minute, {
  int second = 0,
  int millisecond = 0,
  int microsecond = 0,
}) {
  // Create a DateTime with the components directly - no offset needed
  // This represents the actual time as it should appear in Egypt
  return DateTime(year, month, day, hour, minute, second, millisecond, microsecond);
}

/// Formats a DateTime for consistent JSON serialization
/// 
/// Always returns an ISO string in Egypt time zone
String toEgyptTimeIsoString(DateTime date) {
  return date.toIso8601String();
}

/// Converts a DateTime for display in the Egypt time zone
/// 
/// This preserves the hour/minute values as they should be seen in Egypt
DateTime toEgyptTimeDisplay(DateTime date) {
  // Return the time as-is for display
  return date;
} 