// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slots.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANSlotsModel _$ANSlotsModelFromJson(Map<String, dynamic> json) {
  return _ANSlotsModel.fromJson(json);
}

/// @nodoc
mixin _$ANSlotsModel {
  String get id => throw _privateConstructorUsedError;
  DateTime get starts => throw _privateConstructorUsedError;
  DateTime? get ends => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANSlotsModelCopyWith<ANSlotsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANSlotsModelCopyWith<$Res> {
  factory $ANSlotsModelCopyWith(
          ANSlotsModel value, $Res Function(ANSlotsModel) then) =
      _$ANSlotsModelCopyWithImpl<$Res, ANSlotsModel>;
  @useResult
  $Res call({String id, DateTime starts, DateTime? ends});
}

/// @nodoc
class _$ANSlotsModelCopyWithImpl<$Res, $Val extends ANSlotsModel>
    implements $ANSlotsModelCopyWith<$Res> {
  _$ANSlotsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? starts = null,
    Object? ends = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      starts: null == starts
          ? _value.starts
          : starts // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ends: freezed == ends
          ? _value.ends
          : ends // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANSlotsModelCopyWith<$Res>
    implements $ANSlotsModelCopyWith<$Res> {
  factory _$$_ANSlotsModelCopyWith(
          _$_ANSlotsModel value, $Res Function(_$_ANSlotsModel) then) =
      __$$_ANSlotsModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, DateTime starts, DateTime? ends});
}

/// @nodoc
class __$$_ANSlotsModelCopyWithImpl<$Res>
    extends _$ANSlotsModelCopyWithImpl<$Res, _$_ANSlotsModel>
    implements _$$_ANSlotsModelCopyWith<$Res> {
  __$$_ANSlotsModelCopyWithImpl(
      _$_ANSlotsModel _value, $Res Function(_$_ANSlotsModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? starts = null,
    Object? ends = freezed,
  }) {
    return _then(_$_ANSlotsModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      starts: null == starts
          ? _value.starts
          : starts // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ends: freezed == ends
          ? _value.ends
          : ends // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANSlotsModel implements _ANSlotsModel {
  const _$_ANSlotsModel({required this.id, required this.starts, this.ends});

  factory _$_ANSlotsModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANSlotsModelFromJson(json);

  @override
  final String id;
  @override
  final DateTime starts;
  @override
  final DateTime? ends;

  @override
  String toString() {
    return 'ANSlotsModel(id: $id, starts: $starts, ends: $ends)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANSlotsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.starts, starts) || other.starts == starts) &&
            (identical(other.ends, ends) || other.ends == ends));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, starts, ends);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANSlotsModelCopyWith<_$_ANSlotsModel> get copyWith =>
      __$$_ANSlotsModelCopyWithImpl<_$_ANSlotsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANSlotsModelToJson(
      this,
    );
  }
}

abstract class _ANSlotsModel implements ANSlotsModel {
  const factory _ANSlotsModel(
      {required final String id,
      required final DateTime starts,
      final DateTime? ends}) = _$_ANSlotsModel;

  factory _ANSlotsModel.fromJson(Map<String, dynamic> json) =
      _$_ANSlotsModel.fromJson;

  @override
  String get id;
  @override
  DateTime get starts;
  @override
  DateTime? get ends;
  @override
  @JsonKey(ignore: true)
  _$$_ANSlotsModelCopyWith<_$_ANSlotsModel> get copyWith =>
      throw _privateConstructorUsedError;
} 