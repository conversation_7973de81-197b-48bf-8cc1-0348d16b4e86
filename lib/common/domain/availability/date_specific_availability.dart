import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';

class ANDateSpecificAvailabilityModel {
  final String id;
  final DateTime date;
  final String service;
  final String provider;
  final List<ANSlotsModel> slots;

  ANDateSpecificAvailabilityModel({
    required this.id,
    required this.date,
    required this.service,
    required this.provider,
    required this.slots,
  });

  factory ANDateSpecificAvailabilityModel.fromJson(Map<String, dynamic> json) {
    return ANDateSpecificAvailabilityModel(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      service: json['service'] as String,
      provider: json['provider'] as String,
      slots: (json['slots'] as List?)?.map((e) => ANSlotsModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'date': date.toIso8601String(),
    'service': service,
    'provider': provider,
    'slots': slots.map((s) => s.toJson()).toList(),
  };
}

class ANAvailabilityDateInfo {
  final DateTime date;
  final bool hasSlots;
  final bool isAvailable;

  ANAvailabilityDateInfo({
    required this.date,
    required this.hasSlots,
    this.isAvailable = true,
  });

  factory ANAvailabilityDateInfo.fromJson(Map<String, dynamic> json) {
    return ANAvailabilityDateInfo(
      date: DateTime.parse(json['date'] as String),
      hasSlots: json['hasSlots'] as bool? ?? false,
      isAvailable: json['isAvailable'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() => {
    'date': date.toIso8601String(),
    'hasSlots': hasSlots,
    'isAvailable': isAvailable,
  };
} 