import 'package:freezed_annotation/freezed_annotation.dart';

part 'image.freezed.dart';
part 'image.g.dart';

@freezed
class ANImageModel with _$ANImageModel {
  const factory ANImageModel({
    required String id,
    @J<PERSON><PERSON>ey(name: 'filename') required String fileName,
    required String url,
  }) = _ANImageModel;

  factory ANImageModel.fromJson(Map<String, Object?> json) =>
      _$ANImageModelFromJson(json);
}

/*
{
    "id": "63811bc8d8b4539830b6f370",
    "filename": "Logo.png",
    "mimeType": "image/png",
    "filesize": 129629,
    "width": 1352,
    "height": 1200,
    "createdAt": "2022-11-25T19:47:20.793Z",
    "updatedAt": "2022-11-25T19:47:20.793Z",
    "url": "https://ajmalnow.com/service/Logo.png"
}
*/
