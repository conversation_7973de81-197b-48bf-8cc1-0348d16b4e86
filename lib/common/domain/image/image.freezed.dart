// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'image.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANImageModel _$ANImageModelFromJson(Map<String, dynamic> json) {
  return _ANImageModel.fromJson(json);
}

/// @nodoc
mixin _$ANImageModel {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'filename')
  String get fileName => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANImageModelCopyWith<ANImageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANImageModelCopyWith<$Res> {
  factory $ANImageModelCopyWith(
          ANImageModel value, $Res Function(ANImageModel) then) =
      _$ANImageModelCopyWithImpl<$Res, ANImageModel>;
  @useResult
  $Res call(
      {String id, @JsonKey(name: 'filename') String fileName, String url});
}

/// @nodoc
class _$ANImageModelCopyWithImpl<$Res, $Val extends ANImageModel>
    implements $ANImageModelCopyWith<$Res> {
  _$ANImageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? url = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANImageModelCopyWith<$Res>
    implements $ANImageModelCopyWith<$Res> {
  factory _$$_ANImageModelCopyWith(
          _$_ANImageModel value, $Res Function(_$_ANImageModel) then) =
      __$$_ANImageModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, @JsonKey(name: 'filename') String fileName, String url});
}

/// @nodoc
class __$$_ANImageModelCopyWithImpl<$Res>
    extends _$ANImageModelCopyWithImpl<$Res, _$_ANImageModel>
    implements _$$_ANImageModelCopyWith<$Res> {
  __$$_ANImageModelCopyWithImpl(
      _$_ANImageModel _value, $Res Function(_$_ANImageModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fileName = null,
    Object? url = null,
  }) {
    return _then(_$_ANImageModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANImageModel implements _ANImageModel {
  const _$_ANImageModel(
      {required this.id,
      @JsonKey(name: 'filename') required this.fileName,
      required this.url});

  factory _$_ANImageModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANImageModelFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'filename')
  final String fileName;
  @override
  final String url;

  @override
  String toString() {
    return 'ANImageModel(id: $id, fileName: $fileName, url: $url)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANImageModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, fileName, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANImageModelCopyWith<_$_ANImageModel> get copyWith =>
      __$$_ANImageModelCopyWithImpl<_$_ANImageModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANImageModelToJson(
      this,
    );
  }
}

abstract class _ANImageModel implements ANImageModel {
  const factory _ANImageModel(
      {required final String id,
      @JsonKey(name: 'filename') required final String fileName,
      required final String url}) = _$_ANImageModel;

  factory _ANImageModel.fromJson(Map<String, dynamic> json) =
      _$_ANImageModel.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'filename')
  String get fileName;
  @override
  String get url;
  @override
  @JsonKey(ignore: true)
  _$$_ANImageModelCopyWith<_$_ANImageModel> get copyWith =>
      throw _privateConstructorUsedError;
}
