// New models to match the updated backend CityDistricts structure

class CityDistrict {
  final String id;
  final String city; // Now just a string: 'cairo', 'giza', 'alexandria'
  final DistrictName districtName;
  final String districtCode;
  final Coordinates? coordinates;
  final String? address;
  final bool isActive;
  final int displayOrder;

  CityDistrict({
    required this.id,
    required this.city,
    required this.districtName,
    required this.districtCode,
    this.coordinates,
    this.address,
    required this.isActive,
    required this.displayOrder,
  });

  factory CityDistrict.fromJson(Map<String, dynamic> json) {
    return CityDistrict(
      id: json['id'] ?? '',
      city: json['city'] ?? '',
      districtName: DistrictName.fromJson(json['districtName'] ?? {}),
      districtCode: json['districtCode'] ?? '',
      coordinates: json['coordinates'] != null 
          ? Coordinates.fromJson(json['coordinates']) 
          : null,
      address: json['address'],
      isActive: json['isActive'] ?? true,
      displayOrder: json['displayOrder'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'city': city,
      'districtName': districtName.toJson(),
      'districtCode': districtCode,
      'coordinates': coordinates?.toJson(),
      'address': address,
      'isActive': isActive,
      'displayOrder': displayOrder,
    };
  }

  // Helper method to get the city display name
  String get cityDisplayName {
    switch (city.toLowerCase()) {
      case 'cairo':
        return 'Cairo';
      case 'giza':
        return 'Giza';
      case 'alexandria':
        return 'Alexandria';
      default:
        return city.substring(0, 1).toUpperCase() + city.substring(1);
    }
  }

  // Helper method to get the city display name in Arabic
  String get cityDisplayNameAr {
    switch (city.toLowerCase()) {
      case 'cairo':
        return 'القاهرة';
      case 'giza':
        return 'الجيزة';
      case 'alexandria':
        return 'الإسكندرية';
      default:
        return city;
    }
  }
}

class DistrictName {
  final String en;
  final String ar;

  DistrictName({
    required this.en,
    required this.ar,
  });

  factory DistrictName.fromJson(Map<String, dynamic> json) {
    return DistrictName(
      en: json['en'] ?? '',
      ar: json['ar'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'en': en,
      'ar': ar,
    };
  }
}

class Coordinates {
  final double latitude;
  final double longitude;

  Coordinates({
    required this.latitude,
    required this.longitude,
  });

  factory Coordinates.fromJson(Map<String, dynamic> json) {
    return Coordinates(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

// Helper class for city information
class CityInfo {
  final String code;
  final String displayName;
  final String displayNameAr;

  CityInfo({
    required this.code,
    required this.displayName,
    required this.displayNameAr,
  });

  static final List<CityInfo> predefinedCities = [
    CityInfo(code: 'cairo', displayName: 'Cairo', displayNameAr: 'القاهرة'),
    CityInfo(code: 'giza', displayName: 'Giza', displayNameAr: 'الجيزة'),
    CityInfo(code: 'alexandria', displayName: 'Alexandria', displayNameAr: 'الإسكندرية'),
  ];

  static CityInfo? getByCode(String code) {
    try {
      return predefinedCities.firstWhere((city) => city.code == code);
    } catch (e) {
      return null;
    }
  }
} 