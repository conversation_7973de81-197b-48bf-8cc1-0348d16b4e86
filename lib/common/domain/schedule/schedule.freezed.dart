// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'schedule.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANScheduleModel _$ANScheduleModelFromJson(Map<String, dynamic> json) {
  return _ANScheduleModel.fromJson(json);
}

/// @nodoc
mixin _$ANScheduleModel {
  String get id => throw _privateConstructorUsedError;
  Weekday get day => throw _privateConstructorUsedError;
  List<ANSlotsModel> get slots => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANScheduleModelCopyWith<ANScheduleModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANScheduleModelCopyWith<$Res> {
  factory $ANScheduleModelCopyWith(
          ANScheduleModel value, $Res Function(ANScheduleModel) then) =
      _$ANScheduleModelCopyWithImpl<$Res, ANScheduleModel>;
  @useResult
  $Res call({String id, Weekday day, List<ANSlotsModel> slots});
}

/// @nodoc
class _$ANScheduleModelCopyWithImpl<$Res, $Val extends ANScheduleModel>
    implements $ANScheduleModelCopyWith<$Res> {
  _$ANScheduleModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? day = null,
    Object? slots = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      day: null == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as Weekday,
      slots: null == slots
          ? _value.slots
          : slots // ignore: cast_nullable_to_non_nullable
              as List<ANSlotsModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANScheduleModelCopyWith<$Res>
    implements $ANScheduleModelCopyWith<$Res> {
  factory _$$_ANScheduleModelCopyWith(
          _$_ANScheduleModel value, $Res Function(_$_ANScheduleModel) then) =
      __$$_ANScheduleModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, Weekday day, List<ANSlotsModel> slots});
}

/// @nodoc
class __$$_ANScheduleModelCopyWithImpl<$Res>
    extends _$ANScheduleModelCopyWithImpl<$Res, _$_ANScheduleModel>
    implements _$$_ANScheduleModelCopyWith<$Res> {
  __$$_ANScheduleModelCopyWithImpl(
      _$_ANScheduleModel _value, $Res Function(_$_ANScheduleModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? day = null,
    Object? slots = null,
  }) {
    return _then(_$_ANScheduleModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      day: null == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as Weekday,
      slots: null == slots
          ? _value._slots
          : slots // ignore: cast_nullable_to_non_nullable
              as List<ANSlotsModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANScheduleModel implements _ANScheduleModel {
  _$_ANScheduleModel(
      {required this.id,
      required this.day,
      required final List<ANSlotsModel> slots})
      : _slots = slots;

  factory _$_ANScheduleModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANScheduleModelFromJson(json);

  @override
  final String id;
  @override
  final Weekday day;
  final List<ANSlotsModel> _slots;
  @override
  List<ANSlotsModel> get slots {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_slots);
  }

  @override
  String toString() {
    return 'ANScheduleModel(id: $id, day: $day, slots: $slots)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANScheduleModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.day, day) || other.day == day) &&
            const DeepCollectionEquality().equals(other._slots, _slots));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, day, const DeepCollectionEquality().hash(_slots));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANScheduleModelCopyWith<_$_ANScheduleModel> get copyWith =>
      __$$_ANScheduleModelCopyWithImpl<_$_ANScheduleModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANScheduleModelToJson(
      this,
    );
  }
}

abstract class _ANScheduleModel implements ANScheduleModel {
  factory _ANScheduleModel(
      {required final String id,
      required final Weekday day,
      required final List<ANSlotsModel> slots}) = _$_ANScheduleModel;

  factory _ANScheduleModel.fromJson(Map<String, dynamic> json) =
      _$_ANScheduleModel.fromJson;

  @override
  String get id;
  @override
  Weekday get day;
  @override
  List<ANSlotsModel> get slots;
  @override
  @JsonKey(ignore: true)
  _$$_ANScheduleModelCopyWith<_$_ANScheduleModel> get copyWith =>
      throw _privateConstructorUsedError;
}
