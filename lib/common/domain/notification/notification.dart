import 'dart:math';

import 'package:ajmal_now_doctor/constants/tags.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification.freezed.dart';

@freezed
@immutable
class ANNotificationModel with _$ANNotificationModel {
  const ANNotificationModel._();

  const factory ANNotificationModel(
      {int? id,
      int? nid,
      required String title,
      required String message,
      required String body,
      // required String dataType,
      // String? data,
      @Default(false) bool? isSeen,
      DateTime? date}) = _ANNotificationModel;

  factory ANNotificationModel.fromJson(Map<String, dynamic> json, {int? nid}) {
    return ANNotificationModel(
      id: json[Tags.kNotificationIdTag] == null
          ? Random().nextInt(10000)
          : int.parse(json[Tags.kNotificationIdTag].toString()),
      nid: nid,
      title: json[Tags.kNotificationTitleTag] as String? ?? '',
      message: json[Tags.kNotificationMessageTag] as String? ?? '',
      body: json[Tags.kNotificationBodyTag] as String? ?? '',
      // dataType: json[kNotificationDataTypeTag] as String,
      // data: json['n_data'] as String? ?? '',
      isSeen: json[Tags.kNotificationSeenTag] ?? false,
      date: json[Tags.kNotificationDateTag] == null
          ? DateTime.now()
          : DateTime.parse(json[Tags.kNotificationDateTag] as String),
    );
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        Tags.kNotificationIdTag: id,
        Tags.kNotificationTitleTag: title,
        Tags.kNotificationMessageTag: message,
        Tags.kNotificationBodyTag: body,
        // kNotificationDataTypeTag: dataType,
        // 'n_data': data,
        Tags.kNotificationSeenTag: isSeen,
        Tags.kNotificationDateTag: date?.toIso8601String(),
      };
}

/// Android Notification channel config
const String kAndroidNotificationChannelId = 'high_importance_channel';
const String kAndroidNotificationChannelName = 'High Importance Notifications';
const String kAndroidNotificationChannelDescription =
    'This channel is used for important notifications.';
