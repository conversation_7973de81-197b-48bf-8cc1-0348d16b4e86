import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/constants/images.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ANSuccess extends StatelessWidget {
  final String title;
  final String? body;
  final bool applyMinAxisSize;
  final String primaryButtonLabel;
  final String? secondaryButtonLabel;

  const ANSuccess(
      {Key? key,
      this.applyMinAxisSize = false,
      required this.title,
      this.body,
      required this.primaryButtonLabel,
      this.secondaryButtonLabel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: applyMinAxisSize ? MainAxisSize.min : MainAxisSize.max,
      children: [
        SizedBox(
          height: 100.0,
          width: 100.0,
          child: Image.asset(Images.successSticker),
        ),
        gapHXXXL,
        gapHXXS,
        Text(
          title,
          style: GoogleFonts.philosopher(textStyle: TextSize.xl.boldStyle),
          textAlign: TextAlign.center,
        ),
        if (body != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              gapHR,
              Text(
                body!,
                style: TextSize.r.mediumStyle
                    .copyWith(color: const Color(0xFF8391A1)),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        gapHXXXL,
        gapHR,
        Center(
          child: ANElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text(
                primaryButtonLabel,
                style: const TextStyle(
                  color: Colors.white,
                ),
              )),
        ),
        if (secondaryButtonLabel != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              gapHR,
              Center(
                child: ANElevatedButton(
                    backgroundColor: Colors.white,
                    onPressed: () => Navigator.pop(context, false),
                    child: Text(secondaryButtonLabel!,
                        style: const TextStyle(color: AppColors.primaryColor))),
              ),
            ],
          ),
      ],
    );
  }
}
