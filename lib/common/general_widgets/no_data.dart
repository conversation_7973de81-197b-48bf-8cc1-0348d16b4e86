import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';

class ANNoData extends StatelessWidget {
  final String? message;
  final Widget? additionalContent;
  const ANNoData({Key? key, this.message, this.additionalContent})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: circularBorderL,
      ),
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Padding(
          padding: allRoundPaddingXL,
          child: Column(
            children: [
              const Icon(
                MaterialCommunityIcons.database_remove_outline,
                size: 50.0,
                color: AppColors.primaryColor,
              ),
              gapHR,
              Text(
                message ?? S.of(context).kNoDataToDisplay,
                style: TextSize.r.boldStyle,
                textAlign: TextAlign.center,
              ),
              if (additionalContent != null)
                Column(
                  children: [
                    gapHL,
                    additionalContent!,
                  ],
                )
            ],
          ),
        ),
      ),
    );
  }
}
