import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';

class ANError extends StatelessWidget {
  final String errorMessage;
  final Future Function()? refreshCallback;

  const ANError({Key? key, required this.errorMessage, this.refreshCallback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: allRoundPaddingXL,
      decoration:
          BoxDecoration(color: Colors.white, borderRadius: circularBorderL),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).kThereIsSomethingWrong,
                  style: TextSize.r.boldStyle,
                ),
                gapHR,
                Text(errorMessage),
              ],
            ),
          ),
          if (refreshCallback != null)
            Row(
              children: [
                gapWXL,
                Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primaryColor,
                  ),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: refreshCallback,
                    icon: const Icon(Icons.refresh),
                    color: Colors.white,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
