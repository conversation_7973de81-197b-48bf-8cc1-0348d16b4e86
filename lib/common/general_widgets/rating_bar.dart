import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class ANRatingBar extends StatelessWidget {
  final double initialRating;
  final bool ignoreGestures;
  final double itemSize;
  final void Function(double)? onRatingUpdate;
  const ANRatingBar(
      {Key? key,
      required this.initialRating,
      this.ignoreGestures = true,
      this.itemSize = 20.0,
      this.onRatingUpdate})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RatingBar.builder(
      ignoreGestures: ignoreGestures,
      initialRating: initialRating,
      minRating: 1,
      direction: Axis.horizontal,
      allowHalfRating: true,
      itemCount: 5,
      itemSize: this.itemSize,
      itemPadding: const EdgeInsets.symmetric(horizontal: 2.0),
      itemBuilder: (context, _) => const Icon(
        Icons.star,
        color: Colors.amber,
        size: 10.0,
      ),
      onRatingUpdate: (rating) {
        onRatingUpdate?.call(rating);
      },
    );
  }
}
