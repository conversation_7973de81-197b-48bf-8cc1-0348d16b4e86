import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/constants/images.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANGuest extends StatelessWidget {
  final bool applyMinAxisSize;

  const ANGuest({Key? key, this.applyMinAxisSize = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: applyMinAxisSize ? MainAxisSize.min : MainAxisSize.max,
      children: [
        SizedBox(
          height: 100.0,
          width: 100.0,
          child: Image.asset(Images.logoImage),
        ),
        gapHXXXL,
        gapHXXS,
        Text(
          S.of(context).kHelloThere,
          style: GoogleFonts.philosopher(textStyle: TextSize.xl.boldStyle),
          textAlign: TextAlign.center,
        ),
        gapHR,
        Text(
          S
              .of(context)
              .kYouAreUsingTheAppAsAGuestPleaseRegisterOrLoginToContinue,
          style:
              TextSize.r.mediumStyle.copyWith(color: const Color(0xFF8391A1)),
          textAlign: TextAlign.center,
        ),
        gapHXXXL,
        gapHR,
        Center(
          child: ANElevatedButton(
            onPressed: () {
              context.pushNamed(AppRoute.loginScreen.name);
            },
            child: Text(
              S.of(context).kLogin,
              style: const TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ),
        gapHR,
        Center(
          child: ANElevatedButton(
              backgroundColor: Colors.white,
              onPressed: () {
                context.pushNamed(AppRoute.registerScreen.name);
              },
              child: Text(S.of(context).kRegister,
                  style: const TextStyle(color: AppColors.primaryColor))),
        ),
      ],
    );
  }
}
