import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ANMainScreenAppbar extends AppBar {
  ANMainScreenAppbar(
      {super.key,
      required Widget title,
      GlobalKey<ScaffoldState>? scaffoldKey,
      bool? centerTitle,
      List<Widget>? actions})
      : super(
          backgroundColor: Colors.transparent,
          elevation: 0.0,
          toolbarHeight: 70.0,
          leading: scaffoldKey != null
              ? GestureDetector(
                  onTap: () {
                    scaffoldKey.currentState?.openDrawer();
                  },
                  child: const Padding(
                    padding: EdgeInsetsDirectional.only(start: 20.0),
                    child: CircleAvatar(
                      radius: 22.5,
                      backgroundColor: AppColors.primaryColor,
                      child: Icon(
                        Icons.person,
                        color: Colors.white,
                      ),
                    ),
                  ),
                )
              : const BackButton(color: AppColors.primaryColor),
          leadingWidth: 65.0,
          title: title,
          centerTitle: centerTitle,
          iconTheme: const IconThemeData(color: Colors.black),
          titleTextStyle:
              GoogleFonts.philosopher(textStyle: TextSize.xl.boldStyle)
                  .copyWith(color: Colors.black),
          actions: actions,
        );
}
