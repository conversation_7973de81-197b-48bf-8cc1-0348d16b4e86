import 'package:ajmal_now_doctor/common/buttons/drawer_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANDrawerList extends ConsumerStatefulWidget {
  const ANDrawerList({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _ANDrawerListState();
}

class _ANDrawerListState extends ConsumerState<ANDrawerList> {
  @override
  Widget build(BuildContext context) {
    // TODO: Watch account provider for changes
    final accountController = ref.watch(authRepositoryProvider);

    List<ANDrawerButton> drawerItems = [
      ANDrawerButton(
        context: context,
        icon: Icons.house_outlined,
        label: S.of(context).kHome,
        initialLocation: '/home',
        // onPressed: () => _onItemTapped(context, 0),
      ),
      ANDrawerButton(
        context: context,
        icon: Icons.account_circle_outlined,
        label: S.of(context).kAccount,
        initialLocation: '/profile',
        // onPressed: () => _onItemTapped(context, 1),
      ),
      ANDrawerButton(
        context: context,
        icon: Icons.calendar_month_outlined,
        label: S.of(context).kCalender,
        initialLocation: '/calender',
        // onPressed: () => _onItemTapped(context, 2),
      ),
      ANDrawerButton(
        context: context,
        icon: Icons.settings,
        label: S.of(context).kSettings,
        initialLocation: '/settings',
        // onPressed: () => _onItemTapped(context, 4),
      ),
      // ANDrawerButton(
      //   context: context,
      //   icon: Icons.contact_support_outlined,
      //   label: S.of(context).kContactSupport,
      //   namedLocation: AppRoute.complaintsScreen.name,
      //   initialLocation: '/complaints',
      //   // onPressed: () => _onItemTapped(context, 5),
      // ),
      // ANDrawerButton(
      //   context: context,
      //   icon: Icons.info_outline,
      //   label: S.of(context).kAbout,
      //   namedLocation: AppRoute.historyScreen.name,
      //   initialLocation: '/about',
      //   // onPressed: () => _onItemTapped(context, 6),
      // ),
      ANDrawerButton(
        context: context,
        icon: Icons.logout_outlined,
        label: S.of(context).kLogout,
        initialLocation: '/landing',
        onPressed: () async {
          await ref.read(authRepositoryProvider).logout();
        },
      ),
    ];

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          if (accountController.currentUser != null)
            DrawerHeader(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircleAvatar(
                    radius: 22.5,
                    backgroundColor: AppColors.primaryColor,
                    // foregroundImage: NetworkImage(accountController.currentUser!.profileImage),
                    child: Icon(
                      Icons.person,
                      color: Colors.white,
                    ),
                  ),
                  gapHS,
                  Text(
                    '${accountController.currentUser!.firstName} ${accountController.currentUser!.lastName}',
                    style: TextSize.l.regularStyle,
                  ),
                  gapHXXS,
                  Text(
                    accountController.currentUser!.email,
                    style: TextSize.xxs.regularStyle
                        .copyWith(color: const Color(0xFF8C8C8C)),
                  ),
                  Text(
                    accountController.currentUser!.phoneNumber,
                    style: TextSize.xxs.regularStyle
                        .copyWith(color: const Color(0xFF8C8C8C)),
                  ),
                ],
              ),
            ),
          ListView(
            shrinkWrap: true,
            children: drawerItems,
          )
        ],
      ),
    );
  }
}
