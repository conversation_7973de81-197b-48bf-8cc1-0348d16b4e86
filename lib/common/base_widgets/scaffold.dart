import 'package:ajmal_now_doctor/common/base_widgets/bottom_navbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/drawer_list.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/statistics/presentation/home_screen.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANScaffoldOptions {
  final bool disableDefaultPadding;
  final bool disableDefaultPaddingHorizontally;
  final bool disableDefaultPaddingVertically;
  final bool disableSafeArea;
  final bool disableAppbar;
  final bool disableDrawer;
  final bool disableBottomNavBar;
  final bool disableWillPopScope;

  const ANScaffoldOptions({
    this.disableDefaultPadding = false,
    this.disableDefaultPaddingHorizontally = false,
    this.disableDefaultPaddingVertically = false,
    this.disableSafeArea = false,
    this.disableAppbar = true,
    this.disableDrawer = false,
    this.disableBottomNavBar = true,
    this.disableWillPopScope = true,
  });
}

class ANScaffold extends ConsumerStatefulWidget {
  final AppBar? appbar;
  final Widget body;
  final Widget? floatingActionButton;
  final String? image;
  final ANScaffoldOptions scaffoldOptions;
  final GlobalKey<ScaffoldState>? scaffoldKey;

  const ANScaffold(
      {Key? key,
      this.appbar,
      required this.body,
      this.floatingActionButton,
      this.image,
      this.scaffoldKey,
      this.scaffoldOptions = const ANScaffoldOptions()})
      : super(key: key);

  @override
  ConsumerState createState() => _ANScaffoldState();
}

class _ANScaffoldState extends ConsumerState<ANScaffold> {
  @override
  Widget build(BuildContext context) {
    final scaffoldController = ref.watch(scaffoldProvider);

    return WillPopScope(
      onWillPop: () async {
        scaffoldController.setState(false);
        if (widget.scaffoldOptions.disableWillPopScope) {
          return true;
        } else {
          return await ANDialogHelper.gShowConfirmationDialog(
                  context: context, message: S.of(context).kYouWillExitTheApp)
              .then((value) => value ?? false);
        }
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              image: widget.image != null
                  ? DecorationImage(
                      image: AssetImage(widget.image!), fit: BoxFit.cover)
                  : null,
            ),
            child: Scaffold(
              key: widget.scaffoldKey,
              backgroundColor: widget.image != null
                  ? Colors.transparent
                  : AppColors.backgroundColor,
              appBar: widget.scaffoldOptions.disableAppbar
                  ? null
                  : (widget.appbar ?? AppBar()),
              body: widget.scaffoldOptions.disableSafeArea
                  ? (widget.scaffoldOptions.disableDefaultPadding
                      ? widget.body
                      : (widget
                              .scaffoldOptions.disableDefaultPaddingHorizontally
                          ? Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: Paddings.screenPaddingVertically),
                              child: widget.body,
                            )
                          : (widget.scaffoldOptions
                                  .disableDefaultPaddingVertically
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal:
                                          Paddings.screenPaddingHorizontally),
                                  child: widget.body,
                                )
                              : Padding(
                                  padding: screenPadding,
                                  child: widget.body,
                                ))))
                  : SafeArea(
                      child: widget.scaffoldOptions.disableDefaultPadding
                          ? widget.body
                          : (widget.scaffoldOptions
                                  .disableDefaultPaddingHorizontally
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical:
                                          Paddings.screenPaddingVertically),
                                  child: widget.body,
                                )
                              : (widget.scaffoldOptions
                                      .disableDefaultPaddingVertically
                                  ? Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: Paddings
                                              .screenPaddingHorizontally),
                                      child: widget.body,
                                    )
                                  : Padding(
                                      padding: screenPadding,
                                      child: widget.body,
                                    ))),
                    ),
              drawer: widget.scaffoldOptions.disableDrawer
                  ? null
                  : const Drawer(
                      backgroundColor: Colors.white,
                      child: ANDrawerList(),
                    ),
              bottomNavigationBar: widget.scaffoldOptions.disableBottomNavBar
                  ? null
                  : ANBottomNavBar(
                      tabs: [
                        ANNavBarTabItem(
                          initialLocation: '/home',
                          icon: Icons.home_outlined,
                          label: S.of(context).kHome,
                          body: const ANHomeScreen(),
                        ),
                        ANNavBarTabItem(
                          initialLocation: '/services',
                          icon: Icons.medical_services_outlined,
                          label: S.of(context).kHome,
                          body: const ANHomeScreen(),
                        ),
                        ANNavBarTabItem(
                          initialLocation: '/profile',
                          icon: Icons.account_circle_outlined,
                          label: S.of(context).kProfile,
                          body: const ANHomeScreen(),
                        ),
                        ANNavBarTabItem(
                          initialLocation: '/calender',
                          icon: Icons.calendar_month_outlined,
                          label: S.of(context).kCalender,
                          body: const ANHomeScreen(),
                        ),
                        ANNavBarTabItem(
                          initialLocation: '/settings',
                          icon: Icons.settings_outlined,
                          label: S.of(context).kSettings,
                          body: const ANHomeScreen(),
                        ),
                      ],
                    ),
              floatingActionButton: widget.floatingActionButton,
            ),
          ),
          if (scaffoldController.isLoading)
            Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              color: Colors.black.withOpacity(0.2),
              child: const Center(
                  child:
                      CircularProgressIndicator(color: AppColors.primaryColor)),
            )
        ],
      ),
    );
  }
}

class ANScaffoldController extends ChangeNotifier {
  bool isLoading = false;

  setState(value) {
    isLoading = value;
    notifyListeners();
  }
}

final scaffoldProvider = ChangeNotifierProvider<ANScaffoldController>(
    (ref) => ANScaffoldController());
