import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Representation of a tab item in a [ANBottomNavBar]
class ANNavBarTabItem extends BottomNavigationBarItem {
  /// Constructs an [ANNavBarTabItem].
  ANNavBarTabItem(
      {required this.initialLocation,
      required IconData icon,
      String? label,
      required Widget body})
      : super(
            icon: Icon(icon),
            label: label,
            activeIcon: CircleAvatar(
              backgroundColor: AppColors.primaryColor,
              child: Icon(
                icon,
                color: Colors.white,
              ),
            ));

  /// The initial location/path
  final String initialLocation;
}

class ANBottomNavBar extends StatefulWidget {
  final List<ANNavBarTabItem> tabs;

  const ANBottomNavBar({Key? key, required this.tabs}) : super(key: key);

  @override
  State<ANBottomNavBar> createState() => _ANBottomNavBarState();
}

class _ANBottomNavBarState extends State<ANBottomNavBar> {
  int _locationToTabIndex(String location) {
    final index =
        widget.tabs.indexWhere((t) => location.startsWith(t.initialLocation));
    // if index not found (-1), return 0
    return index < 0 ? 0 : index;
  }

  int get _currentIndex => _locationToTabIndex(GoRouter.of(context).state.uri.toString());

  void _onItemTapped(BuildContext context, int tabIndex) {
    // Only navigate if the tab index has changed
    if (tabIndex != _currentIndex) {
      context.go(widget.tabs[tabIndex].initialLocation);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      items: widget.tabs,
      type: BottomNavigationBarType.fixed,
      showSelectedLabels: false,
      showUnselectedLabels: false,
      unselectedItemColor: Colors.black,
      onTap: (index) => _onItemTapped(context, index),
    );
  }
}
