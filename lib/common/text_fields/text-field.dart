import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

class ANTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? hint;
  final String? label;
  final TextInputType textInputType;
  final IconData? icon;
  final Function()? iconAction;
  final String? Function(String?)? validator;
  final Function()? onTap;
  final bool enabled;
  final bool readOnly;
  final bool isPassword;
  final int? maxLines;
  final Function(bool)? onFocusChanged;
  int? maxLength;

  ANTextField({
    super.key,
    this.controller,
    this.hint,
    this.label,
    this.textInputType = TextInputType.text,
    this.icon,
    this.iconAction,
    this.validator,
    this.onTap,
    this.enabled = true,
    this.readOnly = false,
    this.isPassword = false,
    this.maxLines,
    this.maxLength,
    this.onFocusChanged,
  });

  @override
  State<ANTextField> createState() => _ANTextFieldState();
}

class _ANTextFieldState extends State<ANTextField> {
  late final FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    widget.onFocusChanged?.call(_focusNode.hasFocus);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      obscureText: widget.isPassword,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      validator: widget.validator,
      onTap: widget.onTap,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      keyboardType: widget.textInputType,
      maxLines: widget.maxLines,
      cursorColor: AppColors.primaryColor,
      maxLength: widget.maxLength,
      decoration: InputDecoration(
        hintText: widget.hint,
        hintStyle: TextSize.r.mediumStyle,
        labelText: widget.label,
        labelStyle: TextSize.r.mediumStyle
            .copyWith(color: _isFocused ? AppColors.primaryColor : Colors.grey),
        filled: true,
        fillColor: Colors.white,
        suffixIcon: widget.icon != null
            ? (widget.iconAction != null
                ? IconButton(
                    onPressed: widget.iconAction,
                    icon: Icon(
                      widget.icon,
                      color: AppColors.primaryColor,
                    ),
                  )
                : Icon(widget.icon))
            : null,
        border: OutlineInputBorder(
          borderRadius: textFieldBorderRadius,
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: textFieldBorderRadius,
          borderSide: const BorderSide(color: Colors.transparent),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: textFieldBorderRadius,
          borderSide: const BorderSide(color: AppColors.primaryColor),
        ),
      ),
    );
  }
}
