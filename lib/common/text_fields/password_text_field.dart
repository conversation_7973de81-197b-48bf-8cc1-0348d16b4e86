import 'package:ajmal_now_doctor/common/text_fields/text_field.dart';
import 'package:flutter/material.dart';

class ANPasswordTextField extends StatefulWidget {
  final String? hint;
  final String? label;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  const ANPasswordTextField(
      {Key? key, this.label, this.hint, this.validator, this.controller})
      : super(key: key);

  @override
  State<ANPasswordTextField> createState() => _ANPasswordTextFieldState();
}

class _ANPasswordTextFieldState extends State<ANPasswordTextField> {
  bool _isVisible = false;

  @override
  Widget build(BuildContext context) {
    return ANTextFormField(
      validator: widget.validator,
      maxLines: 1,
      controller: widget.controller,
      label: widget.label,
      hint: widget.hint,
      isPassword: !_isVisible,
      icon: _isVisible
          ? Icons.visibility_off_outlined
          : Icons.visibility_outlined,
      iconAction: () {
        setState(() {
          _isVisible = !_isVisible;
        });
      },
    );
  }
}
