import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

class ANTextForm<PERSON>ield extends TextFormField {
  ANTextFormField({
    super.controller,
    super.key,
    String? hint,
    String? label,
    TextInputType textInputType = TextInputType.text,
    IconData? icon,
    Function()? iconAction,
    String? Function(String?)? validator,
    bool enabled = true,
    bool readOnly = false,
    bool isPassword = false,
    int? maxLength,
    int? maxLines,
  }) : super(
            obscureText: isPassword,
            enabled: enabled,
            readOnly: readOnly,
            validator: validator,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            keyboardType: textInputType,
            maxLines: maxLines,
            maxLength: maxLength,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextSize.r.mediumStyle,
              labelText: label,
              filled: true,
              fillColor: Colors.white,
              suffixIcon: icon != null
                  ? (iconAction != null
                      ? IconButton(
                          onPressed: iconAction,
                          icon: Icon(
                            icon,
                            color: AppColors.primaryColor,
                          ))
                      : Icon(icon))
                  : null,
              border: OutlineInputBorder(
                  borderRadius: textFieldBorderRadius,
                  borderSide: const BorderSide(color: Colors.transparent)),
              enabledBorder: OutlineInputBorder(
                  borderRadius: textFieldBorderRadius,
                  borderSide: const BorderSide(color: Colors.transparent)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: textFieldBorderRadius,
                  borderSide: const BorderSide(color: AppColors.primaryColor)),
            ));
}
