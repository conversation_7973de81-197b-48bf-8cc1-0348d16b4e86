import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

class ANDropDownList<T> extends StatelessWidget {
  ANDropDownList({
    super.key,
    this.validator,
    this.value,
    this.hint,
    this.items,
    this.onChanged,
    this.selectedItemBuilder,
    this.dropdownKey,
  });
  String? Function(T?)? validator;
  Key? dropdownKey;
  T? value;
  Widget? hint;
  List<DropdownMenuItem<T>>? items;
  void Function(T?)? onChanged;
  List<Widget> Function(BuildContext)? selectedItemBuilder;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField2<T>(
      key: dropdownKey,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: AppColors.primaryColor,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: Colors.red,
          ),
        ),
        contentPadding: const EdgeInsets.all(1),
      ),

      value: value,
      validator: validator,
      buttonHeight: 60,
      hint: hint,

      buttonDecoration: BoxDecoration(
        borderRadius: buttonBorderRadius,
        color: Colors.white,
      ),
      buttonPadding: const EdgeInsets.symmetric(horizontal: 10.0),
      buttonWidth: MediaQuery.of(context).size.width,
      buttonElevation: 0,
      dropdownDecoration: BoxDecoration(
        borderRadius: buttonBorderRadius,
      ),
      dropdownMaxHeight: 300,
      selectedItemHighlightColor: AppColors.primaryColor,
      // itemPadding: EdgeInsets.zero,
      dropdownPadding: EdgeInsets.zero,
      selectedItemBuilder: selectedItemBuilder,
      items: items,
      onChanged: onChanged,
    );
  }
}
