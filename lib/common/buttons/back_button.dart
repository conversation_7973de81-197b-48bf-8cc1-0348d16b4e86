import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'elevated_button.dart';

class ANBackButton extends ConsumerWidget {
  const ANBackButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ANElevatedButton(
      applyFixedSize: true,
      fixedSize: const Size(40.0, 40.0),
      onPressed: () => context.pop(),
      backgroundColor: Colors.white,
      child: Icon(
        ref.watch(languageProvider).isRTL
            ? Icons.arrow_forward_ios
            : Icons.arrow_back_ios_new,
        color: AppColors.primaryColor,
      ),
    );
  }
}
