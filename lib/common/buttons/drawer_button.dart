import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class ANDrawerButton extends TextButton {
  /// The initial location/path
  final String initialLocation;

  ANDrawerButton({
    super.key,
    required BuildContext context,
    Color? backgroundColor,
    required IconData icon,
    Color? color,
    required String label,
    required this.initialLocation,
    String? namedLocation,
    void Function()? onPressed,
  }) : super(
          onPressed: () {
            onPressed?.call();
            if (!GoRouter.of(context)
                .routerDelegate.currentConfiguration.fullPath
                .startsWith(initialLocation)) {
              if (namedLocation != null) {
                Navigator.pop(context);
                context.pushNamed(namedLocation);
              } else {
                context.go(initialLocation);
              }
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: Paddings.xxl),
            child: Row(
              children: [
                SizedBox(
                  height: 24.0,
                  width: 24.0,
                  child: Icon(
                    icon,
                    color: GoRouter.of(context)
                        .routerDelegate.currentConfiguration.fullPath
                        .startsWith(initialLocation)
                        ? color ?? Colors.white
                        : Colors.black,
                  ),
                ),
                gapWXXXL,
                Text(
                  label,
                  style: TextSize.xs.mediumStyle.copyWith(
                      color: GoRouter.of(context)
                          .routerDelegate.currentConfiguration.fullPath
                          .startsWith(initialLocation)
                          ? color ?? Colors.white
                          : Colors.black),
                )
              ],
            ),
          ),
          style: TextButton.styleFrom(
              fixedSize: const Size.fromHeight(55.0),
              backgroundColor:
                  GoRouter.of(context).routerDelegate.currentConfiguration.fullPath.startsWith(initialLocation)
                      ? backgroundColor ?? AppColors.primaryColor
                      : Colors.transparent),
        );
}
