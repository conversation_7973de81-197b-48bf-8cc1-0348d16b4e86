import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

import 'elevated_button.dart';

class ANLoadingElevatedButton extends StatefulWidget {
  final Widget label;
  final Future<void> Function()? onPressed;
  final Decoration? decoration;
  final double? width;

  const ANLoadingElevatedButton(
      {Key? key,
      required this.label,
      required this.onPressed,
      this.decoration,
      this.width})
      : super(key: key);

  @override
  _ANLoadingElevatedButtonState createState() =>
      _ANLoadingElevatedButtonState();
}

class _ANLoadingElevatedButtonState extends State<ANLoadingElevatedButton> {
  bool _isPressed = false;

  double? _elevation;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      height: _isPressed ? 47.0 : Dimensions.buttonHeight,
      width: _isPressed ? 50.0 : Dimensions.buttonWidth,
      duration: const Duration(milliseconds: 400),
      decoration: widget.decoration,
      onEnd: () {
        if (_isPressed) {
          setState(() {
            _elevation = 0.0;
          });
        }
      },
      child: ANElevatedButton(
        backgroundColor: widget.decoration != null ? Colors.transparent : null,
        // applyFixedSize: !_isPressed,
        // fixedSize: Size(_isPressed ? 50.0 : 260.0, Dimensions.buttonHeight),
        padding: _isPressed ? EdgeInsets.zero : null,
        elevation: widget.decoration != null ? 0.0 : _elevation,
        onPressed: widget.onPressed == null
            ? null
            : () {
                setState(() {
                  _isPressed = true;
                });
                widget.onPressed!
                    .call()
                    .whenComplete(() => setState(() => _isPressed = false));

                // Future.delayed(const Duration(seconds: 3), () {
                //   setState(() {
                //     _isPressed = false;
                //     _elevation = null;
                //   });
                // });
              },
        child: _isPressed
            ? const SizedBox(
                height: 35.0,
                width: 35.0,
                child: CircularProgressIndicator(color: Colors.white),
              )
            : DefaultTextStyle(
                style: const TextStyle(
                  color: Colors.white,
                ),
                child: widget.label),
      ),
    );
  }
}
