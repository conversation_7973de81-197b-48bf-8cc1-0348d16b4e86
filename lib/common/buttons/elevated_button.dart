import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

class ANElevatedButton extends ElevatedButton {
  ANElevatedButton(
      {super.key,
      Color? backgroundColor,
      Color? textColor,
      OutlinedBorder? shape,
      double? elevation,
      Size? fixedSize,
      bool applyFixedSize = true,
      EdgeInsetsGeometry? padding,
      required super.onPressed,
      required super.child})
      : super(
            style: ElevatedButton.styleFrom(
          elevation: elevation,
          padding: padding ?? allRoundPaddingL,
          backgroundColor: backgroundColor ?? AppColors.primaryColor,
          shape:
              shape ?? RoundedRectangleBorder(borderRadius: buttonBorderRadius),
          textStyle: TextSize.r.semiBoldStyle
              .copyWith(color: textColor ?? Colors.white),
          maximumSize: elevatedButtonFixedSize,
          minimumSize: const Size(50.0, Dimensions.buttonHeight),
          fixedSize:
              applyFixedSize ? fixedSize ?? elevatedButtonFixedSize : null,
        ));
}
