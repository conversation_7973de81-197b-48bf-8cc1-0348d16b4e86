import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:ajmal_now_doctor/utils/http_error_message.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANHttpServiceProvidersTypesRepository {
  final Ref ref;

  ANHttpServiceProvidersTypesRepository({required this.ref});

  Future<List<ANServiceTypesModel>> getServiceProvidersTypes() async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'provider_types',
            //     TODO: either handle pagination or set the limit to 0
          );
      if (response.statusCode == 200 && response.data != null) {
        return (response.data!['docs'] as List)
            .map((e) => ANServiceTypesModel.fromJson(e))
            .toList();
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }
}

final httpServiceProvidersTypesProvider =
    Provider<ANHttpServiceProvidersTypesRepository>(
        (ref) => ANHttpServiceProvidersTypesRepository(ref: ref));
