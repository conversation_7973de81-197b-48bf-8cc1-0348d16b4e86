import 'package:ajmal_now_doctor/features/statistics/data/service_providers_types/http_service_providers_types_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANServiceProvidersTypesRepository extends ChangeNotifier {
  final Ref ref;

  ANServiceProvidersTypesRepository({required this.ref});

  ANServiceTypesModel? _selectedServiceProviderType;

  Future<List<ANServiceTypesModel>> getServiceProvidersTypes() async {
    try {
      final List<ANServiceTypesModel> data = await ref
          .read(httpServiceProvidersTypesProvider)
          .getServiceProvidersTypes();
      selectServiceProviderType = data.first;
      return data;
    } catch (e) {
      rethrow;
    }
  }

  ANServiceTypesModel? get selectedServiceProviderType =>
      _selectedServiceProviderType;

  set selectServiceProviderType(ANServiceTypesModel? value) {
    _selectedServiceProviderType = value;
    notifyListeners();
  }

  void resetData() {
    _selectedServiceProviderType = null;
    notifyListeners();
  }
}

final serviceProvidersTypesProvider =
    ChangeNotifierProvider<ANServiceProvidersTypesRepository>(
        (ref) => ANServiceProvidersTypesRepository(ref: ref));

final serviceProvidersTypesDataProvider = FutureProvider((ref) async =>
    ref.read(serviceProvidersTypesProvider).getServiceProvidersTypes());
