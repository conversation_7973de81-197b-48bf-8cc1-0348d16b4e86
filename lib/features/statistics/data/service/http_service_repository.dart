import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/statistics/statistics.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:ajmal_now_doctor/utils/http_error_message.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANHttpServicesRepository {
  final Ref ref;

  ANHttpServicesRepository({required this.ref});

  Future<List<ANServiceModel>> getServiceByOwnerId() async {
    try {
      final ANResponseModel response = await ref
          .read(networkProvider)
          .getData(path: 'services', queryParam: {
        'where[owners.user][equals]':
            ref.read(authRepositoryProvider).currentUser!.id,
      });
      if (response.statusCode == 200 && response.data != null) {
        return (response.data!['docs'] as List)
            .map((e) => ANServiceModel.fromJson(e))
            .toList();
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<ANServiceProviderStatisticsModel> getServiceStatisticsById(
      {required String serviceId}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'services/$serviceId/stats',
          );
      if (response.statusCode == 200 && response.data != null) {
        return ANServiceProviderStatisticsModel.fromJson(response.data!);
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<List<ANSlotsModel>> getServiceTimeSlots(
      {required String serviceId, required DateTime date}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
          path: 'services/$serviceId/slots',
          queryParam: {'date': date.toIso8601String()});
      if (response.statusCode == 200 && response.data != null) {
        return (response.data!['slots'] as List)
            .map((e) => ANSlotsModel.fromJson(e))
            .toList();
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }
}

final httpServicesProvider = Provider<ANHttpServicesRepository>(
    (ref) => ANHttpServicesRepository(ref: ref));
