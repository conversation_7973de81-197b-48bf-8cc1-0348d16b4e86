import 'dart:convert';

import 'package:ajmal_now_doctor/features/statistics/data/service/http_service_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/statistics/statistics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANServicesRepository extends ChangeNotifier {
  final Ref ref;

  ANServicesRepository({required this.ref});

  List<ANServiceModel> services = [];
  ANServiceModel? selectedService;

  Future<List<ANServiceModel>> getServiceByOwnerId() async {
    try {
      if (services.isEmpty) {
        final List<ANServiceModel> data =
            await ref.read(httpServicesProvider).getServiceByOwnerId();
        if (data.isNotEmpty) {
          services.addAll(data);
        }
      }
      return services;
    } catch (e) {
      rethrow;
    }
  }

  void selectService(ANServiceModel serviceProvider) {
    selectedService = serviceProvider;
    notifyListeners();
  }

  Future<ANServiceProviderStatisticsModel> getServiceStatisticsById() async {
    try {
      return await ref
          .read(httpServicesProvider)
          .getServiceStatisticsById(serviceId: selectedService!.id);
    } catch (e) {
      rethrow;
    }
  }

  Future<List> getServiceTimeSlots(
      {required String serviceId, required DateTime date}) async {
    try {
      return await ref
          .read(httpServicesProvider)
          .getServiceTimeSlots(serviceId: serviceId, date: date);
    } catch (e) {
      rethrow;
    }
  }

  void resetData() {
    services.clear();
    selectedService = null;
    notifyListeners();
  }
}

final servicesProvider =
    ChangeNotifierProvider.autoDispose<ANServicesRepository>(
  (ref) => ANServicesRepository(ref: ref),
);

final servicesDataProvider = FutureProvider.autoDispose<List<ANServiceModel>>(
    (ref) async => ref.read(servicesProvider).getServiceByOwnerId());

final serviceProviderStatisticsByIdProvider = FutureProvider.autoDispose(
    (ref) async => ref.read(servicesProvider).getServiceStatisticsById());

final serviceTimeSlotsProvider =
    FutureProvider.family.autoDispose((ref, String data) async {
  var decodeData = jsonDecode(data);
  return ref.read(servicesProvider).getServiceTimeSlots(
      serviceId: decodeData['serviceId'],
      date: DateTime.parse(decodeData['date']));
});
