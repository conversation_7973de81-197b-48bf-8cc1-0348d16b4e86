import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'service_gallery.freezed.dart';
part 'service_gallery.g.dart';

@freezed
class ANServiceGalleryModel with _$ANServiceGalleryModel {
  const factory ANServiceGalleryModel({
    required String id,
    required ANImageModel logo,
    @<PERSON>son<PERSON>ey(name: "pin_location")
    @Default([31.221269, 30.054482])
    List<double> pinLocation,
  }) = _ANServiceGalleryModel;

  factory ANServiceGalleryModel.fromJson(Map<String, Object?> json) =>
      _$ANServiceGalleryModelFromJson(json);
}
