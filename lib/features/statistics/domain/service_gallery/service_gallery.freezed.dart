// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'service_gallery.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANServiceGalleryModel _$ANServiceGalleryModelFromJson(
    Map<String, dynamic> json) {
  return _ANServiceGalleryModel.fromJson(json);
}

/// @nodoc
mixin _$ANServiceGalleryModel {
  String get id => throw _privateConstructorUsedError;
  ANImageModel get logo => throw _privateConstructorUsedError;
  @JsonKey(name: "pin_location")
  List<double> get pinLocation => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANServiceGalleryModelCopyWith<ANServiceGalleryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServiceGalleryModelCopyWith<$Res> {
  factory $ANServiceGalleryModelCopyWith(ANServiceGalleryModel value,
          $Res Function(ANServiceGalleryModel) then) =
      _$ANServiceGalleryModelCopyWithImpl<$Res, ANServiceGalleryModel>;
  @useResult
  $Res call(
      {String id,
      ANImageModel logo,
      @JsonKey(name: "pin_location") List<double> pinLocation});

  $ANImageModelCopyWith<$Res> get logo;
}

/// @nodoc
class _$ANServiceGalleryModelCopyWithImpl<$Res,
        $Val extends ANServiceGalleryModel>
    implements $ANServiceGalleryModelCopyWith<$Res> {
  _$ANServiceGalleryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? logo = null,
    Object? pinLocation = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      logo: null == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
      pinLocation: null == pinLocation
          ? _value.pinLocation
          : pinLocation // ignore: cast_nullable_to_non_nullable
              as List<double>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get logo {
    return $ANImageModelCopyWith<$Res>(_value.logo, (value) {
      return _then(_value.copyWith(logo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ANServiceGalleryModelCopyWith<$Res>
    implements $ANServiceGalleryModelCopyWith<$Res> {
  factory _$$_ANServiceGalleryModelCopyWith(_$_ANServiceGalleryModel value,
          $Res Function(_$_ANServiceGalleryModel) then) =
      __$$_ANServiceGalleryModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      ANImageModel logo,
      @JsonKey(name: "pin_location") List<double> pinLocation});

  @override
  $ANImageModelCopyWith<$Res> get logo;
}

/// @nodoc
class __$$_ANServiceGalleryModelCopyWithImpl<$Res>
    extends _$ANServiceGalleryModelCopyWithImpl<$Res, _$_ANServiceGalleryModel>
    implements _$$_ANServiceGalleryModelCopyWith<$Res> {
  __$$_ANServiceGalleryModelCopyWithImpl(_$_ANServiceGalleryModel _value,
      $Res Function(_$_ANServiceGalleryModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? logo = null,
    Object? pinLocation = null,
  }) {
    return _then(_$_ANServiceGalleryModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      logo: null == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
      pinLocation: null == pinLocation
          ? _value._pinLocation
          : pinLocation // ignore: cast_nullable_to_non_nullable
              as List<double>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANServiceGalleryModel implements _ANServiceGalleryModel {
  const _$_ANServiceGalleryModel(
      {required this.id,
      required this.logo,
      @JsonKey(name: "pin_location")
          final List<double> pinLocation = const [31.221269, 30.054482]})
      : _pinLocation = pinLocation;

  factory _$_ANServiceGalleryModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANServiceGalleryModelFromJson(json);

  @override
  final String id;
  @override
  final ANImageModel logo;
  final List<double> _pinLocation;
  @override
  @JsonKey(name: "pin_location")
  List<double> get pinLocation {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pinLocation);
  }

  @override
  String toString() {
    return 'ANServiceGalleryModel(id: $id, logo: $logo, pinLocation: $pinLocation)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANServiceGalleryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            const DeepCollectionEquality()
                .equals(other._pinLocation, _pinLocation));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, logo, const DeepCollectionEquality().hash(_pinLocation));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANServiceGalleryModelCopyWith<_$_ANServiceGalleryModel> get copyWith =>
      __$$_ANServiceGalleryModelCopyWithImpl<_$_ANServiceGalleryModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANServiceGalleryModelToJson(
      this,
    );
  }
}

abstract class _ANServiceGalleryModel implements ANServiceGalleryModel {
  const factory _ANServiceGalleryModel(
          {required final String id,
          required final ANImageModel logo,
          @JsonKey(name: "pin_location") final List<double> pinLocation}) =
      _$_ANServiceGalleryModel;

  factory _ANServiceGalleryModel.fromJson(Map<String, dynamic> json) =
      _$_ANServiceGalleryModel.fromJson;

  @override
  String get id;
  @override
  ANImageModel get logo;
  @override
  @JsonKey(name: "pin_location")
  List<double> get pinLocation;
  @override
  @JsonKey(ignore: true)
  _$$_ANServiceGalleryModelCopyWith<_$_ANServiceGalleryModel> get copyWith =>
      throw _privateConstructorUsedError;
}
