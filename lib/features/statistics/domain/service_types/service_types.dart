import 'package:freezed_annotation/freezed_annotation.dart';

part 'service_types.freezed.dart';
part 'service_types.g.dart';

@freezed
class ANServiceTypesModel with _$ANServiceTypesModel {
  const factory ANServiceTypesModel({
    required String id,
    @J<PERSON><PERSON>ey(name: 'main_category') required String mainCategory,
    required String name,
    String? description,
    String? color,
  }) = _ANServiceTypesModel;

  factory ANServiceTypesModel.fromJson(Map<String, Object?> json) =>
      _$ANServiceTypesModelFromJson(json);
      
  static ANServiceTypesModel empty() {
    return const ANServiceTypesModel(
      id: '',
      mainCategory: '',
      name: '',
    );
  }
}
