// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'service_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANServiceTypesModel _$ANServiceTypesModelFromJson(Map<String, dynamic> json) {
  return _ANServiceTypesModel.fromJson(json);
}

/// @nodoc
mixin _$ANServiceTypesModel {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'main_category')
  String get mainCategory => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANServiceTypesModelCopyWith<ANServiceTypesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServiceTypesModelCopyWith<$Res> {
  factory $ANServiceTypesModelCopyWith(
          ANServiceTypesModel value, $Res Function(ANServiceTypesModel) then) =
      _$ANServiceTypesModelCopyWithImpl<$Res, ANServiceTypesModel>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'main_category') String mainCategory,
      String name,
      String? description,
      String? color});
}

/// @nodoc
class _$ANServiceTypesModelCopyWithImpl<$Res, $Val extends ANServiceTypesModel>
    implements $ANServiceTypesModelCopyWith<$Res> {
  _$ANServiceTypesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainCategory = null,
    Object? name = null,
    Object? description = freezed,
    Object? color = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _value.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANServiceTypesModelCopyWith<$Res>
    implements $ANServiceTypesModelCopyWith<$Res> {
  factory _$$_ANServiceTypesModelCopyWith(_$_ANServiceTypesModel value,
          $Res Function(_$_ANServiceTypesModel) then) =
      __$$_ANServiceTypesModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'main_category') String mainCategory,
      String name,
      String? description,
      String? color});
}

/// @nodoc
class __$$_ANServiceTypesModelCopyWithImpl<$Res>
    extends _$ANServiceTypesModelCopyWithImpl<$Res, _$_ANServiceTypesModel>
    implements _$$_ANServiceTypesModelCopyWith<$Res> {
  __$$_ANServiceTypesModelCopyWithImpl(_$_ANServiceTypesModel _value,
      $Res Function(_$_ANServiceTypesModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mainCategory = null,
    Object? name = null,
    Object? description = freezed,
    Object? color = freezed,
  }) {
    return _then(_$_ANServiceTypesModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _value.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANServiceTypesModel implements _ANServiceTypesModel {
  const _$_ANServiceTypesModel(
      {required this.id,
      @JsonKey(name: 'main_category') required this.mainCategory,
      required this.name,
      this.description,
      this.color});

  factory _$_ANServiceTypesModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANServiceTypesModelFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'main_category')
  final String mainCategory;
  @override
  final String name;
  @override
  final String? description;
  @override
  final String? color;

  @override
  String toString() {
    return 'ANServiceTypesModel(id: $id, mainCategory: $mainCategory, name: $name, description: $description, color: $color)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANServiceTypesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mainCategory, mainCategory) ||
                other.mainCategory == mainCategory) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.color, color) || other.color == color));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, mainCategory, name, description, color);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANServiceTypesModelCopyWith<_$_ANServiceTypesModel> get copyWith =>
      __$$_ANServiceTypesModelCopyWithImpl<_$_ANServiceTypesModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANServiceTypesModelToJson(
      this,
    );
  }
}

abstract class _ANServiceTypesModel implements ANServiceTypesModel {
  const factory _ANServiceTypesModel(
      {required final String id,
      @JsonKey(name: 'main_category') required final String mainCategory,
      required final String name,
      final String? description,
      final String? color}) = _$_ANServiceTypesModel;

  factory _ANServiceTypesModel.fromJson(Map<String, dynamic> json) =
      _$_ANServiceTypesModel.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'main_category')
  String get mainCategory;
  @override
  String get name;
  @override
  String? get description;
  @override
  String? get color;
  @override
  @JsonKey(ignore: true)
  _$$_ANServiceTypesModelCopyWith<_$_ANServiceTypesModel> get copyWith =>
      throw _privateConstructorUsedError;
}
