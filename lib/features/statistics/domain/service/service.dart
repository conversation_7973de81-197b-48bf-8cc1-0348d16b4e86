import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/providers/providers.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_location/service_location.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_gallery/service_gallery.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';

// Since we cannot regenerate the freezed code easily, we'll manually implement the class

class ANServiceModel {
  final String id;
  final String name;
  final String? description;
  final String? offers;
  final ANServiceTypesModel type;
  final ServiceLocation location;
  final bool homeVisit;
  final List<ANProviderModel> owners;
  final List<ANServicesModel> services;
  final double rating;
  final ANImageModel? logo;
  final int galleryLimit;
  final List<ANServiceGalleryModel> gallery;
  final List<double>? pinLocation;

  ANServiceModel({
    required this.id,
    required this.name,
    this.description,
    this.offers,
    required this.type,
    required this.location,
    required this.homeVisit,
    required this.owners,
    required this.services,
    this.rating = 5,
    this.logo,
    this.galleryLimit = 10,
    required this.gallery,
    this.pinLocation,
  });

  factory ANServiceModel.fromJson(Map<String, dynamic> json) {
    try {
      return ANServiceModel(
        id: json['id'] as String,
        name: json['name'] as String,
        description: json['description'] as String?,
        offers: json['offers'] as String?,
        type: ANServiceTypesModel.fromJson(json['type'] as Map<String, dynamic>),
        location: ServiceLocation.fromJson(json['location'] as Map<String, dynamic>),
        homeVisit: json['home_visit'] as bool,
        owners: (json['owners'] as List<dynamic>? ?? [])
            .map((e) => ANProviderModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        services: (json['services'] as List<dynamic>? ?? [])
            .map((e) {
              // Add debug logging for service parsing
              try {
                // Process data before using the freezed constructor
                Map<String, dynamic> serviceData = Map<String, dynamic>.from(e as Map<String, dynamic>);
                
                // Ensure dateSpecificAvailability is properly set to empty list if null
                if (!serviceData.containsKey('dateSpecificAvailability')) {
                  serviceData['dateSpecificAvailability'] = [];
                } else if (serviceData['dateSpecificAvailability'] == null) {
                  serviceData['dateSpecificAvailability'] = [];
                }
                
                // Ensure schedule is properly set to empty list if null
                if (!serviceData.containsKey('schedule')) {
                  serviceData['schedule'] = [];
                } else if (serviceData['schedule'] == null) {
                  serviceData['schedule'] = [];
                }
                
                print("Processing service ${serviceData['id']} - dateSpecificAvailability: ${serviceData['dateSpecificAvailability']}");
                
                return ANServicesModel.fromJson(serviceData);
              } catch (serviceError) {
                print("Error parsing service in ANServiceModel: $serviceError");
                print("Service data: $e");
                
                // Return a complete service model with all fields
                return ANServicesModel(
                  id: e['id'] as String? ?? '',
                  title: e['title'] as String? ?? '',
                  description: e['description'] as String? ?? '',
                  price: (e['price'] as num?)?.toDouble() ?? 0.0,
                  minPrice: (e['minPrice'] as num?)?.toDouble(),
                  maxPrice: (e['maxPrice'] as num?)?.toDouble(),
                  discount: (e['discount'] as num?)?.toDouble(),
                  discountActive: e['discountActive'] as bool?,
                  isPackage: e['isPackage'] as bool? ?? false,
                  image: e['image'] != null 
                      ? ANImageModel.fromJson(e['image'] as Map<String, dynamic>)
                      : null,
                  schedule: [], // Add empty schedule list
                );
              }
            })
            .toList(),
        rating: (json['rating'] as num?)?.toDouble() ?? 5.0,
        logo: json['logo'] != null 
            ? ANImageModel.fromJson(json['logo'] as Map<String, dynamic>)
            : null,
        galleryLimit: (json['gallery_limit'] as num?)?.toInt() ?? 10,
        gallery: (json['gallery'] as List<dynamic>? ?? [])
            .map((e) => ANServiceGalleryModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        pinLocation: json['pin_location'] != null
            ? (json['pin_location'] as List<dynamic>)
                .map((e) => (e as num).toDouble())
                .toList()
            : null,
      );
    } catch (e) {
      print('Error parsing ANServiceModel: $e');
      print('JSON: $json');
      
      // Return a minimal model with required fields, with defaults for optional fields
      return ANServiceModel(
        id: json['id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        type: json['type'] != null 
            ? ANServiceTypesModel.fromJson(json['type'] as Map<String, dynamic>)
            : ANServiceTypesModel.empty(),
        location: json['location'] != null
            ? ServiceLocation.fromJson(json['location'] as Map<String, dynamic>)
            : ServiceLocation.empty(),
        homeVisit: json['home_visit'] as bool? ?? false,
        owners: [],
        services: [],
        gallery: [],
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'offers': offers,
      'type': type.toJson(),
      'location': location.toJson(),
      'home_visit': homeVisit,
      'owners': owners.map((e) => e.toJson()).toList(),
      'services': services.map((e) => e.toJson()).toList(),
      'rating': rating,
      'logo': logo?.toJson(),
      'gallery_limit': galleryLimit,
      'gallery': gallery.map((e) => e.toJson()).toList(),
      'pin_location': pinLocation,
    };
  }

  ANServiceModel copyWith({
    String? id,
    String? name,
    String? description,
    String? offers,
    ANServiceTypesModel? type,
    ServiceLocation? location,
    bool? homeVisit,
    List<ANProviderModel>? owners,
    List<ANServicesModel>? services,
    double? rating,
    ANImageModel? logo,
    int? galleryLimit,
    List<ANServiceGalleryModel>? gallery,
    List<double>? pinLocation,
  }) {
    return ANServiceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      offers: offers ?? this.offers,
      type: type ?? this.type,
      location: location ?? this.location,
      homeVisit: homeVisit ?? this.homeVisit,
      owners: owners ?? this.owners,
      services: services ?? this.services,
      rating: rating ?? this.rating,
      logo: logo ?? this.logo,
      galleryLimit: galleryLimit ?? this.galleryLimit,
      gallery: gallery ?? this.gallery,
      pinLocation: pinLocation ?? this.pinLocation,
    );
  }
}
