// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'providers.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANProviderModel _$ANProviderModelFromJson(Map<String, dynamic> json) {
  return _ANProviderModel.fromJson(json);
}

/// @nodoc
mixin _$ANProviderModel {
  String get id => throw _privateConstructorUsedError;
  String get user => throw _privateConstructorUsedError;
  String? get biography => throw _privateConstructorUsedError;
  List<ANExperienceModel> get experience => throw _privateConstructorUsedError;
  List<ANCertificatesModel> get certificates =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANProviderModelCopyWith<ANProviderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANProviderModelCopyWith<$Res> {
  factory $ANProviderModelCopyWith(
          ANProviderModel value, $Res Function(ANProviderModel) then) =
      _$ANProviderModelCopyWithImpl<$Res, ANProviderModel>;
  @useResult
  $Res call(
      {String id,
      String user,
      String? biography,
      List<ANExperienceModel> experience,
      List<ANCertificatesModel> certificates});
}

/// @nodoc
class _$ANProviderModelCopyWithImpl<$Res, $Val extends ANProviderModel>
    implements $ANProviderModelCopyWith<$Res> {
  _$ANProviderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? biography = freezed,
    Object? experience = null,
    Object? certificates = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      biography: freezed == biography
          ? _value.biography
          : biography // ignore: cast_nullable_to_non_nullable
              as String?,
      experience: null == experience
          ? _value.experience
          : experience // ignore: cast_nullable_to_non_nullable
              as List<ANExperienceModel>,
      certificates: null == certificates
          ? _value.certificates
          : certificates // ignore: cast_nullable_to_non_nullable
              as List<ANCertificatesModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANProviderModelCopyWith<$Res>
    implements $ANProviderModelCopyWith<$Res> {
  factory _$$_ANProviderModelCopyWith(
          _$_ANProviderModel value, $Res Function(_$_ANProviderModel) then) =
      __$$_ANProviderModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String user,
      String? biography,
      List<ANExperienceModel> experience,
      List<ANCertificatesModel> certificates});
}

/// @nodoc
class __$$_ANProviderModelCopyWithImpl<$Res>
    extends _$ANProviderModelCopyWithImpl<$Res, _$_ANProviderModel>
    implements _$$_ANProviderModelCopyWith<$Res> {
  __$$_ANProviderModelCopyWithImpl(
      _$_ANProviderModel _value, $Res Function(_$_ANProviderModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? biography = freezed,
    Object? experience = null,
    Object? certificates = null,
  }) {
    return _then(_$_ANProviderModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      biography: freezed == biography
          ? _value.biography
          : biography // ignore: cast_nullable_to_non_nullable
              as String?,
      experience: null == experience
          ? _value._experience
          : experience // ignore: cast_nullable_to_non_nullable
              as List<ANExperienceModel>,
      certificates: null == certificates
          ? _value._certificates
          : certificates // ignore: cast_nullable_to_non_nullable
              as List<ANCertificatesModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANProviderModel implements _ANProviderModel {
  const _$_ANProviderModel(
      {required this.id,
      required this.user,
      this.biography,
      required final List<ANExperienceModel> experience,
      required final List<ANCertificatesModel> certificates})
      : _experience = experience,
        _certificates = certificates;

  factory _$_ANProviderModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANProviderModelFromJson(json);

  @override
  final String id;
  @override
  final String user;
  @override
  final String? biography;
  final List<ANExperienceModel> _experience;
  @override
  List<ANExperienceModel> get experience {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_experience);
  }

  final List<ANCertificatesModel> _certificates;
  @override
  List<ANCertificatesModel> get certificates {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_certificates);
  }

  @override
  String toString() {
    return 'ANProviderModel(id: $id, user: $user, biography: $biography, experience: $experience, certificates: $certificates)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANProviderModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.biography, biography) ||
                other.biography == biography) &&
            const DeepCollectionEquality()
                .equals(other._experience, _experience) &&
            const DeepCollectionEquality()
                .equals(other._certificates, _certificates));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user,
      biography,
      const DeepCollectionEquality().hash(_experience),
      const DeepCollectionEquality().hash(_certificates));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANProviderModelCopyWith<_$_ANProviderModel> get copyWith =>
      __$$_ANProviderModelCopyWithImpl<_$_ANProviderModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANProviderModelToJson(
      this,
    );
  }
}

abstract class _ANProviderModel implements ANProviderModel {
  const factory _ANProviderModel(
          {required final String id,
          required final String user,
          final String? biography,
          required final List<ANExperienceModel> experience,
          required final List<ANCertificatesModel> certificates}) =
      _$_ANProviderModel;

  factory _ANProviderModel.fromJson(Map<String, dynamic> json) =
      _$_ANProviderModel.fromJson;

  @override
  String get id;
  @override
  String get user;
  @override
  String? get biography;
  @override
  List<ANExperienceModel> get experience;
  @override
  List<ANCertificatesModel> get certificates;
  @override
  @JsonKey(ignore: true)
  _$$_ANProviderModelCopyWith<_$_ANProviderModel> get copyWith =>
      throw _privateConstructorUsedError;
}
