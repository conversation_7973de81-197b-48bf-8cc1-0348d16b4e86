import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/certificates/certificates.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/experience/experience.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'providers.freezed.dart';
part 'providers.g.dart';

@freezed
class ANProviderModel with _$ANProviderModel {
  const factory ANProviderModel({
    required String id,
    required String user,
    String? biography,
    required List<ANExperienceModel> experience,
    required List<ANCertificatesModel> certificates,
  }) = _ANProviderModel;

  factory ANProviderModel.fromJson(Map<String, Object?> json) {
    final modifiedJson = Map<String, Object?>.from(json);
    
    // Check if user is a nested object
    if (json['user'] is Map<String, dynamic>) {
      // Extract the ID from the nested user object
      final userMap = json['user'] as Map<String, dynamic>;
      modifiedJson['user'] = userMap['id'] as String;
    }
    
    // Handle certificates array - add IDs if they don't exist
    if (json['certificates'] is List) {
      final certificatesList = json['certificates'] as List;
      modifiedJson['certificates'] = certificatesList.asMap().entries.map((entry) {
        final index = entry.key;
        final cert = entry.value as Map<String, dynamic>;
        if (!cert.containsKey('id')) {
          cert['id'] = 'cert_${DateTime.now().millisecondsSinceEpoch}_$index';
        }
        return cert;
      }).toList();
    }
    
    // Handle experience array - add IDs if they don't exist
    if (json['experience'] is List) {
      final experienceList = json['experience'] as List;
      modifiedJson['experience'] = experienceList.asMap().entries.map((entry) {
        final index = entry.key;
        final exp = entry.value as Map<String, dynamic>;
        if (!exp.containsKey('id')) {
          exp['id'] = 'exp_${DateTime.now().millisecondsSinceEpoch}_$index';
        }
        return exp;
      }).toList();
    }
    
    return _$ANProviderModelFromJson(modifiedJson);
  }
}

// Extension for user details
extension UserDetails on ANProviderModel {
  // These methods would be implemented if we had the full user object,
  // but for now we only have the ID reference
  String get userId => user;
}
