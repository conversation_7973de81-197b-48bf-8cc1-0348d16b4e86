import 'package:freezed_annotation/freezed_annotation.dart';

part 'statistics_object.freezed.dart';
part 'statistics_object.g.dart';

@freezed
class ANStatisticsModel with _$ANStatisticsModel {
  const factory ANStatisticsModel({
    required List completed,
    required List cancelled,
  }) = _ANStatisticsModel;

  factory ANStatisticsModel.fromJson(Map<String, Object?> json) =>
      _$ANStatisticsModelFromJson(json);
}

/*
{lastMonth: {completed: [], cancelled: []}, thisMonth: {completed: [], cancelled: []}, all: {completed: [], cancelled: []}}
*/
