// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'statistics_object.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANStatisticsModel _$ANStatisticsModelFromJson(Map<String, dynamic> json) {
  return _ANStatisticsModel.fromJson(json);
}

/// @nodoc
mixin _$ANStatisticsModel {
  List<dynamic> get completed => throw _privateConstructorUsedError;
  List<dynamic> get cancelled => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANStatisticsModelCopyWith<ANStatisticsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANStatisticsModelCopyWith<$Res> {
  factory $ANStatisticsModelCopyWith(
          ANStatisticsModel value, $Res Function(ANStatisticsModel) then) =
      _$ANStatisticsModelCopyWithImpl<$Res, ANStatisticsModel>;
  @useResult
  $Res call({List<dynamic> completed, List<dynamic> cancelled});
}

/// @nodoc
class _$ANStatisticsModelCopyWithImpl<$Res, $Val extends ANStatisticsModel>
    implements $ANStatisticsModelCopyWith<$Res> {
  _$ANStatisticsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? cancelled = null,
  }) {
    return _then(_value.copyWith(
      completed: null == completed
          ? _value.completed
          : completed // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      cancelled: null == cancelled
          ? _value.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANStatisticsModelCopyWith<$Res>
    implements $ANStatisticsModelCopyWith<$Res> {
  factory _$$_ANStatisticsModelCopyWith(_$_ANStatisticsModel value,
          $Res Function(_$_ANStatisticsModel) then) =
      __$$_ANStatisticsModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<dynamic> completed, List<dynamic> cancelled});
}

/// @nodoc
class __$$_ANStatisticsModelCopyWithImpl<$Res>
    extends _$ANStatisticsModelCopyWithImpl<$Res, _$_ANStatisticsModel>
    implements _$$_ANStatisticsModelCopyWith<$Res> {
  __$$_ANStatisticsModelCopyWithImpl(
      _$_ANStatisticsModel _value, $Res Function(_$_ANStatisticsModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? completed = null,
    Object? cancelled = null,
  }) {
    return _then(_$_ANStatisticsModel(
      completed: null == completed
          ? _value._completed
          : completed // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      cancelled: null == cancelled
          ? _value._cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANStatisticsModel implements _ANStatisticsModel {
  const _$_ANStatisticsModel(
      {required final List<dynamic> completed,
      required final List<dynamic> cancelled})
      : _completed = completed,
        _cancelled = cancelled;

  factory _$_ANStatisticsModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANStatisticsModelFromJson(json);

  final List<dynamic> _completed;
  @override
  List<dynamic> get completed {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_completed);
  }

  final List<dynamic> _cancelled;
  @override
  List<dynamic> get cancelled {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cancelled);
  }

  @override
  String toString() {
    return 'ANStatisticsModel(completed: $completed, cancelled: $cancelled)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANStatisticsModel &&
            const DeepCollectionEquality()
                .equals(other._completed, _completed) &&
            const DeepCollectionEquality()
                .equals(other._cancelled, _cancelled));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_completed),
      const DeepCollectionEquality().hash(_cancelled));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANStatisticsModelCopyWith<_$_ANStatisticsModel> get copyWith =>
      __$$_ANStatisticsModelCopyWithImpl<_$_ANStatisticsModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANStatisticsModelToJson(
      this,
    );
  }
}

abstract class _ANStatisticsModel implements ANStatisticsModel {
  const factory _ANStatisticsModel(
      {required final List<dynamic> completed,
      required final List<dynamic> cancelled}) = _$_ANStatisticsModel;

  factory _ANStatisticsModel.fromJson(Map<String, dynamic> json) =
      _$_ANStatisticsModel.fromJson;

  @override
  List<dynamic> get completed;
  @override
  List<dynamic> get cancelled;
  @override
  @JsonKey(ignore: true)
  _$$_ANStatisticsModelCopyWith<_$_ANStatisticsModel> get copyWith =>
      throw _privateConstructorUsedError;
}
