import 'package:ajmal_now_doctor/features/statistics/domain/statistics/statistics_object.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'statistics.freezed.dart';
part 'statistics.g.dart';

@freezed
class ANServiceProviderStatisticsModel with _$ANServiceProviderStatisticsModel {
  const factory ANServiceProviderStatisticsModel({
    required ANStatisticsModel lastMonth,
    required ANStatisticsModel thisMonth,
    required ANStatisticsModel all,
  }) = _ANServiceProviderStatisticsModel;

  factory ANServiceProviderStatisticsModel.fromJson(
          Map<String, Object?> json) =>
      _$ANServiceProviderStatisticsModelFromJson(json);
}

/*
{lastMonth: {completed: [], cancelled: []}, thisMonth: {completed: [], cancelled: []}, all: {completed: [], cancelled: []}}
*/
