import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'services.freezed.dart';
part 'services.g.dart';

@freezed
class ANServicesModel with _$ANServicesModel {
  const factory ANServicesModel({
    required String id,
    required String title,
    required String description,
    required double price,
    double? minPrice,
    double? maxPrice,
    double? discount,
    bool? discountActive,
    ANImageModel? image,
    required bool isPackage,
    @Default([]) List<ANScheduleModel> schedule,
  }) = _ANServicesModel;

  factory ANServicesModel.fromJson(Map<String, Object?> json) =>
      _$ANServicesModelFromJson(json);
}

/*
{
    "title": "Regular service",
    "description": "description",
    "price": 150,
    "image": {
        "id": "63811bd3d8b4539830b6f378",
        "filename": "Success_Sticker-1.png",
        "mimeType": "image/png",
        "filesize": 8897,
        "width": 400,
        "height": 400,
        "createdAt": "2022-11-25T19:47:31.568Z",
        "updatedAt": "2022-11-25T19:47:31.568Z",
        "url": "https://ajmalnow.com/service/Success_Sticker-1.png"
    },
    "schedule": [
        {
            "day": "saturday",
            "slots": [
                {
                    "starts": "2022-12-29T07:01:00.000Z",
                    "ends": "2022-12-29T08:00:00.000Z",
                    "id": "63ada7553fbe855ec1a80b0e"
                }
            ],
            "id": "63ada7523fbe855ec1a80b0d"
        }
    ],
    "id": "63ada7473fbe855ec1a80b0c"
}
*/
