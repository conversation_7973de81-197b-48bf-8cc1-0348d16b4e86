// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'service_location.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ServiceLocation _$ServiceLocationFromJson(Map<String, dynamic> json) {
  return _ServiceLocation.fromJson(json);
}

/// @nodoc
mixin _$ServiceLocation {
  String get id => throw _privateConstructorUsedError;
  String get hotline => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get city => throw _privateConstructorUsedError;
  String get district => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ServiceLocationCopyWith<ServiceLocation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ServiceLocationCopyWith<$Res> {
  factory $ServiceLocationCopyWith(
          ServiceLocation value, $Res Function(ServiceLocation) then) =
      _$ServiceLocationCopyWithImpl<$Res, ServiceLocation>;
  @useResult
  $Res call(
      {String id, String hotline, String phone, String city, String district});
}

/// @nodoc
class _$ServiceLocationCopyWithImpl<$Res, $Val extends ServiceLocation>
    implements $ServiceLocationCopyWith<$Res> {
  _$ServiceLocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? hotline = null,
    Object? phone = null,
    Object? city = null,
    Object? district = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      hotline: null == hotline
          ? _value.hotline
          : hotline // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      city: null == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      district: null == district
          ? _value.district
          : district // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ServiceLocationCopyWith<$Res>
    implements $ServiceLocationCopyWith<$Res> {
  factory _$$_ServiceLocationCopyWith(
          _$_ServiceLocation value, $Res Function(_$_ServiceLocation) then) =
      __$$_ServiceLocationCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String hotline, String phone, String city, String district});
}

/// @nodoc
class __$$_ServiceLocationCopyWithImpl<$Res>
    extends _$ServiceLocationCopyWithImpl<$Res, _$_ServiceLocation>
    implements _$$_ServiceLocationCopyWith<$Res> {
  __$$_ServiceLocationCopyWithImpl(
      _$_ServiceLocation _value, $Res Function(_$_ServiceLocation) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? hotline = null,
    Object? phone = null,
    Object? city = null,
    Object? district = null,
  }) {
    return _then(_$_ServiceLocation(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      hotline: null == hotline
          ? _value.hotline
          : hotline // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      city: null == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String,
      district: null == district
          ? _value.district
          : district // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ServiceLocation implements _ServiceLocation {
  _$_ServiceLocation(
      {required this.id,
      required this.hotline,
      required this.phone,
      required this.city,
      required this.district});

  factory _$_ServiceLocation.fromJson(Map<String, dynamic> json) =>
      _$$_ServiceLocationFromJson(json);

  @override
  final String id;
  @override
  final String hotline;
  @override
  final String phone;
  @override
  final String city;
  @override
  final String district;

  @override
  String toString() {
    return 'ServiceLocation(id: $id, hotline: $hotline, phone: $phone, city: $city, district: $district)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ServiceLocation &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.hotline, hotline) || other.hotline == hotline) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.district, district) ||
                other.district == district));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, hotline, phone, city, district);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ServiceLocationCopyWith<_$_ServiceLocation> get copyWith =>
      __$$_ServiceLocationCopyWithImpl<_$_ServiceLocation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ServiceLocationToJson(
      this,
    );
  }
}

abstract class _ServiceLocation implements ServiceLocation {
  factory _ServiceLocation(
      {required final String id,
      required final String hotline,
      required final String phone,
      required final String city,
      required final String district}) = _$_ServiceLocation;

  factory _ServiceLocation.fromJson(Map<String, dynamic> json) =
      _$_ServiceLocation.fromJson;

  @override
  String get id;
  @override
  String get hotline;
  @override
  String get phone;
  @override
  String get city;
  @override
  String get district;
  @override
  @JsonKey(ignore: true)
  _$$_ServiceLocationCopyWith<_$_ServiceLocation> get copyWith =>
      throw _privateConstructorUsedError;
}
