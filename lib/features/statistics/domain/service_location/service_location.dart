import '../../../../common/domain/location/city_model.dart';

class ServiceLocation {
  final String id;
  final String hotline;
  final String phone;
  // New cityDistrict field (ID reference)
  final String? cityDistrictId;
  // Legacy fields for backward compatibility
  final String? city;
  final String? district;
  // Populated cityDistrict object (when fetched with depth)
  final CityDistrict? cityDistrictDetails;
  // Additional fields
  final String? address;

  ServiceLocation({
    required this.id,
    required this.hotline,
    required this.phone,
    this.cityDistrictId,
    this.city,
    this.district,
    this.cityDistrictDetails,
    this.address,
  });

  factory ServiceLocation.fromJson(Map<String, dynamic> json) {
    // Handle cases where fields might be maps with localization keys
    String extractStringValue(dynamic value) {
      if (value == null) return '';
      if (value is String) return value;
      if (value is Map) {
        // Try to get localized value, fallback to first available value
        if (value.containsKey('en')) return value['en'] as String? ?? '';
        if (value.containsKey('ar')) return value['ar'] as String? ?? '';
        // If no known locale keys, return the first value
        if (value.isNotEmpty) return value.values.first?.toString() ?? '';
      }
      return value.toString();
    }

    // Parse cityDistrict (can be either ID string or populated object)
    String? cityDistrictId;
    CityDistrict? cityDistrictDetails;
    
    if (json['cityDistrict'] != null) {
      if (json['cityDistrict'] is String) {
        cityDistrictId = json['cityDistrict'];
      } else if (json['cityDistrict'] is Map<String, dynamic>) {
        cityDistrictDetails = CityDistrict.fromJson(json['cityDistrict']);
        cityDistrictId = cityDistrictDetails.id;
      }
    }

    return ServiceLocation(
      id: json['id'] as String? ?? '',
      hotline: extractStringValue(json['hotline']),
      phone: extractStringValue(json['phone']),
      cityDistrictId: cityDistrictId,
      cityDistrictDetails: cityDistrictDetails,
      // Legacy fields for backward compatibility
      city: extractStringValue(json['clinic'] ?? json['city'] ?? json['legacyCity']),
      district: extractStringValue(json['district'] ?? json['legacyDistrict']),
      address: extractStringValue(json['address']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hotline': hotline,
      'phone': phone,
      'cityDistrict': cityDistrictId,
      'city': city,
      'district': district,
      'address': address,
    };
  }

  // Helper methods
  String get displayCity {
    if (cityDistrictDetails != null) {
      return cityDistrictDetails!.cityDisplayName;
    } else if (city != null && city!.isNotEmpty) {
      return city!;
    } else {
      return 'Unknown City';
    }
  }

  String get displayDistrict {
    if (cityDistrictDetails != null) {
      return cityDistrictDetails!.districtName.en;
    } else if (district != null && district!.isNotEmpty) {
      return district!;
    } else {
      return 'Unknown District';
    }
  }

  String get displayLocation {
    return '$displayCity, $displayDistrict';
  }

  // For sending to API - this method generates the JSON for API calls
  Map<String, dynamic> toApiJson() {
    final Map<String, dynamic> data = {};
    
    data['hotline'] = hotline;
    data['phone'] = phone;
    
    // Use cityDistrict if available, otherwise fall back to legacy fields
    if (cityDistrictId != null && cityDistrictId!.isNotEmpty) {
      data['cityDistrict'] = cityDistrictId;
    } else {
      // Fallback to legacy fields (will be deprecated)
      if (city != null && city!.isNotEmpty) {
        data['legacyCity'] = city;
      }
      if (district != null && district!.isNotEmpty) {
        data['legacyDistrict'] = district;
      }
    }
    
    if (address != null && address!.isNotEmpty) {
      data['address'] = address;
    }
    
    return data;
  }
      
  static ServiceLocation empty() {
    return ServiceLocation(
      id: '',
      hotline: '',
      phone: '',
      cityDistrictId: null,
      city: '',
      district: '',
      address: '',
    );
  }

  // CopyWith method for immutability
  ServiceLocation copyWith({
    String? id,
    String? hotline,
    String? phone,
    String? cityDistrictId,
    String? city,
    String? district,
    CityDistrict? cityDistrictDetails,
    String? address,
  }) {
    return ServiceLocation(
      id: id ?? this.id,
      hotline: hotline ?? this.hotline,
      phone: phone ?? this.phone,
      cityDistrictId: cityDistrictId ?? this.cityDistrictId,
      city: city ?? this.city,
      district: district ?? this.district,
      cityDistrictDetails: cityDistrictDetails ?? this.cityDistrictDetails,
      address: address ?? this.address,
    );
  }
}
