// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'experience.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ANExperienceModel _$$_ANExperienceModelFromJson(Map<String, dynamic> json) =>
    _$_ANExperienceModel(
      id: json['id'] as String,
      jobPosition: json['jobPosition'] as String?,
      institution: json['institution'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      currentlyWorking: json['currentlyWorking'] as bool?,
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$$_ANExperienceModelToJson(
        _$_ANExperienceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'jobPosition': instance.jobPosition,
      'institution': instance.institution,
      'startDate': instance.startDate?.toIso8601String(),
      'currentlyWorking': instance.currentlyWorking,
      'endDate': instance.endDate?.toIso8601String(),
    };
