import 'package:freezed_annotation/freezed_annotation.dart';

part 'experience.freezed.dart';
part 'experience.g.dart';

@freezed
class ANExperienceModel with _$ANExperienceModel {
  const factory ANExperienceModel({
    required String id,
    String? jobPosition,
    String? institution,
    DateTime? startDate,
    bool? currentlyWorking,
    DateTime? endDate,
  }) = _ANExperienceModel;

  factory ANExperienceModel.fromJson(Map<String, Object?> json) =>
      _$ANExperienceModelFromJson(json);
}
