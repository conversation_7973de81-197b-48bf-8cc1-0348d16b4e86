// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'experience.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANExperienceModel _$ANExperienceModelFromJson(Map<String, dynamic> json) {
  return _ANExperienceModel.fromJson(json);
}

/// @nodoc
mixin _$ANExperienceModel {
  String get id => throw _privateConstructorUsedError;
  String? get jobPosition => throw _privateConstructorUsedError;
  String? get institution => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  bool? get currentlyWorking => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANExperienceModelCopyWith<ANExperienceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANExperienceModelCopyWith<$Res> {
  factory $ANExperienceModelCopyWith(
          ANExperienceModel value, $Res Function(ANExperienceModel) then) =
      _$ANExperienceModelCopyWithImpl<$Res, ANExperienceModel>;
  @useResult
  $Res call(
      {String id,
      String? jobPosition,
      String? institution,
      DateTime? startDate,
      bool? currentlyWorking,
      DateTime? endDate});
}

/// @nodoc
class _$ANExperienceModelCopyWithImpl<$Res, $Val extends ANExperienceModel>
    implements $ANExperienceModelCopyWith<$Res> {
  _$ANExperienceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? jobPosition = freezed,
    Object? institution = freezed,
    Object? startDate = freezed,
    Object? currentlyWorking = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      jobPosition: freezed == jobPosition
          ? _value.jobPosition
          : jobPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      institution: freezed == institution
          ? _value.institution
          : institution // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentlyWorking: freezed == currentlyWorking
          ? _value.currentlyWorking
          : currentlyWorking // ignore: cast_nullable_to_non_nullable
              as bool?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANExperienceModelCopyWith<$Res>
    implements $ANExperienceModelCopyWith<$Res> {
  factory _$$_ANExperienceModelCopyWith(_$_ANExperienceModel value,
          $Res Function(_$_ANExperienceModel) then) =
      __$$_ANExperienceModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? jobPosition,
      String? institution,
      DateTime? startDate,
      bool? currentlyWorking,
      DateTime? endDate});
}

/// @nodoc
class __$$_ANExperienceModelCopyWithImpl<$Res>
    extends _$ANExperienceModelCopyWithImpl<$Res, _$_ANExperienceModel>
    implements _$$_ANExperienceModelCopyWith<$Res> {
  __$$_ANExperienceModelCopyWithImpl(
      _$_ANExperienceModel _value, $Res Function(_$_ANExperienceModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? jobPosition = freezed,
    Object? institution = freezed,
    Object? startDate = freezed,
    Object? currentlyWorking = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_$_ANExperienceModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      jobPosition: freezed == jobPosition
          ? _value.jobPosition
          : jobPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      institution: freezed == institution
          ? _value.institution
          : institution // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentlyWorking: freezed == currentlyWorking
          ? _value.currentlyWorking
          : currentlyWorking // ignore: cast_nullable_to_non_nullable
              as bool?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANExperienceModel implements _ANExperienceModel {
  const _$_ANExperienceModel(
      {required this.id,
      this.jobPosition,
      this.institution,
      this.startDate,
      this.currentlyWorking,
      this.endDate});

  factory _$_ANExperienceModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANExperienceModelFromJson(json);

  @override
  final String id;
  @override
  final String? jobPosition;
  @override
  final String? institution;
  @override
  final DateTime? startDate;
  @override
  final bool? currentlyWorking;
  @override
  final DateTime? endDate;

  @override
  String toString() {
    return 'ANExperienceModel(id: $id, jobPosition: $jobPosition, institution: $institution, startDate: $startDate, currentlyWorking: $currentlyWorking, endDate: $endDate)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANExperienceModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.jobPosition, jobPosition) ||
                other.jobPosition == jobPosition) &&
            (identical(other.institution, institution) ||
                other.institution == institution) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.currentlyWorking, currentlyWorking) ||
                other.currentlyWorking == currentlyWorking) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, jobPosition, institution,
      startDate, currentlyWorking, endDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANExperienceModelCopyWith<_$_ANExperienceModel> get copyWith =>
      __$$_ANExperienceModelCopyWithImpl<_$_ANExperienceModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANExperienceModelToJson(
      this,
    );
  }
}

abstract class _ANExperienceModel implements ANExperienceModel {
  const factory _ANExperienceModel(
      {required final String id,
      final String? jobPosition,
      final String? institution,
      final DateTime? startDate,
      final bool? currentlyWorking,
      final DateTime? endDate}) = _$_ANExperienceModel;

  factory _ANExperienceModel.fromJson(Map<String, dynamic> json) =
      _$_ANExperienceModel.fromJson;

  @override
  String get id;
  @override
  String? get jobPosition;
  @override
  String? get institution;
  @override
  DateTime? get startDate;
  @override
  bool? get currentlyWorking;
  @override
  DateTime? get endDate;
  @override
  @JsonKey(ignore: true)
  _$$_ANExperienceModelCopyWith<_$_ANExperienceModel> get copyWith =>
      throw _privateConstructorUsedError;
}
