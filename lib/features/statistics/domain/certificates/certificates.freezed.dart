// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'certificates.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANCertificatesModel _$ANCertificatesModelFromJson(Map<String, dynamic> json) {
  return _ANCertificatesModel.fromJson(json);
}

/// @nodoc
mixin _$ANCertificatesModel {
  String get id => throw _privateConstructorUsedError;
  String? get certificateName => throw _privateConstructorUsedError;
  String? get issuingInstitution => throw _privateConstructorUsedError;
  DateTime? get issueDate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANCertificatesModelCopyWith<ANCertificatesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANCertificatesModelCopyWith<$Res> {
  factory $ANCertificatesModelCopyWith(
          ANCertificatesModel value, $Res Function(ANCertificatesModel) then) =
      _$ANCertificatesModelCopyWithImpl<$Res, ANCertificatesModel>;
  @useResult
  $Res call(
      {String id,
      String? certificateName,
      String? issuingInstitution,
      DateTime? issueDate});
}

/// @nodoc
class _$ANCertificatesModelCopyWithImpl<$Res, $Val extends ANCertificatesModel>
    implements $ANCertificatesModelCopyWith<$Res> {
  _$ANCertificatesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? certificateName = freezed,
    Object? issuingInstitution = freezed,
    Object? issueDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      certificateName: freezed == certificateName
          ? _value.certificateName
          : certificateName // ignore: cast_nullable_to_non_nullable
              as String?,
      issuingInstitution: freezed == issuingInstitution
          ? _value.issuingInstitution
          : issuingInstitution // ignore: cast_nullable_to_non_nullable
              as String?,
      issueDate: freezed == issueDate
          ? _value.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANCertificatesModelCopyWith<$Res>
    implements $ANCertificatesModelCopyWith<$Res> {
  factory _$$_ANCertificatesModelCopyWith(_$_ANCertificatesModel value,
          $Res Function(_$_ANCertificatesModel) then) =
      __$$_ANCertificatesModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? certificateName,
      String? issuingInstitution,
      DateTime? issueDate});
}

/// @nodoc
class __$$_ANCertificatesModelCopyWithImpl<$Res>
    extends _$ANCertificatesModelCopyWithImpl<$Res, _$_ANCertificatesModel>
    implements _$$_ANCertificatesModelCopyWith<$Res> {
  __$$_ANCertificatesModelCopyWithImpl(_$_ANCertificatesModel _value,
      $Res Function(_$_ANCertificatesModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? certificateName = freezed,
    Object? issuingInstitution = freezed,
    Object? issueDate = freezed,
  }) {
    return _then(_$_ANCertificatesModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      certificateName: freezed == certificateName
          ? _value.certificateName
          : certificateName // ignore: cast_nullable_to_non_nullable
              as String?,
      issuingInstitution: freezed == issuingInstitution
          ? _value.issuingInstitution
          : issuingInstitution // ignore: cast_nullable_to_non_nullable
              as String?,
      issueDate: freezed == issueDate
          ? _value.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANCertificatesModel implements _ANCertificatesModel {
  const _$_ANCertificatesModel(
      {required this.id,
      this.certificateName,
      this.issuingInstitution,
      this.issueDate});

  factory _$_ANCertificatesModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANCertificatesModelFromJson(json);

  @override
  final String id;
  @override
  final String? certificateName;
  @override
  final String? issuingInstitution;
  @override
  final DateTime? issueDate;

  @override
  String toString() {
    return 'ANCertificatesModel(id: $id, certificateName: $certificateName, issuingInstitution: $issuingInstitution, issueDate: $issueDate)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANCertificatesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.certificateName, certificateName) ||
                other.certificateName == certificateName) &&
            (identical(other.issuingInstitution, issuingInstitution) ||
                other.issuingInstitution == issuingInstitution) &&
            (identical(other.issueDate, issueDate) ||
                other.issueDate == issueDate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, certificateName, issuingInstitution, issueDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANCertificatesModelCopyWith<_$_ANCertificatesModel> get copyWith =>
      __$$_ANCertificatesModelCopyWithImpl<_$_ANCertificatesModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANCertificatesModelToJson(
      this,
    );
  }
}

abstract class _ANCertificatesModel implements ANCertificatesModel {
  const factory _ANCertificatesModel(
      {required final String id,
      final String? certificateName,
      final String? issuingInstitution,
      final DateTime? issueDate}) = _$_ANCertificatesModel;

  factory _ANCertificatesModel.fromJson(Map<String, dynamic> json) =
      _$_ANCertificatesModel.fromJson;

  @override
  String get id;
  @override
  String? get certificateName;
  @override
  String? get issuingInstitution;
  @override
  DateTime? get issueDate;
  @override
  @JsonKey(ignore: true)
  _$$_ANCertificatesModelCopyWith<_$_ANCertificatesModel> get copyWith =>
      throw _privateConstructorUsedError;
}
