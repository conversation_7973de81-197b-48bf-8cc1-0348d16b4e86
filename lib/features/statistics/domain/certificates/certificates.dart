import 'package:freezed_annotation/freezed_annotation.dart';

part 'certificates.freezed.dart';
part 'certificates.g.dart';

@freezed
class ANCertificatesModel with _$ANCertificatesModel {
  const factory ANCertificatesModel({
    required String id,
    String? certificateName,
    String? issuingInstitution,
    DateTime? issueDate,
  }) = _ANCertificatesModel;

  factory ANCertificatesModel.fromJson(Map<String, Object?> json) =>
      _$ANCertificatesModelFromJson(json);
}
