
import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANServiceProviderCard extends ConsumerWidget {
  final ANServiceModel serviceProvider;
  const ANServiceProviderCard({Key? key, required this.serviceProvider})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authRepositoryProvider);
    final servicesController = ref.watch(servicesProvider);
    return GestureDetector(
      onTap: () {
        servicesController.selectBranch(serviceProvider);
        context.goNamed(AppRoute.serviceProviderDetailsScreen.name);
      },
      child: Container(
        decoration:
            BoxDecoration(borderRadius: circularBorderXXS, color: Colors.white),
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Row(
          children: [
            const SizedBox(width: 19.0),
            SizedBox(
              width: 120.0,
              height: 120.0,
              child: Image.network(
                serviceProvider.logo?.url?.replaceAll("localhost", Config.IP) ?? '',
                fit: BoxFit.cover,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: 17.0, horizontal: 10.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      serviceProvider.name,
                      style: GoogleFonts.philosopher(
                          textStyle: TextSize.r.boldStyle),
                    ),
                    if (authController.currentUser != null) ...[
                      gapHXXS,
                      Row(
                        children: [
                          const Icon(MaterialCommunityIcons.map_marker_outline),
                          Flexible(
                            child: Text(
                              serviceProvider.location.address?.isNotEmpty == true
                                  ? serviceProvider.location.address!
                                  : serviceProvider.location.displayLocation,
                              style: TextSize.xxs.regularStyle,
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )
                        ],
                      ),
                    ],
                    if (serviceProvider.description != null) ...[
                      gapHXL,
                      Text(
                        serviceProvider.description.toString(),
                        style: TextSize.xxs.regularStyle
                            .copyWith(fontStyle: FontStyle.italic),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            IconButton(
              onPressed: () {
                servicesController.selectBranch(serviceProvider);
                context.goNamed(AppRoute.editBranchScreen.name);
              },
              icon: const Icon(
                Icons.edit,
              ),
            )
          ],
        ),
      ),
    );
  }
}
