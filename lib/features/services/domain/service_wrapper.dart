import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';

class ServiceWrapper {
  final ANServiceModel service;
  final String? offersValue;

  ServiceWrapper({required this.service, this.offersValue});
}

final Map<String, String> serviceOffersCache = {};

// Helper function to get offers
String? getOffers(ANServiceModel service) {
  if (serviceOffersCache.containsKey(service.id)) {
    return serviceOffersCache[service.id];
  }
  
  try {
    // Try to access the offers field directly if the generated code is updated
    final offersValue = (service as dynamic).offers;
    serviceOffersCache[service.id] = offersValue;
    return offersValue;
  } catch (e) {
    // Return null if field doesn't exist or can't be accessed
    return null;
  }
}

// Helper function to set offers
void setOffers(ANServiceModel service, String? value) {
  if (value != null) {
    serviceOffersCache[service.id] = value;
  } else {
    serviceOffersCache.remove(service.id);
  }
} 