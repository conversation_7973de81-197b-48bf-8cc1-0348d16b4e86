// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'services.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANServicesModel _$ANServicesModelFromJson(Map<String, dynamic> json) {
  return _ANServicesModel.fromJson(json);
}

/// @nodoc
mixin _$ANServicesModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double? get minPrice => throw _privateConstructorUsedError;
  double? get maxPrice => throw _privateConstructorUsedError;
  double? get discount => throw _privateConstructorUsedError;
  bool? get discountActive => throw _privateConstructorUsedError;
  ANImageModel? get image => throw _privateConstructorUsedError;
  bool get isPackage => throw _privateConstructorUsedError;
  List<ANScheduleModel> get schedule => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANServicesModelCopyWith<ANServicesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANServicesModelCopyWith<$Res> {
  factory $ANServicesModelCopyWith(
          ANServicesModel value, $Res Function(ANServicesModel) then) =
      _$ANServicesModelCopyWithImpl<$Res, ANServicesModel>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      double price,
      double? minPrice,
      double? maxPrice,
      double? discount,
      bool? discountActive,
      ANImageModel? image,
      bool isPackage,
      List<ANScheduleModel> schedule});

  $ANImageModelCopyWith<$Res>? get image;
}

/// @nodoc
class _$ANServicesModelCopyWithImpl<$Res, $Val extends ANServicesModel>
    implements $ANServicesModelCopyWith<$Res> {
  _$ANServicesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? discount = freezed,
    Object? discountActive = freezed,
    Object? image = freezed,
    Object? isPackage = null,
    Object? schedule = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      minPrice: freezed == minPrice
          ? _value.minPrice
          : minPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPrice: freezed == maxPrice
          ? _value.maxPrice
          : maxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      discountActive: freezed == discountActive
          ? _value.discountActive
          : discountActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel?,
      isPackage: null == isPackage
          ? _value.isPackage
          : isPackage // ignore: cast_nullable_to_non_nullable
              as bool,
      schedule: null == schedule
          ? _value.schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as List<ANScheduleModel>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res>? get image {
    if (_value.image == null) {
      return null;
    }

    return $ANImageModelCopyWith<$Res>(_value.image!, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ANServicesModelCopyWith<$Res>
    implements $ANServicesModelCopyWith<$Res> {
  factory _$$_ANServicesModelCopyWith(
          _$_ANServicesModel value, $Res Function(_$_ANServicesModel) then) =
      __$$_ANServicesModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      double price,
      double? minPrice,
      double? maxPrice,
      double? discount,
      bool? discountActive,
      ANImageModel? image,
      bool isPackage,
      List<ANScheduleModel> schedule});

  @override
  $ANImageModelCopyWith<$Res>? get image;
}

/// @nodoc
class __$$_ANServicesModelCopyWithImpl<$Res>
    extends _$ANServicesModelCopyWithImpl<$Res, _$_ANServicesModel>
    implements _$$_ANServicesModelCopyWith<$Res> {
  __$$_ANServicesModelCopyWithImpl(
      _$_ANServicesModel _value, $Res Function(_$_ANServicesModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? minPrice = freezed,
    Object? maxPrice = freezed,
    Object? discount = freezed,
    Object? discountActive = freezed,
    Object? image = freezed,
    Object? isPackage = null,
    Object? schedule = null,
  }) {
    return _then(_$_ANServicesModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      minPrice: freezed == minPrice
          ? _value.minPrice
          : minPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      maxPrice: freezed == maxPrice
          ? _value.maxPrice
          : maxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      discountActive: freezed == discountActive
          ? _value.discountActive
          : discountActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel?,
      isPackage: null == isPackage
          ? _value.isPackage
          : isPackage // ignore: cast_nullable_to_non_nullable
              as bool,
      schedule: null == schedule
          ? _value._schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as List<ANScheduleModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANServicesModel implements _ANServicesModel {
  const _$_ANServicesModel(
      {required this.id,
      required this.title,
      required this.description,
      required this.price,
      this.minPrice,
      this.maxPrice,
      this.discount,
      this.discountActive,
      this.image,
      required this.isPackage,
      required final List<ANScheduleModel> schedule})
      : _schedule = schedule;

  factory _$_ANServicesModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANServicesModelFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final double price;
  @override
  final double? minPrice;
  @override
  final double? maxPrice;
  @override
  final double? discount;
  @override
  final bool? discountActive;
  @override
  final ANImageModel? image;
  @override
  final bool isPackage;
  final List<ANScheduleModel> _schedule;
  @override
  List<ANScheduleModel> get schedule {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_schedule);
  }

  @override
  String toString() {
    return 'ANServicesModel(id: $id, title: $title, description: $description, price: $price, minPrice: $minPrice, maxPrice: $maxPrice, discount: $discount, discountActive: $discountActive, image: $image, isPackage: $isPackage, schedule: $schedule)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANServicesModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.minPrice, minPrice) ||
                other.minPrice == minPrice) &&
            (identical(other.maxPrice, maxPrice) ||
                other.maxPrice == maxPrice) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.discountActive, discountActive) ||
                other.discountActive == discountActive) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isPackage, isPackage) ||
                other.isPackage == isPackage) &&
            const DeepCollectionEquality().equals(other._schedule, _schedule));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      price,
      minPrice,
      maxPrice,
      discount,
      discountActive,
      image,
      isPackage,
      const DeepCollectionEquality().hash(_schedule));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANServicesModelCopyWith<_$_ANServicesModel> get copyWith =>
      __$$_ANServicesModelCopyWithImpl<_$_ANServicesModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANServicesModelToJson(
      this,
    );
  }
}

abstract class _ANServicesModel implements ANServicesModel {
  const factory _ANServicesModel(
      {required final String id,
      required final String title,
      required final String description,
      required final double price,
      final double? minPrice,
      final double? maxPrice,
      final double? discount,
      final bool? discountActive,
      final ANImageModel? image,
      required final bool isPackage,
      required final List<ANScheduleModel> schedule}) = _$_ANServicesModel;

  factory _ANServicesModel.fromJson(Map<String, dynamic> json) =
      _$_ANServicesModel.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  double get price;
  @override
  double? get minPrice;
  @override
  double? get maxPrice;
  @override
  double? get discount;
  @override
  bool? get discountActive;
  @override
  ANImageModel? get image;
  @override
  bool get isPackage;
  @override
  List<ANScheduleModel> get schedule;
  @override
  @JsonKey(ignore: true)
  _$$_ANServicesModelCopyWith<_$_ANServicesModel> get copyWith =>
      throw _privateConstructorUsedError;
}
