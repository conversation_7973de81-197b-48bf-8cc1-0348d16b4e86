import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'services.freezed.dart';
part 'services.g.dart';

@freezed
class ANServicesModel with _$ANServicesModel {
  const factory ANServicesModel({
    required String id,
    required String title,
    required String description,
    required double price,
    double? minPrice,
    double? maxPrice,
    double? discount,
    bool? discountActive,
    ANImageModel? image,
    required bool isPackage,
    @Default([]) List<ANScheduleModel> schedule,
  }) = _ANServicesModel;

  factory ANServicesModel.fromJson(Map<String, Object?> json) =>
      _$ANServicesModelFromJson(json);
} 