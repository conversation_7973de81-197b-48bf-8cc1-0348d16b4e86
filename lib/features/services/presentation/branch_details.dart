import 'dart:io';

import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:flutter/foundation.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/common/general_widgets/rating_bar.dart';
import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/features/services/presentation/service_card.dart';
import 'package:ajmal_now_doctor/features/services/presentation/service_provider_card.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_gallery/service_gallery.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as google_maps;
import 'package:map_launcher/map_launcher.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

class ANServiceProviderDetailsScreen extends ConsumerStatefulWidget {
  const ANServiceProviderDetailsScreen({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _ANServiceProviderDetailsScreenState();
}

class _ANServiceProviderDetailsScreenState
    extends ConsumerState<ANServiceProviderDetailsScreen> {
  int _selectedItemIndex = -1;

  final ScrollController _controller = ScrollController();
  List<Map<String, dynamic>> owners = [];
  final controller = CarouselSliderController();
  int _activeIndex = 0;
  // This is what you're looking for!
  void _scrollDown() {
    _controller.animateTo(
      _controller.position.maxScrollExtent,
      duration: const Duration(seconds: 2),
      curve: Curves.fastOutSlowIn,
    );
  }

  void _showPhoneOptionsDialog(BuildContext context, String phoneNumber) {
    // For iOS, show an action sheet style dialog
    if (Platform.isIOS) {
      showCupertinoModalPopup(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: Text(S.of(context).kContactOptions),
            message: Text(S.of(context).kWouldYouLikeToCallOrCopy),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    // Use our common method for making phone calls
                    await _makePhoneCall(phoneNumber);
                  } catch (e) {
                    print("Error making phone call from dialog: $e");
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(                      content: Text(S.of(context).kCouldNotMakePhoneCall),
                      backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                child: Text('${S.of(context).kCallPhoneNumber} $phoneNumber'),
              ),
              CupertinoActionSheetAction(
                onPressed: () async {
                  try {
                    await Clipboard.setData(ClipboardData(text: phoneNumber));
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(S.of(context).kPhoneNumberCopied),
                        backgroundColor: AppColors.primaryColor,
                      ),
                    );
                  } catch (e) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(S.of(context).kCouldNotCopyPhoneNumber),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                child: Text(S.of(context).kCopyPhoneNumber),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              isDefaultAction: true,
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
          );
        },
      );
    } else {
      // For Android, use Material design dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(S.of(context).kContactOptions),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(S.of(context).kWouldYouLikeToCallOrCopy),
                SizedBox(height: 16),
                Text('Hotline: $phoneNumber', style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    // Use our common method for making phone calls
                    await _makePhoneCall(phoneNumber);
                  } catch (e) {
                    print("Error making phone call from Android dialog: $e");
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(                      content: Text(S.of(context).kCouldNotMakePhoneCall),
                      backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                child: Text(S.of(context).kCallPhoneNumber),
              ),
              TextButton(
                onPressed: () async {
                  // Try to copy to clipboard
                  try {
                    await Clipboard.setData(ClipboardData(text: phoneNumber));
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(S.of(context).kPhoneNumberCopied),
                        backgroundColor: AppColors.primaryColor,
                      ),
                    );
                  } catch (e) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(S.of(context).kCouldNotCopyPhoneNumber),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                child: Text(S.of(context).kCopyPhoneNumber),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Close'),
              ),
            ],
          );
        },
      );
    }
  }

  // Helper method to handle phone calls in a consistent way across iOS and Android
  Future<void> _makePhoneCall(String phoneNumber) async {

    if(Platform.isIOS) {
      return;
    }
    // Clean the phone number - remove any spaces, dashes, or special characters except +
    final cleanedNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Define the URI based on platform
    Uri uri;
    if (Platform.isIOS) {
      // For iOS, first try with the telprompt: scheme which should initiate call directly
      uri = Uri.parse('telprompt:$cleanedNumber');
      
      try {
        print("iOS: Attempting direct call with telprompt scheme: $uri");
        // Force using universal link mode specifically for iOS telprompt
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return; // Exit if successful
        } else {
          print("iOS: telprompt scheme failed, trying tel scheme...");
          // If telprompt fails, fall back to regular tel scheme
          uri = Uri.parse('tel:$cleanedNumber');
          if (await canLaunchUrl(uri)) {
            await launchUrl(
              uri,
              mode: LaunchMode.externalNonBrowserApplication,
            );
            return; // Exit if successful
          } else {
            throw Exception("Could not launch URL with either scheme");
          }
        }
      } catch (e) {
        print("iOS error launching call: $e");
        return Future.error(e);
      }
    } else {
      // For Android, use the standard tel: scheme
      uri = Uri.parse('tel:$cleanedNumber');
      try {
        print("Android: Attempting call with tel scheme: $uri");
        if (await canLaunchUrl(uri)) {
          await launchUrl(
            uri,
            mode: LaunchMode.externalApplication,
          );
        } else {
          throw Exception("Could not launch URL");
        }
      } catch (e) {
        print("Android error launching call: $e");
        return Future.error(e);
      }
    }
  }

  // Debug method to check URL scheme support
  Future<void> _debugCheckUrlSchemes(BuildContext context) async {
    if (!Platform.isIOS) return;
    
    final schemes = ['tel:', 'telprompt:'];
    final results = <String, bool>{};
    
    for (final scheme in schemes) {
      try {
        final uri = Uri.parse('${scheme}1234567890');
        final canLaunch = await canLaunchUrl(uri);
        results[scheme] = canLaunch;
      } catch (e) {
        results[scheme] = false;
        print('Error checking $scheme: $e');
      }
    }
    
    // Show results in a snackbar or dialog
    final message = 'URL Scheme Support:\n' +
        results.entries.map((e) => '${e.key}: ${e.value ? 'Supported' : 'Not supported'}').join('\n');
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: Duration(seconds: 10),
        ),
      );
    }
  }

  Future<void> _refreshData() async {
    // Reset selection
    setState(() {
      _selectedItemIndex = -1;
    });

    // Refresh owners list if user is authenticated
    if (ref.read(authRepositoryProvider).currentUser != null) {
      final list = await ref.read(servicesProvider).getServiceOwnersById();
      setState(() {
        owners = list;
      });
    }

    // Invalidate the service provider data to force a refresh
    ref.invalidate(serviceProviderByIdProvider);
  }

  void initOwners() async {
    final list = await ref.read(servicesProvider).getServiceOwnersById();
    setState(() {
      owners = list;
    });
  }

  @override
  void initState() {
    super.initState();
    if (ref.read(authRepositoryProvider).currentUser != null) {
      initOwners();
    }
    ref.refresh(serviceProviderByIdProvider);
    // Add listener for when the screen gains focus
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    // Listen to route changes to refresh data when returning to this screen
    //   GoRouter.of(context).addListener(() {
    //     if (mounted && ModalRoute.of(context)?.isCurrent == true) {
    //       _refreshData();
    //     }
    //   });
    // });
  }

  @override
  Widget build(BuildContext context) {
    final authController = ref.watch(authRepositoryProvider);
    final servicesController = ref.watch(servicesProvider);
    final serviceProviderController = ref.watch(serviceProviderByIdProvider);

    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDefaultPadding: true,
      ),
      floatingActionButton: CircleAvatar(
        backgroundColor: AppColors.primaryColor.withOpacity(0.8),
        child: IconButton(
          icon: const Icon(Icons.add, color: Colors.white),
          onPressed: () {
            context.goNamed(AppRoute.createServiceScreen.name);
          },
        ),
      ),
      body: Stack(
        children: [
          // Adding debug button at the top right corner if in debug mode
          if (!kReleaseMode) 
            Positioned(
              top: 50,
              right: 10,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () => _debugCheckUrlSchemes(context),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Icon(Icons.bug_report, color: Colors.grey.withOpacity(0.7)),
                  ),
                ),
              ),
            ),
          serviceProviderController.when(
            data: (data) => servicesController.loading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.primaryColor,
                    ),
                  )
                : SingleChildScrollView(
                    controller: _controller,
                    child: Column(
                      children: [
                        gapHXXL,
                        Center(
                          child: SizedBox(
                            height: 210.0,
                            width: 210.0, // Make it square for simplicity
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  8.0), // Optional: adds rounded corners
                              child: Image.network(
                                data.logo?.url ?? ''
                                    .replaceAll("localhost", Config.IP),
                                fit: BoxFit.cover,
                                // TODO: adjust this with the default image
                                errorBuilder: (context, error, stackTrace) {
                                  // Return a duck emoji as the fallback, centered in the Container
                                  return Container(
                                    child: const Center(
                                      child: Text(
                                        '🦆',
                                        style: TextStyle(fontSize: 100),
                                      ),
                                    ),
                                  );
                                },
                                loadingBuilder:
                                    (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Center(
                                    child: CircularProgressIndicator(
                                      value:
                                          loadingProgress.expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  loadingProgress
                                                      .expectedTotalBytes!
                                              : null,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                        gapHXXXL,
                        Padding(
                          padding: screenPadding,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      data.name,
                                      style: GoogleFonts.philosopher(
                                          textStyle: TextSize.xl.boldStyle),
                                    ),
                                  ),
                                  gapWXL
                                ],
                              ),
                              gapHR,
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  GestureDetector(
                                    onTap: () async {
                                      if (data.pinLocation != null && 
                                          data.pinLocation!.length >= 2) {
                                        try {
                                          // Create a Google Maps URL directly
                                          final lat = data.pinLocation![0];
                                          final lng = data.pinLocation![1];
                                          final url = Uri.parse('https://www.google.com/maps/search/?api=1&query=$lat,$lng');
                                          
                                          // Use url_launcher to open the URL
                                          if (await canLaunchUrl(url)) {
                                            await launchUrl(url, mode: LaunchMode.externalApplication);
                                          } else {
                                            throw 'Could not launch $url';
                                          }
                                        } catch (e) {
                                          print("Error opening map: $e");
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                              content: Text('Error opening map'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        const Icon(
                                          MaterialCommunityIcons.map_marker_outline,
                                          color: AppColors.primaryColor,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            _getLocationDisplayText(data.location),
                                            style: TextSize.s.regularStyle.copyWith(
                                              color: AppColors.primaryColor,
                                              decoration: TextDecoration.underline,
                                            ),
                                            maxLines: 3,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        const Icon(
                                          Icons.open_in_new,
                                          color: AppColors.primaryColor,
                                          size: 16,
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (data.location.hotline.isNotEmpty) ...[
                                    gapHXXS,
                                    Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(8),
                                        onTap: () async {
                                          final phoneNumber = data.location.hotline;
                                          
                                          // Give haptic feedback to indicate the tap was registered
                                          HapticFeedback.mediumImpact();
                                          
                                          try {
                                            // Use our improved method for making phone calls
                                            await _makePhoneCall(phoneNumber);
                                          } catch (e) {
                                            print("Error making phone call from tap: $e");
                                            // Show fallback dialog and a helpful message
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(
                                                content: Text(S.of(context).kShowingOptions),
                                              ),
                                            );
                                            await Future.delayed(Duration(milliseconds: 300));
                                            if (context.mounted) {
                                              _showPhoneOptionsDialog(context, phoneNumber);
                                            }
                                          }
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                                          child: Row(
                                            children: [
                                              const Icon(
                                                MaterialCommunityIcons.phone,
                                                color: AppColors.primaryColor,
                                                size: 20,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                'Hotline: ${data.location.hotline}',
                                                style: TextSize.s.regularStyle.copyWith(
                                                  color: AppColors.primaryColor,
                                                  decoration: TextDecoration.underline,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              const SizedBox(width: 4),
                                              const Icon(
                                                Icons.call,
                                                color: AppColors.primaryColor,
                                                size: 16,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                  if (data.location.phone.isNotEmpty && 
                                      data.location.phone != data.location.hotline) ...[
                                    gapHXXS,
                                    GestureDetector(
                                      onTap: () async {
                                        // Show phone options dialog
                                        _showPhoneOptionsDialog(context, data.location.phone);
                                      },
                                      child: Row(
                                        children: [
                                          const Icon(
                                            MaterialCommunityIcons.phone_outline,
                                            color: AppColors.primaryColor,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Phone: ${data.location.phone}',
                                            style: TextSize.s.regularStyle.copyWith(
                                              color: AppColors.primaryColor,
                                              decoration: TextDecoration.underline,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          const Icon(
                                            Icons.call,
                                            color: AppColors.primaryColor,
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                  gapHXS,
                                  // Add a small map preview
                                  if (data.pinLocation != null && 
                                      data.pinLocation!.length >= 2 && 
                                      data.pinLocation![0] != 0.0 && 
                                      data.pinLocation![1] != 0.0) ...[
                                    GestureDetector(
                                      onTap: () async {
                                        try {
                                          // Create coordinate values
                                          final lat = data.pinLocation![0];
                                          final lng = data.pinLocation![1];
                                          
                                          // Check available map apps
                                          final availableMaps = await MapLauncher.installedMaps;
                                          
                                          if (availableMaps.isNotEmpty) {
                                            // Show a dialog to let user choose which map app to use
                                            showModalBottomSheet(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return SafeArea(
                                                  child: SingleChildScrollView(
                                                    child: Column(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: <Widget>[
                                                        Padding(
                                                          padding: const EdgeInsets.all(16.0),
                                                          child: Text(
                                                            S.of(context).kOpenWithMap,
                                                            style: TextStyle(
                                                              fontSize: 16,
                                                              fontWeight: FontWeight.bold,
                                                            ),
                                                          ),
                                                        ),
                                                        const Divider(),
                                                        ...availableMaps.map((map) {
                                                          return ListTile(
                                                            onTap: () {
                                                              map.showMarker(
                                                                coords: Coords(lat, lng),
                                                                title: data.name,
                                                              );
                                                              Navigator.pop(context);
                                                            },
                                                            title: Text(map.mapName),
                                                            leading: SvgPicture.asset(
                                                              map.icon,
                                                              height: 30.0,
                                                              width: 30.0,
                                                              package: 'map_launcher',
                                                            ),
                                                          );
                                                        }).toList(),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            );
                                          } else {
                                            // Fallback if no map apps available
                                            Uri mapUrl = Uri.parse('https://www.google.com/maps/search/?api=1&query=$lat,$lng');
                                            if (await canLaunchUrl(mapUrl)) {
                                              await launchUrl(mapUrl, mode: LaunchMode.externalApplication);
                                            } else {
                                              throw 'Could not launch map application';
                                            }
                                          }
                                        } catch (e) {
                                          print("Error opening map: $e");
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(
                                              content: Text(S.of(context).kErrorOpeningMap),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                      },
                                      child: Container(
                                        margin: const EdgeInsets.only(top: 8),
                                        height: 150,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                          border: Border.all(color: AppColors.primaryColor.withOpacity(0.3)),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(10),
                                          child: Stack(
                                            children: [
                                              google_maps.GoogleMap(
                                                mapType: google_maps.MapType.normal,
                                                initialCameraPosition: google_maps.CameraPosition(
                                                  target: google_maps.LatLng(data.pinLocation![0], data.pinLocation![1]),
                                                  zoom: 14,
                                                ),
                                                markers: {
                                                  google_maps.Marker(
                                                    markerId: const google_maps.MarkerId('branch_location'),
                                                    position: google_maps.LatLng(data.pinLocation![0], data.pinLocation![1]),
                                                  ),
                                                },
                                                zoomControlsEnabled: false,
                                                zoomGesturesEnabled: false,
                                                scrollGesturesEnabled: false,
                                                rotateGesturesEnabled: false,
                                                tiltGesturesEnabled: false,
                                                myLocationEnabled: false,
                                                myLocationButtonEnabled: false,
                                                mapToolbarEnabled: false,
                                                compassEnabled: false,
                                              ),
                                              Positioned(
                                                right: 10,
                                                bottom: 10,
                                                child: Container(
                                                  padding: const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 4,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.primaryColor,
                                                    borderRadius: BorderRadius.circular(4),
                                                  ),
                                                  child: Text(
                                                    S.of(context).kOpenMap,
                                                    style: TextSize.xs.mediumStyle.copyWith(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              gapHL,
                              if (data.description != null) ...[
                                Column(
                                  children: [
                                    Text(
                                      data.description!,
                                      style: TextSize.xs.regularStyle,
                                    ),
                                    gapHXXXL,
                                    gapHXXS,
                                  ],
                                ),
                              ],
                              // Add offers section
                              if (data.offers != null && data.offers!.isNotEmpty) ...[
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      S.of(context).kOffers,
                                      style: TextSize.r.semiBoldStyle,
                                    ),
                                    gapHR,
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryColor.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: AppColors.primaryColor.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.campaign_outlined,
                                                color: AppColors.primaryColor,
                                                size: 18,
                                              ),
                                              gapWXS,
                                              Text(
                                                S.of(context).kSpecialOffers,
                                                style: TextSize.xs.semiBoldStyle.copyWith(
                                                  color: AppColors.primaryColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                          gapHXS,
                                          Text(
                                            data.offers!,
                                            style: TextSize.xs.regularStyle,
                                          ),
                                        ],
                                      ),
                                    ),
                                    gapHXXXL,
                                    gapHXXS,
                                  ],
                                ),
                              ],
                              Text(
                                S.of(context).kGallery,
                                style: TextSize.r.semiBoldStyle,
                              ),
                              gapHR,
                              CarouselSlider.builder(
                                itemCount: data.gallery.length,
                                carouselController: controller,
                                itemBuilder: (context, index, realIndex) {
                                  return buildImage(data.gallery, index) ??
                                      const SizedBox();
                                },
                                options: CarouselOptions(
                                  height: 200,
                                  aspectRatio: 16 / 9,
                                  autoPlay: false,
                                  enableInfiniteScroll: false,
                                  autoPlayAnimationDuration:
                                      const Duration(seconds: 2),
                                  enlargeCenterPage: true,
                                  onPageChanged: (index, reason) =>
                                      setState(() => _activeIndex = index),
                                ),
                              ),
                              gapHL,
                              Center(child: buildIndicator(data.gallery)),
                              gapHXL,
                              gapHXXXL,
                              Text(
                                S.of(context).kOwners,
                                style: TextSize.r.semiBoldStyle,
                              ),
                              gapHR,
                              ListView.separated(
                                itemCount: owners.length,
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemBuilder: (BuildContext context, int index) {
                                  return ANServiceProviderOwnerCard(
                                    ownerName:
                                        owners.elementAt(index)['full_name'],
                                    ownerBiography:
                                        owners.elementAt(index)['biography'],
                                  );
                                },
                                separatorBuilder:
                                    (BuildContext context, int index) => gapHR,
                              ),
                              gapHXXXL,
                              Text(
                                S.of(context).kServices,
                                style: TextSize.r.semiBoldStyle,
                              ),
                              gapHR,
                              ListView.separated(
                                itemCount: data.services
                                    .where((item) => !item.isPackage)
                                    .length,
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemBuilder: (BuildContext context, int index) {
                                  final services = data.services
                                      .where((item) => !item.isPackage)
                                      .toList();
                                  return GestureDetector(
                                    onTap: () {},
                                    child: ANServiceCard(
                                      service: services[index],
                                      isSelected: _selectedItemIndex ==
                                          data.services
                                              .indexOf(services[index]),
                                      onPressed: () {
                                        servicesController
                                            .selectService(services[index]);
                                        context.goNamed(
                                            AppRoute.editServiceScreen.name);
                                      },
                                    )
                                  );
                                },
                                separatorBuilder:
                                    (BuildContext context, int index) => gapHR,
                              ),

                              // Packages Section
                              if (data.services.toList() != [])
                                if (data.services
                                    .any((item) => item.isPackage)) ...[
                                  gapHXXL,
                                  Text(
                                    "Packages",
                                    style: TextSize.r.semiBoldStyle,
                                  ),
                                  gapHR,
                                  ListView.separated(
                                    itemCount: data.services
                                        .where((item) => item.isPackage)
                                        .length,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      final packages = data.services
                                          .where((item) => item.isPackage)
                                          .toList();
                                      return GestureDetector(
                                        onTap: () {},
                                        child: ANPackageCard(
                                          package: packages[index],
                                          isSelected: _selectedItemIndex ==
                                              data.services
                                                  .indexOf(packages[index]),
                                          onPressed: () {
                                            servicesController
                                                .selectService(packages[index]);
                                            context.goNamed(AppRoute
                                                .editServiceScreen.name);
                                          },
                                        ),
                                      );
                                    },
                                    separatorBuilder:
                                        (BuildContext context, int index) =>
                                            gapHR,
                                  ),
                                ],
                              // Availability Section
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
            error: (error, _) =>
                Center(child: ANError(errorMessage: error.toString())),
            loading: () => const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryColor,
              ),
            ),
          ),
          const Padding(
            padding: screenPadding,
            child: ANBackButton(),
          ),
        ],
      ),
    );
  }

  // Helper method to get location display text
  String _getLocationDisplayText(dynamic location) {
    // Debug: Print location data to see what we have
    print("🔍 _getLocationDisplayText received location: $location");
    if (location != null) {
      print("   - address: '${location.address}'");
      print("   - city: '${location.city}'");
      print("   - district: '${location.district}'");
    }
    
    if (location == null) {
      return S.of(context).kLocationNotAvailable;
    }

    // Prioritize full address first
    if (location.address != null && location.address!.isNotEmpty) {
      print("🔍 Debug - Using full address: ${location.address}");
      return location.address!;
    }

    // Fallback to city/district if no full address
    List<String> locationParts = [];
    
    // Add city if available
    if (location.city != null && location.city.isNotEmpty) {
      locationParts.add(location.city);
    }
    
    // Add district if available  
    if (location.district != null && location.district.isNotEmpty) {
      locationParts.add(location.district);
    }
    
    // If we have location parts, join them with comma
    if (locationParts.isNotEmpty) {
      print("🔍 Debug - Using city/district: ${locationParts.join(', ')}");
      return locationParts.join(', ');
    }
    
    // Fallback to displayLocation if it exists
    if (location.displayLocation != null && location.displayLocation.isNotEmpty) {
      return location.displayLocation;
    }
    
    // Fallback to hotline if no location text is available
    if (location.hotline != null && location.hotline.isNotEmpty) {
      print("🔍 Debug - Using hotline fallback: Contact: ${location.hotline}");
      return 'Contact: ${location.hotline}';
    }
    
    print("🔍 Debug - No location data available");
    return S.of(context).kLocationNotAvailable;
  }

  Widget buildIndicator(gallery) {
    return AnimatedSmoothIndicator(
      onDotClicked: animateToSlide,
      effect: const ExpandingDotsEffect(
        dotWidth: 10,
        activeDotColor: AppColors.primaryColor,
        dotHeight: 10,
      ),
      activeIndex: _activeIndex,
      count: gallery.length,
    );
  }

  void animateToSlide(int index) => controller.animateToPage(index);

  Widget? buildImage(List<ANServiceGalleryModel> urlImages, int index) {
    return Image.network(
      urlImages[index].logo.url.replaceAll("localhost", Config.IP),
    );
  }
}
