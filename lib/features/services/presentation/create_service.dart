// ignore_for_file: use_build_context_synchronously


import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/common/text_fields/text-field.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/date_specific_availability_repository.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/features/services/presentation/tabbed_schedule_selector.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

class ANCreateServiceeScreen extends ConsumerStatefulWidget {
  const ANCreateServiceeScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _ANCreateServiceScreenState();
}

class _ANCreateServiceScreenState
    extends ConsumerState<ANCreateServiceeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController scrollController = ScrollController();
  final _formKey = GlobalKey<FormState>();
  final _logoKey = GlobalKey<FormState>();
  bool? _isDaySelectionError = false;
  final TextEditingController _serviceNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _discountController = TextEditingController();
  bool? logoError = false;
  bool _isPackage = false;
  File? _image;
  String? imageUrl;

  List<ANScheduleModel> schedules = [];
  bool isActive = false;
  bool enableGap = false;
  // Date specific availability data
  List<DateTime> dateSpecificDates = [];
  Map<DateTime, List<ANSlotsModel>> dateSpecificSlots = {};
  Map<DateTime, bool> dateAvailability = {};

  @override
  void initState() {
    final servicesController = ref.read(servicesProvider);
    servicesController.logo = null;
    servicesController.gallery = [];
    super.initState();
  }

  // Handle date-specific availability changes
  void _handleDateSpecificScheduleChanged(List<ANSlotsModel> slots, DateTime date, bool isAvailable) {
    setState(() {
      if (!dateSpecificDates.contains(date)) {
        dateSpecificDates.add(date);
      }
      dateSpecificSlots[date] = slots;
      dateAvailability[date] = isAvailable;
    });
  }

  @override
  Widget build(BuildContext context) {
    final servicesController = ref.watch(servicesProvider);
    final serviceProvider = ref.watch(serviceProviderByIdProvider.future);
    
    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
        disableBottomNavBar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: true,
      ),
      appbar: ANMainScreenAppbar(
        title: Text(S.of(context).kCreateNewService),
      ),
      body: RefreshIndicator(
        onRefresh: () async => setState(() {}),
        child: SingleChildScrollView(
          controller: scrollController,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  '* ${S.of(context).kServiceName}',
                  style: const TextStyle(color: AppColors.primaryColor),
                ),
                gapHXXS,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _serviceNameController,
                  hint: S.of(context).kServiceName,
                  validator: (value) => ANValidationsHelper.getValidators(
                      validation: {'is_required': true}, value: value),
                ),
                gapHXL,
                Text(
                  '* ${S.of(context).kDescription}',
                  style: const TextStyle(color: AppColors.primaryColor),
                ),
                gapHXXS,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _descriptionController,
                  hint: S.of(context).kDescription,
                  textInputType: TextInputType.multiline,
                  maxLines: 2,
                  maxLength: 500,
                  validator: (value) => ANValidationsHelper.getValidators(
                      validation: {'is_required': true}, value: value),
                ),
                gapHXL,
                Text(
                  '* ${S.of(context).kOriginalPriceEGP}',
                  style: const TextStyle(color: AppColors.primaryColor),
                ),
                gapHXXS,
                Row(
                  children: [
                    Expanded(
                      child: ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _minPriceController,
                        hint: S.of(context).kMinPriceEGP,
                        textInputType: TextInputType.number,
                        validator: (value) {
                          if (_minPriceController.text.isEmpty && _maxPriceController.text.isEmpty) {
                            return S.of(context).kPleaseEnterAtLeastOnePrice;
                          }
                          return null;
                        },
                      ),
                    ),
                    gapWM,
                    Expanded(
                      child: ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _maxPriceController,
                        hint: S.of(context).kMaxPriceEGP,
                        textInputType: TextInputType.number,
                        validator: (value) {
                          if (_minPriceController.text.isNotEmpty && _maxPriceController.text.isNotEmpty) {
                            double min = double.parse(_minPriceController.text);
                            double max = double.parse(_maxPriceController.text);
                            if (max < min) {
                              return S.of(context).kMaxPriceMustBeGreaterThanMinPrice;
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _discountController,
                  hint: S.of(context).kDiscountPercentage,
                  textInputType: TextInputType.number,
                  validator: isActive
                      ? (value) => ANValidationsHelper.getValidators(
                          validation: {'is_required': true}, value: value)
                      : null,
                ),
                gapHXL,
                Row(
                  children: [
                    Checkbox(
                      value: isActive,
                      side: const BorderSide(color: AppColors.primaryColor),
                      activeColor: AppColors.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      onChanged: (value) => setState(() => isActive = value!),
                    ),
                    Text(S.of(context).kDiscountActive,
                        style: TextSize.r.lightStyle),
                  ],
                ),
                Row(
                  children: [
                    Checkbox(
                      value: _isPackage,
                      side: const BorderSide(color: AppColors.primaryColor),
                      activeColor: AppColors.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      onChanged: (value) => setState(() => _isPackage = value!),
                    ),
                    Text(S.of(context).kIsThisAPackage,
                        style: TextSize.r.lightStyle),
                  ],
                ),
                gapHXL,
                FutureBuilder(
                  future: serviceProvider,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    
                    if (snapshot.hasError) {
                      return Text('Error: ${snapshot.error}');
                    }
                    
                    final serviceData = snapshot.data;
                    if (serviceData == null) {
                      return const Text('No service provider data available');
                    }
                    
                    return TabbedScheduleSelector(
                      weeklySchedules: schedules,
                      onWeeklySchedulesChanged: (newSchedules) => setState(() {
                        schedules = newSchedules;
                      }),
                      onDateSpecificScheduleChanged: _handleDateSpecificScheduleChanged,
                      serviceProviderId: serviceData.id,
                      serviceId: DateTime.now().toString(), // Temporary ID for new service
                      hasError: false,
                    );
                  }
                ),
                gapHXL,
                ANLoadingElevatedButton(
                  onPressed: () async {
                    if (!_formKey.currentState!.validate()) {
                      return;
                    }

                    try {
                      double? minPrice = _minPriceController.text.isNotEmpty 
                          ? double.parse(_minPriceController.text) 
                          : null;
                      double? maxPrice = _maxPriceController.text.isNotEmpty 
                          ? double.parse(_maxPriceController.text) 
                          : null;
                      
                      // For backward compatibility, set a legacy price field value
                      // If only one price is provided, use that value
                      // If both prices are provided, use the minimum price for better representation
                      double price = 0;
                      if (minPrice != null && maxPrice == null) {
                        price = minPrice;
                      } else if (maxPrice != null && minPrice == null) {
                        price = maxPrice;
                      } else if (minPrice != null && maxPrice != null) {
                        // Use minimum price for the legacy field
                        price = minPrice;
                      }
                      
                      ANServicesModel service = ANServicesModel(
                        id: DateTime.now().toString(),
                        title: _serviceNameController.text,
                        description: _descriptionController.text,
                        price: price,
                        minPrice: minPrice,
                        maxPrice: maxPrice,
                        discount: isActive
                            ? double.parse(_discountController.text)
                            : null,
                        discountActive: isActive,
                        isPackage: _isPackage,
                        // Keep empty schedule for backward compatibility 
                        schedule: [],
                      );
                      ANServiceModel res = await servicesController
                          .createService(newService: service);
                      
                      // Save date-specific availability
                      final dateSpecificRepo = ref.read(dateSpecificAvailabilityProvider);
                      for (final date in dateSpecificDates) {
                        if (dateSpecificSlots.containsKey(date)) {
                          await dateSpecificRepo.saveDateSpecificAvailability(
                            providerId: res.id,
                            serviceId: res.services.first.id,
                            date: date,
                            slots: dateSpecificSlots[date] ?? [],
                          );
                        }
                      }
                      
                      await ANDialogHelper.gShowSuccessDialog(
                          context: context,
                          title: S.of(context).kServiceCreatedSuccessfully,
                          primaryButtonLabel: S.of(context).kConfirm);
                      Navigator.pop(context);
                      ref.refresh(serviceProviderByIdProvider.future);
                      // ref.refresh(servicesDataProvider);
                    } catch (e) {
                      await ANDialogHelper.gShowSuccessDialog(
                          context: context,
                          title: S.of(context).kBranchCreatedSuccessfully,
                          primaryButtonLabel: S.of(context).kConfirm);
                      Navigator.pop(context);
                    }
                  },
                  label: Text(
                    S.of(context).kCreate,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
                ),
                gapHXL,
                if (enableGap) const SizedBox(height: 270),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<bool> validateForm() {
    if (_formKey.currentState!.validate()) {
      if (_image == null && imageUrl == null) {
        ANDialogHelper.gShowConfirmationDialog(
          context: context,
          message: S.of(context).kServiceUploadImageValidation,
          type: DialogType.error,
        );
        return Future.value(false);
      }
      
      // We no longer require weekly schedules
      return Future.value(true);
    } else {
      if (_image == null) {
        ANDialogHelper.gShowConfirmationDialog(
          context: context,
          message: S.of(context).kServiceUploadImageValidation,
          type: DialogType.error,
        );
      }
      return Future.value(false);
    }
  }
}
