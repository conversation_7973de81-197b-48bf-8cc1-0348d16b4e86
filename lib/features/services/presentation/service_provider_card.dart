import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// ignore: must_be_immutable
class ANServiceProviderOwnerCard extends StatelessWidget {
  final String ownerName;
  final String? ownerBiography;
  const ANServiceProviderOwnerCard(
      {Key? key, required this.ownerName, required this.ownerBiography})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 21.0, horizontal: 23.0),
      decoration: BoxDecoration(
        borderRadius: circularBorderXXS,
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      ownerName,
                      style: GoogleFonts.philosopher(
                          textStyle: TextSize.r.boldStyle
                              .copyWith(color: Colors.black)),
                    )
                  ],
                ),
                if (ownerBiography != null) ...[
                  gapHL,
                  Text(
                    ownerBiography!,
                    style:
                        TextSize.s.regularStyle.copyWith(color: Colors.black),
                    textAlign: TextAlign.start,
                  )
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
