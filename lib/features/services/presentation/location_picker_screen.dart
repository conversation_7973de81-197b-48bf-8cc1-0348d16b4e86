import 'dart:async';

import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';

class ANLocationPickerScreen extends StatefulWidget {
  LatLng? pickedLocation;
  ANLocationPickerScreen({super.key, required this.pickedLocation});

  @override
  State<ANLocationPickerScreen> createState() => _ANLocationPickerScreenState();
}

class _ANLocationPickerScreenState extends State<ANLocationPickerScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String? _searchError;
  Timer? _debounce;
  String _lastSearchQuery = '';
  final FocusNode _searchFocusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  GoogleMapController? _mapController;

  LatLng _selectedLocation = const LatLng(30.0444, 31.2357); // Cairo, Egypt as default
  String? _address;
  List<Map<String, dynamic>> _searchResults = [];

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    
    // Apply a map style that's more similar to Google Maps with Arabic labels highlighted
    _mapController?.setMapStyle('''
      [
        {
          "featureType": "administrative",
          "elementType": "geometry",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "administrative.country",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            },
            {
              "weight": 1.5
            }
          ]
        },
        {
          "featureType": "administrative.province",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "poi",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "poi.business",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "poi.park",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "road",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "road",
          "elementType": "labels.icon",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "road.arterial",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "road.highway",
          "elementType": "labels",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "road.local",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        },
        {
          "featureType": "transit",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        }
      ]
    ''');
    
    // If we already have a location, get its address
    if (widget.pickedLocation != null) {
      _getAddress(widget.pickedLocation!);
    }
  }

  Future<void> _onTap(LatLng position) async {
    _removeOverlay();
    setState(() {
      _selectedLocation = position;
    });
    await _getAddress(position);
  }

  Future<void> _getAddress(LatLng position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        String address = '';
        
        if (place.name != null && place.name!.isNotEmpty) {
          address += place.name!;
        }
        
        if (place.street != null && place.street!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.street!;
        }
        
        if (place.locality != null && place.locality!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.locality!;
        }
        
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.administrativeArea!;
        }
        
        if (place.country != null && place.country!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.country!;
        }
        
        if (address.isEmpty) {
          address = '${position.latitude}, ${position.longitude}';
        }
        
        setState(() {
          _address = address;
        });
      }
    } catch (e) {
      print('Error getting address: $e');
      setState(() {
        _address = '${position.latitude}, ${position.longitude}';
      });
    }
  }

  // Try to search with various modifications of the query but prioritize Egypt locations
  Future<List<Location>> _trySearchWithVariations(String query) async {
    try {
      print('Trying variations of: $query');
      
      // First, check if the query already includes Egypt in some form
      bool hasEgyptReference = _containsEgyptReference(query);
      
      // Try with Egypt first 
      if (!hasEgyptReference) {
        try {
          print('Trying with country first: $query, Egypt');
          var locations = await locationFromAddress("$query, Egypt");
          if (locations.isNotEmpty) return await _filterEgyptLocations(locations);
        } catch (e) {
          print('Query with country failed: $e');
        }
      }
      
      // Try original query but filter for Egypt locations
      try {
        var locations = await locationFromAddress(query);
        if (locations.isNotEmpty) {
          var egyptLocations = await _filterEgyptLocations(locations);
          if (egyptLocations.isNotEmpty) return egyptLocations;
          
          // If no Egypt locations found but we have results, and query didn't specify Egypt,
          // try again with Egypt in the query
          if (!hasEgyptReference) {
            try {
              var locationsWithEgypt = await locationFromAddress("$query, Egypt");
              egyptLocations = await _filterEgyptLocations(locationsWithEgypt);
              if (egyptLocations.isNotEmpty) return egyptLocations;
            } catch (e) {
              print('Egypt-appended retry failed: $e');
              // Fall back to original results
            }
          }
          
          return locations; // Fall back to all locations if no Egypt ones found
        }
      } catch (e) {
        print('Original query failed: $e');
      }
      
      // Try with major cities in Egypt
      for (String city in ['Cairo', 'Alexandria', 'Giza', 'Luxor']) {
        try {
          print('Trying as a location in $city: $query, $city, Egypt');
          var locations = await locationFromAddress("$query, $city, Egypt");
          if (locations.isNotEmpty) {
            var egyptLocations = await _filterEgyptLocations(locations);
            if (egyptLocations.isNotEmpty) return egyptLocations;
            return locations;
          }
        } catch (e) {
          print('Egypt city query for $city failed: $e');
        }
      }
      
      // Try with transliterated Arabic names if the query might be in English
      if (!_containsArabicCharacters(query)) {
        try {
          var arabicEquivalents = _getArabicEquivalents(query);
          for (var arabicName in arabicEquivalents) {
            try {
              print('Trying with Arabic equivalent: $arabicName, Egypt');
              var locations = await locationFromAddress("$arabicName, Egypt");
              if (locations.isNotEmpty) return await _filterEgyptLocations(locations);
            } catch (e) {
              print('Arabic equivalent query failed: $e');
            }
          }
        } catch (e) {
          print('Arabic transliteration attempt failed: $e');
        }
      }
      
      // Rest of the variants...
      // Try with first letter capitalized
      if (query.length > 1) {
        try {
          var capitalizedQuery = query[0].toUpperCase() + query.substring(1).toLowerCase();
          print('Trying capitalized: $capitalizedQuery, Egypt');
          var locations = await locationFromAddress("$capitalizedQuery, Egypt");
          if (locations.isNotEmpty) return locations;
        } catch (e) {
          print('Capitalized query failed: $e');
        }
      }
      
      // Try all lowercase
      try {
        print('Trying lowercase: ${query.toLowerCase()}, Egypt');
        var locations = await locationFromAddress("${query.toLowerCase()}, Egypt");
        if (locations.isNotEmpty) return locations;
      } catch (e) {
        print('Lowercase query failed: $e');
      }
      
      // Try all uppercase
      try {
        print('Trying uppercase: ${query.toUpperCase()}, Egypt');
        var locations = await locationFromAddress("${query.toUpperCase()}, Egypt");
        if (locations.isNotEmpty) return locations;
      } catch (e) {
        print('Uppercase query failed: $e');
      }
      
      // If adding common words helps
      if (!query.toLowerCase().contains("street") && 
          !query.toLowerCase().contains("avenue") && 
          !query.toLowerCase().contains("road") &&
          !query.toLowerCase().contains("شارع")) {
        try {
          print('Trying with "street": $query street, Egypt');
          var locations = await locationFromAddress("$query street, Egypt");
          if (locations.isNotEmpty) return locations;
        } catch (e) {
          print('Query with street failed: $e');
        }
        
        // Try with Arabic word for street
        try {
          print('Trying with "شارع": شارع $query, Egypt');
          var locations = await locationFromAddress("شارع $query, Egypt");
          if (locations.isNotEmpty) return locations;
        } catch (e) {
          print('Query with Arabic street word failed: $e');
        }
      }
      
      // Try just the first part if there are multiple words
      if (query.contains(" ")) {
        try {
          var firstPart = query.split(" ")[0];
          print('Trying first part: $firstPart, Egypt');
          var locations = await locationFromAddress("$firstPart, Egypt");
          if (locations.isNotEmpty) return locations;
        } catch (e) {
          print('First part query failed: $e');
        }
      }
      
      // Try removing special characters
      try {
        var cleanQuery = query.replaceAll(RegExp(r'[^\w\s]'), '');
        if (cleanQuery != query) {
          print('Trying clean query: $cleanQuery, Egypt');
          var locations = await locationFromAddress("$cleanQuery, Egypt");
          if (locations.isNotEmpty) return locations;
        }
      } catch (e) {
        print('Clean query failed: $e');
      }
      
      // Try common place fallbacks if query is short (likely a place name)
      if (query.length < 15) {
        try {
          print('Trying as a city: $query City, Egypt');
          var locations = await locationFromAddress("$query City, Egypt");
          if (locations.isNotEmpty) return locations;
        } catch (e) {
          print('City query failed: $e');
        }
      }
      
      // Fall back to original query if everything else fails
      try {
        var locations = await locationFromAddress(query);
        return locations;
      } catch (e) {
        print('Final fallback query failed: $e');
      }
      
      return [];
    } catch (e) {
      print('Error in search variations: $e');
      return [];
    }
  }
  
  // Helper method to check if the query already has Egypt references
  bool _containsEgyptReference(String query) {
    String normalized = query.toLowerCase();
    return normalized.contains('egypt') || 
           normalized.contains('مصر') || 
           normalized.contains('cairo') || 
           normalized.contains('القاهرة') ||
           normalized.contains('alexandria') ||
           normalized.contains('الإسكندرية') ||
           normalized.contains('giza') ||
           normalized.contains('الجيزة');
  }
  
  // Helper method to check if string contains Arabic characters
  bool _containsArabicCharacters(String text) {
    // Unicode range for Arabic characters
    RegExp arabicChars = RegExp(r'[\u0600-\u06FF]');
    return arabicChars.hasMatch(text);
  }
  
  // Helper to get Arabic equivalents for common English place names
  List<String> _getArabicEquivalents(String query) {
    // Map common English place name parts to Arabic equivalents
    Map<String, String> transliterationMap = {
      'street': 'شارع',
      'road': 'طريق',
      'square': 'ميدان',
      'avenue': 'جادة',
      'mosque': 'مسجد',
      'market': 'سوق',
      'tower': 'برج',
      'mall': 'مول',
      'city': 'مدينة',
      'district': 'حي',
      'downtown': 'وسط البلد',
      'university': 'جامعة',
      'hospital': 'مستشفى',
      'bridge': 'كوبري',
      'park': 'حديقة',
      'station': 'محطة',
      'garden': 'حدائق',
      'center': 'مركز',
      'building': 'مبنى',
      'plaza': 'ساحة',
      'heights': 'مرتفعات',
      'new': 'الجديدة',
      'old': 'القديمة',
      'east': 'شرق',
      'west': 'غرب',
      'north': 'شمال',
      'south': 'جنوب',
      'cairo': 'القاهرة',
      'alexandria': 'الإسكندرية',
      'giza': 'الجيزة',
      'luxor': 'الأقصر',
      'aswan': 'أسوان',
      'sharm': 'شرم',
      'sheikh': 'الشيخ',
      'zamalek': 'الزمالك',
      'maadi': 'المعادي',
      'heliopolis': 'مصر الجديدة',
      'mohandessin': 'المهندسين',
      'dokki': 'الدقي',
      'agouza': 'العجوزة',
      'nasr': 'نصر',
      'rehab': 'الرحاب',
      'october': 'أكتوبر',
      'abbassia': 'العباسية',
      'tahrir': 'التحرير',
      'ramses': 'رمسيس',
      'nile': 'النيل',
    };
    
    List<String> results = [];
    String lowercaseQuery = query.toLowerCase();
    
    // Direct mapping for common place names
    if (transliterationMap.containsKey(lowercaseQuery)) {
      results.add(transliterationMap[lowercaseQuery]!);
    }
    
    // Check for partial matches in the query
    for (var entry in transliterationMap.entries) {
      if (lowercaseQuery.contains(entry.key)) {
        String arabicVersion = lowercaseQuery.replaceAll(entry.key, entry.value);
        results.add(arabicVersion);
      }
    }
    
    // Add original query to ensure we try both
    if (results.isEmpty) {
      results.add(query);
    }
    
    return results;
  }

  // Filter locations to prioritize Egypt
  Future<List<Location>> _filterEgyptLocations(List<Location> locations) async {
    if (locations.isEmpty) return locations;
    
    List<Location> egyptLocations = [];
    List<Location> otherLocations = [];
    
    for (var location in locations) {
      try {
        final placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );
        
        if (placemarks.isNotEmpty) {
          final country = placemarks.first.country?.toLowerCase() ?? '';
          // Expanded check for Egypt in various forms
          if (country.contains('egypt') || 
              country == 'مصر' || 
              country == 'eg' || 
              country == 'egy' ||
              placemarks.first.isoCountryCode?.toLowerCase() == 'eg') {
            egyptLocations.add(location);
          } else {
            otherLocations.add(location);
          }
        } else {
          otherLocations.add(location);
        }
      } catch (e) {
        // If there's an error, just add to other locations
        otherLocations.add(location);
      }
    }
    
    // Return Egypt locations if available, otherwise return all
    return egyptLocations.isNotEmpty ? egyptLocations : otherLocations;
  }

  void _showSearchResults() {
    _removeOverlay();

    if (_searchResults.isEmpty) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width - 32, // 16px padding on each side
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(16, 65), // Offset below search bar
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8.0),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              constraints: const BoxConstraints(
                maxHeight: 300,
              ),
              child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: _searchResults.length,
                separatorBuilder: (context, index) => 
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade200),
                itemBuilder: (context, index) {
                  final result = _searchResults[index];
                  
                  // Extract address parts for better display
                  final String fullAddress = result['address'];
                  String mainText = fullAddress;
                  String secondaryText = '';
                  
                  // Try to split address into primary and secondary parts for better display
                  final addressParts = fullAddress.split(', ');
                  if (addressParts.length > 1) {
                    // First part is the main text (usually the most specific location)
                    mainText = addressParts[0];
                    
                    // Rest is secondary text (area, city, country)
                    secondaryText = addressParts.sublist(1).join(', ');
                  }
                  
                  // Check if this is a fallback/approximate location
                  bool isApproximate = fullAddress.toLowerCase().contains('(approximate');

                  return ListTile(
                    dense: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: isApproximate
                            ? Colors.amber.withOpacity(0.2)
                            : AppColors.primaryColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isApproximate ? Icons.place : Icons.location_on, 
                        color: isApproximate ? Colors.amber.shade800 : AppColors.primaryColor,
                      ),
                    ),
                    title: Text(
                      mainText,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: secondaryText.isNotEmpty
                      ? Text(
                          secondaryText,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        )
                      : null,
                    onTap: () {
                      _moveToLocation(result['location']);
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Future<void> _searchLocation(String query) async {
    // Save the query for potential retry
    _lastSearchQuery = query;
    
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
        _searchError = null;
      });
      _removeOverlay();
      return;
    }

    // Cancel previous debounce timer
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    // Set up new debounce timer
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      if (!mounted) return;
      
      setState(() {
        _isSearching = true;
        _searchError = null;
        _searchResults = []; // Clear previous results while searching
      });
      _removeOverlay();
      
      // Try with Egypt first if not already specified
      bool hasEgyptReference = _containsEgyptReference(query);
      if (!hasEgyptReference) {
        query = "$query, Egypt";
        print('Auto-appending Egypt to search query: $query');
      }

      try {
        print('Searching for location: $query');
        
        // Try different variations of the search query with Egypt prioritization
        List<Location> locations = await _trySearchWithVariations(query);
        print('Found ${locations.length} locations');
        
        // Filter to prioritize Egypt locations
        if (locations.isNotEmpty) {
          locations = await _filterEgyptLocations(locations);
          print('After filtering for Egypt: ${locations.length} locations');
        }
        
        if (locations.isEmpty) {
          // Try fallback location for common places
          final fallbackLocation = await _getFallbackLocation(_lastSearchQuery);
          
          if (fallbackLocation != null) {
            print('Using fallback location for: $_lastSearchQuery');
            // Create a single result with the fallback
            if (mounted) {
              setState(() {
                _searchResults = [{
                  'address': '$_lastSearchQuery (Approximate location)',
                  'location': fallbackLocation,
                }];
                _isSearching = false;
              });
              print('Set 1 fallback result in state');
              _showSearchResults();
            }
            return;
          }
          
          if (mounted) {
            setState(() {
              _searchResults = [];
              _isSearching = false;
              _searchError = 'No results found for "$_lastSearchQuery". Try a more specific search or use current location.';
            });
            print('No results found, set error');
          }
          return;
        }
        
        List<Map<String, dynamic>> results = [];
        Map<String, bool> uniqueAddresses = {};

        for (var location in locations) {
          try {
            final placemarks = await placemarkFromCoordinates(
              location.latitude,
              location.longitude,
            );

            if (placemarks.isNotEmpty) {
              for (var place in placemarks) {
                // Build a better formatted address with more context and Arabic support
                String address = _buildBetterFormattedAddress(place);
                
                // Skip duplicate addresses
                if (uniqueAddresses.containsKey(address)) continue;
                uniqueAddresses[address] = true;
                
                // Add location with better formatting
                results.add({
                  'address': address,
                  'location': LatLng(location.latitude, location.longitude),
                  // Add additional metadata for sorting/filtering
                  'metadata': {
                    'isInEgypt': _isLocationInEgypt(place),
                    'fullStreet': place.street,
                    'fullLocality': place.locality,
                    'adminArea': place.administrativeArea,
                    'subAdminArea': place.subAdministrativeArea,
                    'country': place.country,
                  }
                });
                
                // Limit to prevent overwhelmingly long lists
                if (results.length >= 10) break;
              }
            }
            
            if (results.length >= 10) break; // Overall limit
          } catch (e) {
            print('Error getting placemark for location: $e');
            // Still add the location with coordinates as address
            String address = '${location.latitude}, ${location.longitude}';
            if (!uniqueAddresses.containsKey(address)) {
              uniqueAddresses[address] = true;
              results.add({
                'address': address,
                'location': LatLng(location.latitude, location.longitude),
                'metadata': {
                  'isInEgypt': false, // Unknown, so assume false
                }
              });
            }
          }
        }

        // Sort the results to prioritize Egyptian locations
        results.sort((a, b) {
          // First priority: Is the location in Egypt
          bool aInEgypt = a['metadata']['isInEgypt'] ?? false;
          bool bInEgypt = b['metadata']['isInEgypt'] ?? false;
          
          if (aInEgypt && !bInEgypt) return -1;
          if (!aInEgypt && bInEgypt) return 1;
          
          // If both are in Egypt or both are not, sort by how well the query matches the address
          String aAddress = a['address'].toLowerCase();
          String bAddress = b['address'].toLowerCase();
          String lowerQuery = _lastSearchQuery.toLowerCase();
          
          // Check if address starts with the query
          bool aStartsWithQuery = aAddress.startsWith(lowerQuery);
          bool bStartsWithQuery = bAddress.startsWith(lowerQuery);
          
          if (aStartsWithQuery && !bStartsWithQuery) return -1;
          if (!aStartsWithQuery && bStartsWithQuery) return 1;
          
          // Check if address contains the query
          bool aContainsQuery = aAddress.contains(lowerQuery);
          bool bContainsQuery = bAddress.contains(lowerQuery);
          
          if (aContainsQuery && !bContainsQuery) return -1;
          if (!aContainsQuery && bContainsQuery) return 1;
          
          // If still tied, prioritize shorter addresses which are likely more specific
          return aAddress.length - bAddress.length;
        });

        print('Processed ${results.length} results with addresses');
        
        if (mounted) {
          setState(() {
            _searchResults = results;
            _isSearching = false;
          });
          print('Set search results in state: ${_searchResults.length}');
          _showSearchResults();
        }
      } catch (e) {
        print('Error searching location: $e');
        if (mounted) {
          setState(() {
            _searchResults = [];
            _isSearching = false;
            _searchError = 'Error searching: ${e.toString().split(':').last.trim()}';
          });
        }
      }
    });
  }
  
  // Helper method to build a better formatted address
  String _buildBetterFormattedAddress(Placemark place) {
    List<String> addressParts = [];
    
    // Start with most specific and go to most general
    if (place.name != null && place.name!.isNotEmpty && place.name != place.street) {
      addressParts.add(place.name!);
    }
    
    if (place.street != null && place.street!.isNotEmpty) {
      addressParts.add(place.street!);
    }
    
    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }
    
    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }
    
    if (place.subAdministrativeArea != null && place.subAdministrativeArea!.isNotEmpty && 
        place.subAdministrativeArea != place.locality) {
      addressParts.add(place.subAdministrativeArea!);
    }
    
    if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }
    
    if (place.country != null && place.country!.isNotEmpty) {
      addressParts.add(place.country!);
    }
    
    // Remove duplicates while preserving order
    List<String> uniqueAddressParts = [];
    for (var part in addressParts) {
      if (!uniqueAddressParts.contains(part)) {
        uniqueAddressParts.add(part);
      }
    }
    
    if (uniqueAddressParts.isEmpty) {
      return 'Unknown location';
    }
    
    return uniqueAddressParts.join(', ');
  }
  
  // Helper to check if a placemark is in Egypt
  bool _isLocationInEgypt(Placemark place) {
    final country = place.country?.toLowerCase() ?? '';
    final isoCode = place.isoCountryCode?.toLowerCase() ?? '';
    
    return country.contains('egypt') || 
           country == 'مصر' || 
           isoCode == 'eg' || 
           isoCode == 'egy';
  }

  void _moveToLocation(LatLng location) {
    _searchFocusNode.unfocus();
    _removeOverlay();
    _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: location,
          zoom: 15.0,
        ),
      ),
    );

    setState(() {
      _selectedLocation = location;
      _searchResults = [];
      _searchController.clear();
      _searchError = null;
    });
    
    _getAddress(location);
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Hide keyboard
      _searchFocusNode.unfocus();
      _removeOverlay();
      
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permissions are denied')),
          );
          return;
        }
      }
      
      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location permissions are permanently denied, we cannot request permissions.'),
          ),
        );
        return;
      }
      
      // Show loading indicator
      setState(() {
        _isSearching = true;
      });
      
      // Get current position
      final Position position = await Geolocator.getCurrentPosition();
      
      // Update the map
      _moveToLocation(LatLng(position.latitude, position.longitude));
      
      // Hide loading indicator
      setState(() {
        _isSearching = false;
      });
    } catch (e) {
      print('Error getting current location: $e');
      setState(() {
        _isSearching = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not get current location: $e')),
      );
    }
  }

  void _confirmLocation() async {
    if (_address == null) {
      await _getAddress(_selectedLocation);
    }
    Navigator.pop(context, {
      'location': _selectedLocation,
      'address': _address,
    });
  }

  void _retrySearch() {
    if (_lastSearchQuery.isNotEmpty) {
      _searchLocation(_lastSearchQuery);
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.pickedLocation != null) {
      setState(() {
        _selectedLocation = widget.pickedLocation!;
      });
      _getAddress(_selectedLocation); // Fetch address for the initial location
    }

    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus) {
        // When search loses focus, remove the overlay after a short delay
        // to allow for interactions with the dropdown
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted && !_searchFocusNode.hasFocus) {
            _removeOverlay();
          }
        });
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _searchResults = [];
      _searchController.clear();
      _searchError = null;
    });
    _removeOverlay();
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _removeOverlay();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('Building with ${_searchResults.length} search results');
    return ANScaffold(
      key: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
        disableBottomNavBar: true,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: true,
      ),
      appbar: ANMainScreenAppbar(
        title: Text(S.of(context).kPickALocation),
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: _confirmLocation,
          ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          // Close keyboard when tapping outside text field
          FocusScope.of(context).unfocus();
          _removeOverlay();
        },
        child: Stack(
          children: [
            Column(
              children: [
                // Search bar
                Material(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: CompositedTransformTarget(
                      link: _layerLink,
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _searchController,
                              focusNode: _searchFocusNode,
                              decoration: InputDecoration(
                                hintText: S.of(context).kSearchForLocation,
                                contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                  borderSide: BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                  borderSide: BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                  borderSide: const BorderSide(color: AppColors.primaryColor),
                                ),
                                prefixIcon: const Icon(Icons.search, color: AppColors.primaryColor),
                                suffixIcon: _searchController.text.isNotEmpty
                                    ? IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: _clearSearch,
                                      )
                                    : null,
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              onChanged: _searchLocation,
                              onTap: () {
                                if (_searchResults.isNotEmpty) {
                                  _showSearchResults();
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          InkWell(
                            borderRadius: BorderRadius.circular(8.0),
                            onTap: _getCurrentLocation,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.primaryColor,
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              child: const Icon(
                                Icons.my_location,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // Error message
                if (_searchError != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Text(
                            _searchError!,
                            style: TextStyle(color: Colors.red.shade800),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextButton.icon(
                                icon: const Icon(Icons.refresh, size: 16),
                                label: const Text('Try Again'),
                                onPressed: _retrySearch,
                                style: TextButton.styleFrom(
                                  foregroundColor: AppColors.primaryColor,
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  minimumSize: const Size(0, 32),
                                ),
                              ),
                              TextButton.icon(
                                icon: const Icon(Icons.my_location, size: 16),
                                label: const Text('Use Current Location'),
                                onPressed: _getCurrentLocation,
                                style: TextButton.styleFrom(
                                  foregroundColor: AppColors.primaryColor,
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  minimumSize: const Size(0, 32),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                
                // Map - takes up remaining space
                Expanded(
                  child: Stack(
                    children: [
                      GoogleMap(
                        onMapCreated: _onMapCreated,
                        mapType: MapType.normal,
                        initialCameraPosition: CameraPosition(
                          target: _selectedLocation,
                          zoom: 14.0,
                        ),
                        markers: {
                          Marker(
                            markerId: const MarkerId("selected-location"),
                            position: _selectedLocation,
                          ),
                        },
                        onTap: _onTap,
                      ),
                      
                      // Selected address display at bottom
                      if (_address != null)
                        Positioned(
                          bottom: 16.0,
                          left: 16.0,
                          right: 16.0,
                          child: Container(
                            padding: const EdgeInsets.all(12.0),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8.0),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 4.0,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  'Selected Location:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12.0,
                                    color: Colors.grey,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _address!,
                                  style: const TextStyle(
                                    fontSize: 14.0,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primaryColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8.0),
                                      ),
                                    ),
                                    onPressed: _confirmLocation,
                                    child: const Text('Use This Location'),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Loading indicator
            if (_isSearching)
              Container(
                color: Colors.black.withOpacity(0.1),
                alignment: Alignment.center,
                child: const CircularProgressIndicator(
                  color: AppColors.primaryColor,
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  // Special function to get fallback coordinates for a place with focus on Egypt
  Future<LatLng?> _getFallbackLocation(String query) async {
    // Normalize and clean the query for better matching
    String normalizedQuery = query.toLowerCase().trim()
      .replaceAll(RegExp(r'[^\w\s]'), '') // Remove special chars
      .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace

    // Expanded fallbacks for Egyptian locations
    final fallbacks = {
      // Egyptian Governorates
      'cairo': const LatLng(30.0444, 31.2357),
      'alexandria': const LatLng(31.2001, 29.9187),
      'giza': const LatLng(30.0131, 31.2089),
      'luxor': const LatLng(25.6872, 32.6396),
      'aswan': const LatLng(24.0889, 32.8998),
      'hurghada': const LatLng(27.2579, 33.8116),
      'sharm': const LatLng(27.9158, 34.3300),
      'dahab': const LatLng(28.5091, 34.5136),
      'marsa alam': const LatLng(25.0676, 34.8790),
      'port said': const LatLng(31.2560, 32.2841),
      'suez': const LatLng(29.9737, 32.5263),
      'ismailia': const LatLng(30.6043, 32.2723),
      'fayoum': const LatLng(29.3085, 30.8428),
      'minya': const LatLng(28.1099, 30.7503),
      'sohag': const LatLng(26.5586, 31.6957),
      'assiut': const LatLng(27.1809, 31.1845),
      'damietta': const LatLng(31.4175, 31.8144),
      'mansoura': const LatLng(31.0409, 31.3785),
      'tanta': const LatLng(30.7865, 31.0004),
      'beni suef': const LatLng(29.0661, 31.0994),
      'al minya': const LatLng(28.0871, 30.7618),
      'qena': const LatLng(26.1551, 32.7165),
      'kafr el sheikh': const LatLng(31.1097, 30.9395),
      'al beheira': const LatLng(30.8481, 30.3436),
      'sharqia': const LatLng(30.7248, 31.7555),
      'al gharbiya': const LatLng(30.8754, 31.0335),
      'al dakahlia': const LatLng(31.1656, 31.6497),
      'beni suef': const LatLng(29.0713, 31.0932),
      'new valley': const LatLng(25.4476, 30.5546),
      'al qalyubia': const LatLng(30.1286, 31.2422),
      'red sea': const LatLng(27.2579, 33.8116), // Hurghada as representative
      'matrouh': const LatLng(31.3543, 27.2373),
      'south sinai': const LatLng(28.2333, 33.6167),
      'north sinai': const LatLng(30.5000, 33.8333),

      // Greater Cairo Districts and Neighborhoods
      'nasr city': const LatLng(30.0510, 31.3460),
      'new cairo': const LatLng(30.0074, 31.4913),
      'fifth settlement': const LatLng(30.0219, 31.4871), // Fifth Settlement in New Cairo
      'first settlement': const LatLng(30.0617, 31.4444), // First Settlement in New Cairo
      '6 october': const LatLng(29.9285, 30.9188),
      '6th october': const LatLng(29.9285, 30.9188),
      'sixth october': const LatLng(29.9285, 30.9188),
      'maadi': const LatLng(29.9559, 31.2486),
      'heliopolis': const LatLng(30.0911, 31.3425),
      'zamalek': const LatLng(30.0571, 31.2272),
      'downtown': const LatLng(30.0454, 31.2335),
      'rehab': const LatLng(30.0620, 31.4930),
      'shorouk': const LatLng(30.1395, 31.6307),
      'obour': const LatLng(30.2283, 31.4518),
      'sheikh zayed': const LatLng(30.0141, 30.9633),
      'mohandessin': const LatLng(30.0566, 31.2014),
      'dokki': const LatLng(30.0381, 31.2122),
      'agouza': const LatLng(30.0530, 31.2055),
      'haram': const LatLng(29.9914, 31.1423),
      'pyramids': const LatLng(29.9792, 31.1342),
      'garden city': const LatLng(30.0371, 31.2272),
      'abbassia': const LatLng(30.0695, 31.2840),
      'ain shams': const LatLng(30.1288, 31.3194),
      'tagamoa': const LatLng(30.0219, 31.4871), // Alternative name for Fifth Settlement
      'moqattam': const LatLng(30.0306, 31.3230),
      'mokattam': const LatLng(30.0306, 31.3230),
      'shubra': const LatLng(30.1154, 31.2442),
      'ramses': const LatLng(30.0626, 31.2497),
      'abdin': const LatLng(30.0440, 31.2488),
      'bolaq': const LatLng(30.0549, 31.2321),
      'helwan': const LatLng(29.8500, 31.3333),
      'imbaba': const LatLng(30.0792, 31.2113),
      'hadayek el kobba': const LatLng(30.0805, 31.3008),
      'hadayek el ahram': const LatLng(29.9977, 31.1295),
      'masr el gedida': const LatLng(30.0911, 31.3425), // Alternative name for Heliopolis
      'badr city': const LatLng(30.1397, 31.7357),
      'smart village': const LatLng(30.0742, 30.9734),
      'katameya': const LatLng(29.9916, 31.3900),
      
      // Major Landmarks and Areas
      'tahrir square': const LatLng(30.0444, 31.2357),
      'cairo tower': const LatLng(30.0459, 31.2243),
      'city stars': const LatLng(30.0728, 31.3442), // City Stars Mall
      'mall of arabia': const LatLng(29.9738, 30.9826),
      'cairo festival': const LatLng(30.0262, 31.4621), // Cairo Festival City Mall
      'grand egyptian museum': const LatLng(29.9947, 31.1167),
      'egyptian museum': const LatLng(30.0276, 31.2337),
      'khan el khalili': const LatLng(30.0470, 31.2625),
      'al azhar': const LatLng(30.0434, 31.2622), // Al-Azhar Mosque
      'coptic cairo': const LatLng(30.0058, 31.2301),
      'cairo international stadium': const LatLng(30.0719, 31.3187),
      'media city': const LatLng(29.9694, 30.9381), // 6th October Media City
      'cairo airport': const LatLng(30.1119, 31.4006),
      'citadel': const LatLng(30.0286, 31.2598), // Cairo Citadel
      'corniche': const LatLng(30.0371, 31.2243), // Nile Corniche
      'rod al farag': const LatLng(30.0892, 31.2296), // Axis/Bridge
      'gezira': const LatLng(30.0438, 31.2236), // Gezira Island
      'auc': const LatLng(30.0191, 31.4989), // American University in Cairo
      'guc': const LatLng(29.9701, 31.4416), // German University in Cairo
      'bue': const LatLng(30.0791, 31.0106), // British University in Egypt
      'miu': const LatLng(29.9687, 30.9419), // Misr International University
      'must': const LatLng(29.9658, 31.2514), // Misr University for Science and Technology
      'nile university': const LatLng(29.9981, 31.0164),
      'cairo university': const LatLng(30.0259, 31.2093),
      'ain shams university': const LatLng(30.0616, 31.2861),
      'nile ritz': const LatLng(30.0454, 31.2298), // Nile Ritz-Carlton
      'four seasons': const LatLng(30.0448, 31.2241), // Four Seasons Nile Plaza
      'marriott': const LatLng(30.0575, 31.2251), // Marriott Zamalek
      
      // Other major cities in Egypt
      'alexandria corniche': const LatLng(31.2088, 29.8856),
      'bibliotheca alexandria': const LatLng(31.2089, 29.9092),
      'montazah': const LatLng(31.2877, 29.9830), // Montazah Palace
      'al alamein': const LatLng(30.8326, 28.9527),
      'siwa': const LatLng(29.2031, 25.5196),
      'saint catherine': const LatLng(28.5552, 33.9482),
      'ras muhammad': const LatLng(27.7338, 34.2139), // National Park
      'nabq bay': const LatLng(28.0564, 34.4352),
      'el gouna': const LatLng(27.3974, 33.6746),
      'soma bay': const LatLng(26.8464, 33.9861),
      'sahl hasheesh': const LatLng(27.0029, 33.8600),
      'makadi bay': const LatLng(26.9902, 33.8897),
      'abu tig marina': const LatLng(27.2513, 33.8424), // El Gouna marina

      // International locations last (for fallback if nothing matches in Egypt)
      'america': const LatLng(37.0902, -95.7129), // Center of USA
      'london': const LatLng(51.5074, -0.1278),
      'paris': const LatLng(48.8566, 2.3522),
      'dubai': const LatLng(25.2048, 55.2708),
      'riyadh': const LatLng(24.7136, 46.6753),
      'jeddah': const LatLng(21.4858, 39.1925),
    };
    
    // First check for exact matches
    for (var entry in fallbacks.entries) {
      if (normalizedQuery == entry.key || 
          normalizedQuery == entry.key.replaceAll(' ', '')) {
        return entry.value;
      }
    }
    
    // Then check for prefix matches (query starts with key)
    for (var entry in fallbacks.entries) {
      if (normalizedQuery.startsWith('${entry.key} ') || 
          normalizedQuery.startsWith('${entry.key},')) {
        return entry.value;
      }
    }
    
    // Then check for partial matches with Egyptian locations
    List<MapEntry<String, LatLng>> egyptMatches = [];
    List<MapEntry<String, LatLng>> otherMatches = [];
    
    for (var entry in fallbacks.entries) {
      // Skip the international locations for this check
      if (entry.key == 'america' || entry.key == 'london' || 
          entry.key == 'paris' || entry.key == 'dubai' ||
          entry.key == 'riyadh' || entry.key == 'jeddah') {
        if (normalizedQuery.contains(entry.key)) {
          otherMatches.add(entry);
        }
        continue;
      }
      
      // Check if normalized query contains the key or key contains query
      if (normalizedQuery.contains(entry.key) || entry.key.contains(normalizedQuery)) {
        egyptMatches.add(entry);
      }
    }
    
    // Prefer Egyptian matches
    if (egyptMatches.isNotEmpty) {
      // If we have multiple matches, pick the one with closest length to query
      if (egyptMatches.length > 1) {
        egyptMatches.sort((a, b) {
          return (a.key.length - normalizedQuery.length).abs() - 
                 (b.key.length - normalizedQuery.length).abs();
        });
      }
      return egyptMatches.first.value;
    }
    
    // Use international match if available
    if (otherMatches.isNotEmpty) {
      return otherMatches.first.value;
    }
    
    // Special handling for Arabic transliteration
    for (var entry in fallbacks.entries) {
      // Try removing 'al' prefix which might be transliterated differently
      if (normalizedQuery.startsWith('al ') || normalizedQuery.startsWith('el ')) {
        var withoutPrefix = normalizedQuery.substring(3);
        if (entry.key == withoutPrefix) {
          return entry.value;
        }
      }
    }
    
    // If no match found, default to Cairo center
    return const LatLng(30.0444, 31.2357); // Default to Cairo if no match found
  }
}