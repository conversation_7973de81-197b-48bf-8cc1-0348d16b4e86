import 'dart:convert';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/availability/date_specific_availability.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/date_specific_availability_repository.dart';
import 'package:ajmal_now_doctor/features/services/presentation/date_specific_slots_editor.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

class DateSpecificSchedule extends ConsumerStatefulWidget {
  final String serviceProviderId;
  final String serviceId;
  final Function(List<ANSlotsModel>, DateTime, bool) onDateScheduleChanged;

  const DateSpecificSchedule({
    Key? key,
    required this.serviceProviderId,
    required this.serviceId,
    required this.onDateScheduleChanged,
  }) : super(key: key);

  @override
  ConsumerState<DateSpecificSchedule> createState() => _DateSpecificScheduleState();
}

class _DateSpecificScheduleState extends ConsumerState<DateSpecificSchedule> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  int _currentYear = DateTime.now().year;
  int _currentMonth = DateTime.now().month; // 1-based month (DateTime.month is already 1-based)
  
  // List of dates that have specific availability
  List<ANAvailabilityDateInfo> _availabilityInfo = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMonthlyAvailability();
  }

  Future<void> _loadMonthlyAvailability() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final availabilityInfo = await ref.read(monthlyAvailabilityProvider({
        'providerId': widget.serviceProviderId,
        'serviceId': widget.serviceId,
        'year': _currentYear,
        'month': _currentMonth,
      }).future);
      
      setState(() {
        _availabilityInfo = availabilityInfo;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading monthly availability: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
    });
  }

  void _onFormatChanged(CalendarFormat format) {
    setState(() {
      _calendarFormat = format;
    });
  }

  void _onPageChanged(DateTime focusedDay) {
    // Month changed
    setState(() {
      _focusedDay = focusedDay;
      _currentYear = focusedDay.year;
      _currentMonth = focusedDay.month;
    });
    _loadMonthlyAvailability();
  }

  // Check if a date has specific availability
  bool _hasAvailability(DateTime day) {
    return _availabilityInfo.any((info) => 
      isSameDay(info.date, day) && info.hasSlots && info.isAvailable);
  }
  
  // Check if a date is marked unavailable
  bool _isUnavailable(DateTime day) {
    return _availabilityInfo.any((info) => 
      isSameDay(info.date, day) && !info.isAvailable);
  }
  
  // Helper function to compare dates
  bool isSameDay(DateTime? a, DateTime? b) {
    if (a == null || b == null) {
      return false;
    }
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Information text
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    S.of(context).kCalendarScheduleInfo,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                S.of(context).kSelectDayToSetAvailabilityDescription,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 24),
        
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator(color: AppColors.primaryColor),
          )
        else
          Column(
            children: [
              // Calendar legend
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildLegendItem(
                      Colors.green.withOpacity(0.8), 
                      S.of(context).kAvailableDays
                    ),
                    _buildLegendItem(
                      Colors.red.withOpacity(0.8), 
                      S.of(context).kUnavailableDays
                    ),
                    _buildLegendItem(
                      Colors.grey.withOpacity(0.5), 
                      S.of(context).kNotSetDays
                    ),
                  ],
                ),
              ),
              
              // Calendar container with shadow
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TableCalendar(
                  firstDay: DateTime.now(),
                  lastDay: DateTime.now().add(const Duration(days: 365)),
                  focusedDay: _focusedDay,
                  calendarFormat: _calendarFormat,
                  selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                  onDaySelected: _onDaySelected,
                  onFormatChanged: _onFormatChanged,
                  onPageChanged: _onPageChanged,
                  calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    selectedDecoration: const BoxDecoration(
                      color: AppColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: AppColors.primaryColor.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    markerDecoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                  calendarBuilders: CalendarBuilders(
                    defaultBuilder: (context, day, focusedDay) {
                      if (_hasAvailability(day)) {
                        return Container(
                          margin: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.2),
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.green, width: 1),
                          ),
                          child: Center(
                            child: Text(
                              '${day.day}',
                              style: const TextStyle(color: Colors.black),
                            ),
                          ),
                        );
                      } else if (_isUnavailable(day)) {
                        return Container(
                          margin: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.2),
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.red, width: 1),
                          ),
                          child: Center(
                            child: Text(
                              '${day.day}',
                              style: const TextStyle(color: Colors.black),
                            ),
                          ),
                        );
                      }
                      return null;
                    },
                  ),
                ),
              ),
            ],
          ),
          
        const SizedBox(height: 24),
          
        // Add/Edit buttons
        if (_selectedDay != null) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  DateFormat.yMMMMEEEEd().format(_selectedDay!),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ANElevatedButton(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _hasAvailability(_selectedDay!)
                                  ? Icons.edit
                                  : Icons.check_circle,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _hasAvailability(_selectedDay!)
                                  ? S.of(context).kEditAvailability
                                  : S.of(context).kSetAsAvailable,
                            ),
                          ],
                        ),
                        backgroundColor: AppColors.primaryColor,
                        onPressed: _editDayAvailability,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ANElevatedButton(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _isUnavailable(_selectedDay!)
                                  ? Icons.edit
                                  : Icons.block,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _isUnavailable(_selectedDay!)
                                  ? S.of(context).kEditUnavailability
                                  : S.of(context).kSetAsUnavailable,
                            ),
                          ],
                        ),
                        backgroundColor: Colors.orange,
                        onPressed: _markAsUnavailable,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ] else ...[
          Center(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                S.of(context).kPleaseSelectDateFirst,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
  
  Widget _buildLegendItem(Color color, String text) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(text, style: const TextStyle(fontSize: 12)),
      ],
    );
  }
  
  void _editDayAvailability() async {
    if (_selectedDay == null) return;
    
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DateSpecificSlotsEditor(
          date: _selectedDay!,
          serviceProviderId: widget.serviceProviderId,
          serviceId: widget.serviceId,
          isAvailable: true,
        ),
      ),
    );
    
    if (result != null) {
      setState(() {
        // Add or update the availability info locally
        final existingIndex = _availabilityInfo.indexWhere(
          (info) => isSameDay(info.date, _selectedDay));
        
        if (existingIndex >= 0) {
          _availabilityInfo[existingIndex] = ANAvailabilityDateInfo(
            date: _selectedDay!,
            hasSlots: (result['slots'] as List).isNotEmpty,
            isAvailable: true, // Always true now
          );
        } else {
          _availabilityInfo.add(ANAvailabilityDateInfo(
            date: _selectedDay!,
            hasSlots: (result['slots'] as List).isNotEmpty,
            isAvailable: true, // Always true now
          ));
        }
      });
      
      widget.onDateScheduleChanged(
        result['slots'],
        _selectedDay!,
        true, // Always true now
      );
    }
  }
  
  void _markAsUnavailable() async {
    if (_selectedDay == null) return;
    
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DateSpecificSlotsEditor(
          date: _selectedDay!,
          serviceProviderId: widget.serviceProviderId,
          serviceId: widget.serviceId,
          isAvailable: false,
        ),
      ),
    );
    
    if (result != null) {
      setState(() {
        // Remove the availability info entirely when marked as unavailable
        final existingIndex = _availabilityInfo.indexWhere(
          (info) => isSameDay(info.date, _selectedDay));
        
        if (existingIndex >= 0) {
          // Remove the entry entirely to remove the green circle
          _availabilityInfo.removeAt(existingIndex);
        }
      });
      
      widget.onDateScheduleChanged(
        [], // Empty slots
        _selectedDay!,
        result['isAvailable'] ?? false, // Use the actual availability status from the editor
      );
    }
  }
} 