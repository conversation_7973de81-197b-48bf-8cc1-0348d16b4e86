import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';

class ScheduleSelector extends StatefulWidget {
  final List<ANScheduleModel> schedules;
  final Function(List<ANScheduleModel>) onSchedulesChanged;
  final bool? hasError;

  const ScheduleSelector({
    Key? key,
    required this.schedules,
    required this.onSchedulesChanged,
    this.hasError,
  }) : super(key: key);

  @override
  State<ScheduleSelector> createState() => _ScheduleSelectorState();
}

class _ScheduleSelectorState extends State<ScheduleSelector> {
  void _handleDaySelection(String day, List<String> days, bool selected) {
    List<ANScheduleModel> newSchedules = List.from(widget.schedules);
    if (selected) {
      newSchedules.add(
        ANScheduleModel(
          id: days.indexOf(day).toString(),
          day: Weekday.values[(days.indexOf(day) + 1) % 7],
          slots: [
            ANSlotsModel(
              id: newSchedules.length.toString(),
              starts: DateTime.now(),
              ends: DateTime.now(),
            ),
          ],
        ),
      );
    } else {
      newSchedules.removeWhere((element) =>
          element.day == Weekday.values[(days.indexOf(day) + 1) % 7]);
    }
    widget.onSchedulesChanged(newSchedules);
  }

  void _handleSlotTimeUpdate(
      ANScheduleModel schedule, ANSlotsModel slot, ANSlotsModel updatedSlot) {
    List<ANScheduleModel> newSchedules = List.from(widget.schedules);
    int scheduleIndex = newSchedules.indexWhere((s) => s.id == schedule.id);

    if (scheduleIndex != -1) {
      List<ANSlotsModel> newSlots =
          List.from(newSchedules[scheduleIndex].slots);
      int slotIndex = newSlots.indexWhere((s) => s.id == slot.id);

      if (slotIndex != -1) {
        newSlots[slotIndex] = updatedSlot;
        newSchedules[scheduleIndex] = ANScheduleModel(
          id: schedule.id,
          day: schedule.day,
          slots: newSlots,
        );
        widget.onSchedulesChanged(newSchedules);
      }
    }
  }

  void _handleAddSlot(ANScheduleModel schedule) {
    List<ANScheduleModel> newSchedules = List.from(widget.schedules);
    int scheduleIndex = newSchedules.indexWhere((s) => s.id == schedule.id);

    if (scheduleIndex != -1) {
      List<ANSlotsModel> newSlots =
          List.from(newSchedules[scheduleIndex].slots);
      newSlots.add(ANSlotsModel(
        id: (newSlots.length + 1).toString(),
        starts: DateTime.now(),
        ends: DateTime.now(),
      ));

      newSchedules[scheduleIndex] = ANScheduleModel(
        id: schedule.id,
        day: schedule.day,
        slots: newSlots,
      );
      widget.onSchedulesChanged(newSchedules);
    }
  }

  void _handleRemoveSlot(ANScheduleModel schedule, ANSlotsModel slot) {
    List<ANScheduleModel> newSchedules = List.from(widget.schedules);
    int scheduleIndex = newSchedules.indexWhere((s) => s.id == schedule.id);

    if (scheduleIndex != -1) {
      List<ANSlotsModel> newSlots = [];
      for (int i = 0; i < schedule.slots.length; i++) {
        if (schedule.slots[i].id != slot.id) {
          ANSlotsModel newSlot = ANSlotsModel(
            id: (newSlots.length + 1).toString(),
            starts: schedule.slots[i].starts,
            ends: schedule.slots[i].ends,
          );
          newSlots.add(newSlot);
        }
      }

      newSchedules[scheduleIndex] = ANScheduleModel(
        id: schedule.id,
        day: schedule.day,
        slots: newSlots,
      );
      widget.onSchedulesChanged(newSchedules);
    }
  }

  void _showTimePickerBottomSheet(
      BuildContext context, ANScheduleModel schedule, ANSlotsModel slot) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30.0)),
      ),
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        return _TimePickerSheet(
          slot: slot,
          onTimeUpdated: (updatedSlot) =>
              _handleSlotTimeUpdate(schedule, slot, updatedSlot),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isRtl = Directionality.of(context) == TextDirection.rtl;
    List<String> days = [
      S.of(context).kSun,
      S.of(context).kMon,
      S.of(context).kTue,
      S.of(context).kWed,
      S.of(context).kThu,
      S.of(context).kFri,
      S.of(context).kSat,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '* ${S.of(context).kScheduleDay}',
          style: TextStyle(
            color: AppColors.primaryColor,
            fontSize: TextSize.r.size,
          ),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children:
                days.map((day) => _buildDayChip(day, days, isRtl)).toList(),
          ),
        ),
        if (widget.hasError == true)
          Text(
            S.of(context).kSelectAtLeastOneDay,
            style: TextStyle(
              color: Colors.red,
              fontSize: TextSize.s.size,
            ),
          ),
        if (widget.schedules.isNotEmpty) ...[
          gapHXL,
          Text(
            S.of(context).kTime,
            style: TextStyle(
              color: AppColors.primaryColor,
              fontSize: TextSize.r.size,
            ),
          ),
          ...widget.schedules
              .map((schedule) => _buildScheduleRow(context, schedule)),
        ],
      ],
    );
  }

  Widget _buildDayChip(String day, List<String> days, bool isRtl) {
    return FilterChip(
      visualDensity: VisualDensity.compact,
      side: BorderSide(
        color: widget.hasError == false ? AppColors.primaryColor : Colors.red,
        width: 0.5,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: !isRtl
            ? BorderRadius.horizontal(
                left: Radius.circular(day == S.of(context).kSun ? 25.0 : 0),
                right: Radius.circular(day == S.of(context).kSat ? 25.0 : 0))
            : BorderRadius.horizontal(
                right: Radius.circular(day == S.of(context).kSun ? 25.0 : 0),
                left: Radius.circular(day == S.of(context).kSat ? 25.0 : 0),
              ),
      ),
      label: Text(
        day,
        style: TextStyle(
          color: widget.schedules.any((element) =>
                  element.day == Weekday.values[(days.indexOf(day) + 1) % 7])
              ? Colors.white
              : AppColors.primaryColor,
        ),
      ),
      selected: widget.schedules.any((element) =>
          element.day == Weekday.values[(days.indexOf(day) + 1) % 7]),
      showCheckmark: false,
      onSelected: (selected) => _handleDaySelection(day, days, selected),
      selectedColor: AppColors.primaryColor,
      backgroundColor: Colors.white,
    );
  }

  Widget _buildScheduleRow(BuildContext context, ANScheduleModel schedule) {
    return Row(
      children: [
        Text(
          '•   ${schedule.day.name[0].toUpperCase()}${schedule.day.name.substring(1)}',
          style: TextStyle(
            fontSize: TextSize.s.size,
            color: AppColors.primaryColor,
          ),
        ),
        const Spacer(),
        Column(
          children: schedule.slots
              .map((slot) => _buildTimeSlot(context, schedule, slot))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildTimeSlot(
      BuildContext context, ANScheduleModel schedule, ANSlotsModel slot) {
    // Ensure ends time is not null, default to 30 minutes after starts
    final endsTime = slot.ends ?? slot.starts.add(const Duration(minutes: 30));
    
    return Row(
      children: [
        GestureDetector(
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: AppColors.primaryColor,
                width: 1,
              ),
            ),
            child: Text(
              "${(slot.starts.hour % 12).toString().padLeft(2, '0')} : ${slot.starts.minute.toString().padLeft(2, '0')} ${slot.starts.hour >= 12 ? "PM" : "AM"} - ${(endsTime.hour % 12).toString().padLeft(2, '0')} : ${endsTime.minute.toString().padLeft(2, '0')} ${endsTime.hour >= 12 ? "PM" : "AM"}",
              style: TextStyle(
                color: Colors.purple,
                fontSize: TextSize.xs.size,
              ),
            ),
          ),
          onTap: () => _showTimePickerBottomSheet(context, schedule, slot),
        ),
        if (slot != schedule.slots.last) ...[
          gapHXL,
          IconButton(
            onPressed: () => _handleRemoveSlot(schedule, slot),
            icon: const Icon(
              Icons.remove,
              color: AppColors.primaryColor,
            ),
          ),
        ] else
          IconButton(
            onPressed: () => _handleAddSlot(schedule),
            icon: const Icon(
              Icons.add,
              color: AppColors.primaryColor,
            ),
          ),
      ],
    );
  }
}

class _TimePickerSheet extends StatefulWidget {
  final ANSlotsModel slot;
  final Function(ANSlotsModel) onTimeUpdated;

  const _TimePickerSheet({
    Key? key,
    required this.slot,
    required this.onTimeUpdated,
  }) : super(key: key);

  @override
  State<_TimePickerSheet> createState() => _TimePickerSheetState();
}

class _TimePickerSheetState extends State<_TimePickerSheet> {
  late ANSlotsModel selectedSlot;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    selectedSlot = widget.slot;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 100.0,
            height: 10.0,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: circularBorderL,
            ),
          ),
          gapHXL,
          DefaultTabController(
            length: 2,
            child: TabBar(
              onTap: (index) => setState(() => selectedIndex = index),
              splashBorderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25.0),
                topRight: Radius.circular(25.0),
              ),
              labelColor: AppColors.primaryColor,
              indicatorColor: AppColors.primaryColor,
              tabs: [
                Tab(text: S.of(context).kStartTime),
                Tab(text: S.of(context).kEndTime),
              ],
            ),
          ),
          SizedBox(
            height: 150.0,
            child: CupertinoDatePicker(
              key: ValueKey(selectedIndex),
              initialDateTime:
                  selectedIndex == 0 ? selectedSlot.starts : (selectedSlot.ends ?? selectedSlot.starts.add(const Duration(minutes: 30))),
              mode: CupertinoDatePickerMode.time,
              onDateTimeChanged: (DateTime newTime) {
                setState(() {
                  if (selectedIndex == 0) {
                    selectedSlot = ANSlotsModel(
                      id: selectedSlot.id,
                      starts: newTime,
                      ends: selectedSlot.ends,
                    );
                  } else {
                    selectedSlot = ANSlotsModel(
                      id: selectedSlot.id,
                      starts: selectedSlot.starts,
                      ends: newTime,
                    );
                  }
                });
              },
            ),
          ),
          gapHL,
          ANElevatedButton(
            onPressed: () {
              final endsTime = selectedSlot.ends ?? selectedSlot.starts.add(const Duration(minutes: 30));
              if (selectedSlot.starts.isBefore(endsTime)) {
                widget.onTimeUpdated(selectedSlot);
                Navigator.pop(context);
              } else {
                ANDialogHelper.gShowConfirmationDialog(
                  context: context,
                  message: S.of(context).kPleaseSelectAValidTimeSlot,
                  type: DialogType.confirm,
                );
              }
            },
            child: Text(
              S.of(context).kConfirm,
              style: const TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
