import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/presentation/date_specific_schedule.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';

class TabbedScheduleSelector extends StatefulWidget {
  // Keep these for backward compatibility, but we'll only use date-specific schedule
  final List<ANScheduleModel> weeklySchedules;
  final Function(List<ANScheduleModel>) onWeeklySchedulesChanged;
  final Function(List<ANSlotsModel>, DateTime, bool) onDateSpecificScheduleChanged;
  final String serviceProviderId;
  final String serviceId;
  final bool? hasError;

  const TabbedScheduleSelector({
    Key? key,
    required this.weeklySchedules,
    required this.onWeeklySchedulesChanged,
    required this.onDateSpecificScheduleChanged,
    required this.serviceProviderId,
    required this.serviceId,
    this.hasError,
  }) : super(key: key);

  @override
  State<TabbedScheduleSelector> createState() => _TabbedScheduleSelectorState();
}

class _TabbedScheduleSelectorState extends State<TabbedScheduleSelector> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '* ${S.of(context).kSchedule}',
          style: TextStyle(
            color: AppColors.primaryColor,
            fontSize: TextSize.r.size,
          ),
        ),
        gapHXS,
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(Icons.calendar_today, color: AppColors.primaryColor),
              const SizedBox(width: 8),
              Text(
                S.of(context).kDateSpecificHours,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: Paddings.l),
        // Only show Date-specific Schedule
        DateSpecificSchedule(
          serviceProviderId: widget.serviceProviderId,
          serviceId: widget.serviceId,
          onDateScheduleChanged: widget.onDateSpecificScheduleChanged,
        ),
      ],
    );
  }
} 