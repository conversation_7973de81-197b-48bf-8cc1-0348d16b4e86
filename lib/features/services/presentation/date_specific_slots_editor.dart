import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/date_specific_availability_repository.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/utils/timezone_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

class DateSpecificSlotsEditor extends ConsumerStatefulWidget {
  final DateTime date;
  final String serviceProviderId;
  final String serviceId;
  final bool isAvailable;

  const DateSpecificSlotsEditor({
    Key? key,
    required this.date,
    required this.serviceProviderId,
    required this.serviceId,
    required this.isAvailable,
  }) : super(key: key);

  @override
  ConsumerState<DateSpecificSlotsEditor> createState() => _DateSpecificSlotsEditorState();
}

class _DateSpecificSlotsEditorState extends ConsumerState<DateSpecificSlotsEditor> {
  List<ANSlotsModel> _slots = [];
  bool _isLoading = true;
  bool _isSaving = false;
  bool _isAvailable = true;

  @override
  void initState() {
    super.initState();
    _isAvailable = widget.isAvailable;
    _loadExistingAvailability();
  }

  Future<void> _loadExistingAvailability() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print("=== LOADING AVAILABILITY DEBUG ===");
      print("Service Provider ID: ${widget.serviceProviderId}");
      print("Service ID: ${widget.serviceId}");
      print("Date: ${widget.date.toIso8601String()}");
      
      final data = await ref.read(dateSpecificAvailabilityDataProvider({
        'providerId': widget.serviceProviderId,
        'serviceId': widget.serviceId,
        'date': widget.date.toIso8601String(),
      }).future);

      print("Raw server response: $data");
      
      if (data['exists'] == true) {
        final availability = data['availability'];
        final List<dynamic> slotsData = availability['slots'] ?? [];
        
        print("Found ${slotsData.length} slots in server response");
        
        for (int i = 0; i < slotsData.length; i++) {
          print("Raw slot $i data: ${slotsData[i]}");
        }

        setState(() {
          _slots = slotsData.map((slotData) {
            final slot = ANSlotsModel.fromJson(slotData);
            print("Parsed slot - Local time: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(slot.starts)}");
            return slot;
          }).toList();
          
          // Sort slots by start time for better UX
          _slots.sort((a, b) => a.starts.compareTo(b.starts));
          
          // If we have slots, ensure _isAvailable is true
          if (_slots.isNotEmpty) {
            _isAvailable = true;
          }
        });
      } else if (_isAvailable) {
        // If this date doesn't have specific slots but is marked available, add a default slot
        print("No existing slots found, adding default slot");
        _addSlot();
      }
    } catch (e) {
      print('Error loading date-specific availability: $e');
      // Just add a single default slot if there was an error
      if (_isAvailable && _slots.isEmpty) {
        _addSlot();
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _addSlot() {
    setState(() {
      // Create time directly without timezone adjustments
      final startTime = DateTime(
        widget.date.year,
        widget.date.month,
        widget.date.day,
        9, // Default start time: 9:00 AM
        0,
      );
      
      // End time is 30 minutes after start time
      final endTime = startTime.add(const Duration(minutes: 30));
      
      _slots.add(ANSlotsModel(
        id: (_slots.length + 1).toString(),
        starts: startTime,
        ends: endTime,
      ));
      
      print("Added slot - Time: ${DateFormat('HH:mm').format(startTime)}");
    });
  }

  void _removeSlot(ANSlotsModel slot) {
    setState(() {
      _slots.removeWhere((s) => s.id == slot.id);
      // Renumber IDs
      for (int i = 0; i < _slots.length; i++) {
        _slots[i] = ANSlotsModel(
          id: (i + 1).toString(),
          starts: _slots[i].starts,
          ends: _slots[i].ends,
        );
      }
    });
  }

  void _showTimePickerBottomSheet(ANSlotsModel slot) {
    // Use the slot time directly without timezone conversion
    final displayTime = slot.starts;

    showModalBottomSheet(
      context: context,
      isDismissible: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30.0)),
      ),
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        DateTime initialTime = displayTime;
        DateTime selectedLocalTime = initialTime;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 100.0,
                height: 10.0,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: circularBorderL,
                ),
              ),
              gapHXL,
              Text(
                S.of(context).kAppointmentTime,
                style: TextSize.r.semiBoldStyle,
              ),
              SizedBox(
                height: 150.0,
                child: CupertinoDatePicker(
                  initialDateTime: initialTime,
                  mode: CupertinoDatePickerMode.time,
                  onDateTimeChanged: (DateTime newTime) {
                    selectedLocalTime = DateTime(
                      widget.date.year,
                      widget.date.month,
                      widget.date.day,
                      newTime.hour,
                      newTime.minute,
                    );
                  },
                ),
              ),
              gapHL,
              ANElevatedButton(
                onPressed: () {
                  int slotIndex = _slots.indexWhere((s) => s.id == slot.id);
                  if (slotIndex != -1) {
                    setState(() {
                      // Create time directly without timezone adjustments
                      final startTime = DateTime(
                        widget.date.year,
                        widget.date.month,
                        widget.date.day,
                        selectedLocalTime.hour,
                        selectedLocalTime.minute,
                      );
                      
                      // Calculate end time (30 minutes after start time)
                      final endTime = startTime.add(const Duration(minutes: 30));
                      
                      _slots[slotIndex] = ANSlotsModel(
                        id: slot.id,
                        starts: startTime,
                        ends: endTime,
                      );
                      
                      print("Updated slot - Time: ${DateFormat('HH:mm').format(startTime)}");
                    });
                  }
                  Navigator.pop(context);
                },
                child: Text(
                  S.of(context).kConfirm,
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _saveAvailability() async {
    // Ensure there's at least one slot if available
    if (_isAvailable && _slots.isEmpty) {
      await ANDialogHelper.gShowConfirmationDialog(
        context: context,
        message: S.of(context).kPleaseAddAtLeastOneTimeSlot,
        type: DialogType.confirm,
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      print("=== SAVING AVAILABILITY DEBUG ===");
      print("Service Provider ID: ${widget.serviceProviderId}");
      print("Service ID: ${widget.serviceId}");
      print("Date: ${widget.date.toIso8601String()}");
      print("Number of slots: ${_slots.length}");
      
      for (int i = 0; i < _slots.length; i++) {
        final slot = _slots[i];
        print("Slot $i - Local time: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(slot.starts)}");
        print("Slot $i - JSON format: ${slot.toMapForServer()}");
      }
      
      await ref.read(dateSpecificAvailabilityProvider).saveDateSpecificAvailability(
        providerId: widget.serviceProviderId,
        serviceId: widget.serviceId,
        date: widget.date,
        slots: _isAvailable ? _slots : [],
      );

      print("Successfully saved date-specific availability");

      // Return result to parent screen
      Navigator.pop(context, {
        'slots': _slots,
        'isAvailable': _isAvailable
      });
    } catch (e) {
      print("Error saving date-specific availability: $e");
      
      // Still return result to parent screen even if server save failed
      // This way the UI will reflect the changes even if they didn't save to server
      Navigator.pop(context, {
        'slots': _slots,
        'isAvailable': _isAvailable
      });
      
      // Show error dialog
      await ANDialogHelper.gShowConfirmationDialog(
        context: context,
        message: "Changes saved locally but couldn't save to server: ${e.toString()}",
        type: DialogType.error,
      );
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isAvailable 
          ? S.of(context).kSetAvailabilityFor(DateFormat.yMMMd().format(widget.date)) 
          : S.of(context).kMarkUnavailableFor(DateFormat.yMMMd().format(widget.date))),
        backgroundColor: _isAvailable ? AppColors.primaryColor : Colors.red,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Date display
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.calendar_today, color: AppColors.primaryColor),
                        const SizedBox(width: Paddings.l),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat.EEEE().format(widget.date),
                              style: TextSize.r.regularStyle,
                            ),
                            Text(
                              DateFormat.yMMMd().format(widget.date),
                              style: TextSize.r.semiBoldStyle,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  gapHXL,
                  
                  // Available/unavailable toggle
                  Row(
                    children: [
                      Switch(
                        value: _isAvailable,
                        activeColor: AppColors.primaryColor,
                        inactiveTrackColor: Colors.red[100],
                        thumbColor: MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                            return _isAvailable ? AppColors.primaryColor : Colors.red;
                          },
                        ),
                        onChanged: (value) {
                          setState(() {
                            _isAvailable = value;
                          });
                        },
                      ),
                      Text(
                        _isAvailable 
                            ? S.of(context).kAvailable 
                            : S.of(context).kUnavailable,
                        style: TextSize.r.mediumStyle,
                      ),
                    ],
                  ),
                  
                  gapHXL,
                  
                  // Time slots section (only shown if available)
                  if (_isAvailable) ...[
                    Text(
                      S.of(context).kTimeSlots,
                      style: TextSize.r.semiBoldStyle,
                    ),
                    const SizedBox(height: Paddings.l),
                    ..._slots.map((slot) => _buildTimeSlot(slot)).toList(),
                    const SizedBox(height: Paddings.l),
                    TextButton.icon(
                      onPressed: _addSlot,
                      icon: const Icon(Icons.add, color: AppColors.primaryColor),
                      label: Text(
                        S.of(context).kAddTimeSlot,
                        style: const TextStyle(color: AppColors.primaryColor),
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: 40.0),
                  
                  // Save button
                  ANElevatedButton(
                    onPressed: _isSaving ? null : _saveAvailability,
                    child: _isSaving 
                      ? const SizedBox(
                          width: 20, 
                          height: 20, 
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          )
                        )
                      : Text(
                        S.of(context).kSave,
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                  ),
                ],
              ),
            ),
    );
  }
  
  Widget _buildTimeSlot(ANSlotsModel slot) {
    // Display the slot time directly without timezone conversion
    final displayTime = slot.starts;
    
    return Card(
      elevation: 2.0,
      margin: const EdgeInsets.only(bottom: 12.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        title: Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _showTimePickerBottomSheet(slot),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Text(
                    // Display the time directly
                    DateFormat('h:mm a').format(displayTime),
                    style: TextSize.r.regularStyle,
                  ),
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _removeSlot(slot),
            ),
          ],
        ),
      ),
    );
  }
}


