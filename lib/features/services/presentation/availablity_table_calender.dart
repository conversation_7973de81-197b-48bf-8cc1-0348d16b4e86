import 'dart:convert';

import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

class ANAvailabilityTableCalender extends StatefulWidget {
  final List<ANScheduleModel>? schedule;
  final Function() scrollDownCallback;
  final Function(ANSlotsModel, DateTime) submitReservation;
  final String serviceProviderId;
  final String serviceId;

  const ANAvailabilityTableCalender(
      {Key? key,
      required this.scrollDownCallback,
      required this.serviceProviderId,
      required this.serviceId,
      this.schedule,
      required this.submitReservation})
      : super(key: key);

  @override
  State<ANAvailabilityTableCalender> createState() =>
      _ANAvailabilityTableCalenderState();
}

class _ANAvailabilityTableCalenderState
    extends State<ANAvailabilityTableCalender> {
  CalendarFormat _calendarFormat = CalendarFormat.month;

  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  int _selectedTimeSlotIndex = -1;

  // Date
  bool _showTimeslots = false;

  ANScheduleModel? _selectedSchedule;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 32.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: circularBorderXS,
          ),
          child: TableCalendar(
            focusedDay: _focusedDay,
            firstDay: DateTime.now().subtract(const Duration(hours: 1)),
            lastDay: DateTime.now().add(const Duration(days: 30)),
            calendarFormat: _calendarFormat,
            calendarStyle: const CalendarStyle(
              selectedDecoration: BoxDecoration(
                  color: AppColors.primaryColor, shape: BoxShape.circle),
            ),
            headerStyle: const HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: TextStyle(fontSize: 17),
              headerPadding: EdgeInsets.symmetric(vertical: 10),
            ),
            daysOfWeekStyle: const DaysOfWeekStyle(
              weekdayStyle: TextStyle(fontSize: 14),
              weekendStyle: TextStyle(fontSize: 14),
            ),
            daysOfWeekHeight:
                50, // Increase this value to provide more space for day names
            rowHeight:
                50, // Adjust this value if needed to ensure enough space for date cells
            enabledDayPredicate: (day) {
              if (widget.schedule == null) return true;
              return widget.schedule!
                  .any((element) => element.day.weekday() == day.weekday);
            },
            selectedDayPredicate: (day) {
              return isSameDay(_selectedDay, day);
            },
            onDaySelected: (selectedDay, focusedDay) {
              if (!isSameDay(_selectedDay, selectedDay)) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                  _selectedSchedule = widget.schedule?.firstWhere((element) =>
                      element.day.weekday() == selectedDay.weekday);
                  _selectedTimeSlotIndex = -1;
                  _showTimeslots = true;
                });
                widget.scrollDownCallback.call();
              }
            },
            onFormatChanged: (format) {
              setState(() {
                _calendarFormat = format;
              });
            },
          ),
        ),
        if (_showTimeslots && _selectedSchedule != null)
          Consumer(
            builder: (BuildContext context, WidgetRef ref, Widget? child) {
              final timeSlots = ref.watch(serviceTimeSlotsProvider(jsonEncode({
                'serviceProviderId': widget.serviceProviderId,
                'date': _selectedDay!.toIso8601String(),
                'serviceId': widget.serviceId,
              })));
              widget.scrollDownCallback.call();

              return timeSlots.when(
                data: (data) => Column(
                  children: [
                    gapHXXL,
                    GridView.builder(
                      itemCount: data.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 10.0,
                        mainAxisSpacing: 10.0,
                        childAspectRatio: 100 / 40,
                      ),
                      itemBuilder: (BuildContext context, int index) =>
                          ElevatedButton(
                        onPressed: data
                                    .elementAt(index)
                                    .starts
                                    .toLocal()
                                    .isBefore(DateTime.now().toLocal()) ||
                                data.elementAt(index).reserved!
                            ? null
                            : () {
                                setState(() {
                                  _selectedTimeSlotIndex = index;
                                });
                                widget.scrollDownCallback.call();
                              },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: _selectedTimeSlotIndex == index
                                ? AppColors.primaryColor
                                : Colors.white,
                            disabledBackgroundColor: const Color(0xFFF2F2F2),
                            shape: RoundedRectangleBorder(
                              borderRadius: circularBorderXXS,
                            )),
                        child: Text(
                          DateFormat()
                              .add_jm()
                              .format(data.elementAt(index).starts.toLocal()),
                          style: TextSize.s.regularStyle.copyWith(
                            color: _selectedTimeSlotIndex == index
                                ? Colors.white
                                : data
                                            .elementAt(index)
                                            .starts
                                            .toLocal()
                                            .isBefore(
                                                DateTime.now().toLocal()) ||
                                        data.elementAt(index).reserved!
                                    ? const Color(0xFFD9D9D9)
                                    : Colors.black,
                          ),
                        ),
                      ),
                    ),
                    gapHXXXL,
                    gapHXL,
                    Center(
                      child: ANElevatedButton(
                        onPressed: _selectedTimeSlotIndex != -1
                            ? () {
                                ANDialogHelper.gShowCustomDialog(
                                  context: context,
                                  title:
                                      "S.of(context).kAppointmentConfirmation",
                                  body: Text(
                                    'S.of(context).kPleaseConfirmYourReservationOn ${DateFormat().add_yMMMEd().format(_selectedDay!)} ${DateFormat().add_jm().format(data.elementAt(_selectedTimeSlotIndex).starts.toLocal())}',
                                    textAlign: TextAlign.center,
                                  ),
                                  primaryButtonLabel: S.of(context).kConfirm,
                                  primaryButtonCallBack: () {
                                    widget.submitReservation.call(
                                        data.elementAt(_selectedTimeSlotIndex),
                                        _selectedDay!);
                                  },
                                  secondaryButtonLabel: S.of(context).kCancel,
                                  secondaryButtonCallBack: () {
                                    context.pop();
                                  },
                                );
                              }
                            : null,
                        child: Text(
                          S.of(context).kReserve,
                          style: const TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                error: (error, stackTrace) {
                  return Center(
                    child: ANError(
                      errorMessage: error.toString(),
                      refreshCallback: () async {
                        await ref.refresh(serviceTimeSlotsProvider(jsonEncode({
                          'serviceProviderId': widget.serviceProviderId,
                          'date': _selectedDay!.toIso8601String(),
                          'serviceId': widget.serviceId,
                        })).future);
                      },
                    ),
                  );
                },
                loading: () => const Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 10.0),
                    child: CircularProgressIndicator(
                        color: AppColors.primaryColor),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}
