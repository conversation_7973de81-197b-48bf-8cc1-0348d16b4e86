// ignore_for_file: use_build_context_synchronously

import 'dart:math';

import 'package:ajmal_now_doctor/common/Lists/drop_down_list.dart';
import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/common/text_fields/text-field.dart';
import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/features/services/presentation/location_picker_screen.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_location/service_location.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/image_picker_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ajmal_now_doctor/common/domain/location/city_model.dart';
import 'package:ajmal_now_doctor/features/location/data/location_provider.dart';

class ANCreateBranchScreen extends ConsumerStatefulWidget {
  const ANCreateBranchScreen({super.key});

  @override
  ConsumerState createState() => _ANCreateServiceScreenState();
}

class _ANCreateServiceScreenState extends ConsumerState<ANCreateBranchScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController scrollController = ScrollController();
  final _formKey = GlobalKey<FormState>();
  final _typeListKey = GlobalKey<FormFieldState>();
  final _cityDistrictListKey = GlobalKey<FormFieldState>();
  bool _galleryError = false;
  bool _logoError = false;
  bool locationError = false;
  final TextEditingController _serviceNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _offersController = TextEditingController();
  final TextEditingController _clinicHotlineController =
      TextEditingController();
  final TextEditingController _clinicPhoneNumberController =
      TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final controller = CarouselSliderController();
  
  LatLng? _pickedLocation;
  CityDistrict? selectedCityDistrict;
  int _activeIndex = 0;
  String? address;

  int gelleryLimit = 3;

  List<ANScheduleModel> schedules = [];
  bool atHomeVisit = false;
  bool enableGap = false;

  Future<void> _selectLocation() async {
    final selectedLocation = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            ANLocationPickerScreen(pickedLocation: _pickedLocation),
      ),
    );

    if (selectedLocation != null) {
      setState(() {
        _pickedLocation = selectedLocation['location'];
        address = selectedLocation['address'];
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize location data when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Load city districts data
      await ref.read(locationProvider.notifier).fetchDistrictsAndCities();
    });
  }

  @override
  void dispose() {
    _serviceNameController.dispose();
    _descriptionController.dispose();
    _offersController.dispose();
    _clinicHotlineController.dispose();
    _clinicPhoneNumberController.dispose();
    _addressController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final servicesController = ref.watch(servicesProvider);
    final providerTypes = ref.watch(providerTypesProvider);
    final locationState = ref.watch(locationProvider);

    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
        disableBottomNavBar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: true,
      ),
      appbar: ANMainScreenAppbar(
        title: Text(
          S.of(context).kCreateNewBranch,
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {});
        },
        child: SingleChildScrollView(
          controller: scrollController,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: SizedBox(
                    width: 85.0,
                    child: Stack(
                      children: [
                        CircleAvatar(
                          radius: 40.0,
                          backgroundColor:
                              _logoError ? Colors.red : AppColors.primaryColor,
                          child: servicesController.logo == null
                              ? const CircleAvatar(
                                  radius: 37.0,
                                  backgroundColor: AppColors.backgroundColor,
                                  child: Icon(
                                    Icons.image,
                                    color: AppColors.primaryColor,
                                    size: 45.0,
                                  ),
                                )
                              : CircleAvatar(
                                  radius: 37.0,
                                  backgroundImage: NetworkImage(
                                      servicesController.logo!.url
                                          .replaceAll("localhost", Config.IP)),
                                ),
                        ),
                        PositionedDirectional(
                          bottom: 5.0,
                          end: 0.0,
                          child: CircleAvatar(
                            radius: 15.0,
                            backgroundColor: Colors.white,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              splashRadius: 25.0,
                              icon: const Icon(
                                Icons.add,
                                size: 20.0,
                                color: Colors.black,
                              ),
                              onPressed: () {
                                ANDialogHelper.gShowCustomDialog(
                                  context: context,
                                  title: S.of(context).kPickProfileImage,
                                  primaryButtonLabel:
                                      S.of(context).kPickFromGallery,
                                  primaryButtonCallBack: () async {
                                    Navigator.pop(context);
                                    final tempImages = await ANImagePickerHelper
                                        .galleryImagePicker();
                                    if (tempImages != null) {
                                      final mimeType =
                                          lookupMimeType(tempImages.path);
                                      bool success =
                                          await servicesController.addPhoto(
                                        await MultipartFile.fromFile(
                                          tempImages.path,
                                          filename:
                                              tempImages.path.split('/').last,
                                          contentType:
                                              MediaType.parse(mimeType!),
                                        ),
                                        false,
                                      );
                                      if (success) {
                                        setState(
                                          () {
                                            _logoError = false;
                                          },
                                        );
                                      }
                                    }
                                  },
                                  secondaryButtonLabel:
                                      S.of(context).kCaptureFromCamera,
                                  secondaryButtonCallBack: () async {
                                    Navigator.pop(context);
                                    final tempImages = await ANImagePickerHelper
                                        .cameraImagePicker();
                                    if (tempImages != null) {
                                      final mimeType =
                                          lookupMimeType(tempImages.path);
                                      bool success =
                                          await servicesController.addPhoto(
                                        await MultipartFile.fromFile(
                                          tempImages.path,
                                          filename:
                                              tempImages.path.split('/').last,
                                          contentType:
                                              MediaType.parse(mimeType!),
                                        ),
                                        false,
                                      );
                                      if (success) {
                                        setState(() {
                                          _logoError = false;
                                        });
                                      }
                                    }
                                  },
                                );
                              },
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                if (_logoError) ...[
                  gapHL,
                  Center(
                    child: Text(
                      S.of(context).kYouMustAddALogo,
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: TextSize.r.size,
                      ),
                    ),
                  ),
                ],
                gapHXL,
                ANTextField(
                  controller: _serviceNameController,
                  hint: S.of(context).kBranchName,
                  label: S.of(context).kBranchName,
                  validator: (value) {
                    return ANValidationsHelper.getValidators(validation: {
                      'is_required': true,
                    }, value: value);
                  },
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _descriptionController,
                  hint: S.of(context).kDescription,
                  label: S.of(context).kDescription,
                  textInputType: TextInputType.multiline,
                  maxLines: 5,
                  maxLength: 500,
                  validator: (value) {
                    return ANValidationsHelper.getValidators(
                        validation: {'is_required': true}, value: value);
                  },
                ),
                gapHL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _offersController,
                  hint: S.of(context).kOffers,
                  label: S.of(context).kOffers,
                  textInputType: TextInputType.multiline,
                  maxLines: 5,
                  maxLength: 300,
                ),
                gapHL,
                providerTypes.when(
                  data: (data) {
                    return ANDropDownList<ANServiceTypesModel>(
                      dropdownKey: _typeListKey,
                      value: servicesController.selectedProviderType,
                      validator: (value) {
                        return ANValidationsHelper.getValidators(
                            validation: {'is_required': true},
                            value: value?.name);
                      },
                      hint: Text(
                        S.of(context).kType,
                        style: TextSize.r.mediumStyle,
                      ),
                      selectedItemBuilder: (_) =>
                          data.map((e) => Center(child: Text(e.name))).toList(),
                      items: data.map((e) {
                        return DropdownMenuItem<ANServiceTypesModel>(
                          value: e,
                          child: Text(
                            e.name,
                            style: TextStyle(
                                color:
                                    servicesController.selectedProviderType == e
                                        ? Colors.white
                                        : Colors.black),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          _typeListKey.currentState!.validate();
                          servicesController.selectProviderType(value);
                          ref.refresh(providerTypesProvider.future);
                        }
                      },
                    );
                  },
                  error: (error, _) => Center(
                    child: ANError(
                      errorMessage: error.toString(),
                      refreshCallback: () async {},
                    ),
                  ),
                  loading: () => const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 10.0),
                      child: CircularProgressIndicator(
                          color: AppColors.primaryColor),
                    ),
                  ),
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _clinicHotlineController,
                  hint: S.of(context).kClinicHotline,
                  label: S.of(context).kClinicHotline,
                  validator: (value) {
                    return ANValidationsHelper.getValidators(validation: {
                      'is_required': true,
                    }, value: value);
                  },
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _clinicPhoneNumberController,
                  hint: S.of(context).kClinicPhone,
                  label: S.of(context).kClinicPhone,
                  // Made optional - no validator
                ),
                gapHXL,
                // New CityDistrict Dropdown
                if (locationState.isLoading)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 10.0),
                      child: CircularProgressIndicator(
                          color: AppColors.primaryColor),
                    ),
                  )
                else if (locationState.error != null)
                  Center(
                    child: ANError(
                      errorMessage: locationState.error!,
                      refreshCallback: () async {
                        await ref.read(locationProvider.notifier).fetchDistrictsAndCities();
                      },
                    ),
                  )
                else
                  ANDropDownList<CityDistrict>(
                    dropdownKey: _cityDistrictListKey,
                    value: selectedCityDistrict,
                    validator: (value) {
                      return ANValidationsHelper.getValidators(
                          validation: {'is_required': true}, 
                          value: value?.id);
                    },
                    hint: Text(
                      "City & District",
                      style: TextSize.r.mediumStyle,
                    ),
                    selectedItemBuilder: (_) => locationState.districts
                        .map((district) => Center(
                            child: Text('${district.cityDisplayName}, ${district.districtName.en}')))
                        .toList(),
                    items: locationState.districts.map((district) {
                      return DropdownMenuItem<CityDistrict>(
                        value: district,
                        child: Text(
                          '${district.cityDisplayName}, ${district.districtName.en}',
                          style: TextStyle(
                            color: selectedCityDistrict == district
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedCityDistrict = value;
                        });
                        _cityDistrictListKey.currentState!.validate();
                      }
                    },
                  ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _addressController,
                  hint: "Full Address",
                  label: "Address",
                  textInputType: TextInputType.multiline,
                  maxLines: 3,
                ),
                gapHXL,
                Row(
                  children: [
                    IconButton(
                      onPressed: _selectLocation,
                      icon: const Icon(
                        Icons.location_on,
                        color: AppColors.primaryColor,
                        size: 35,
                      ),
                    ),
                    Flexible(
                      child: Text(
                        address ?? S.of(context).kPickALocation,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: AppColors.primaryColor,
                          fontSize: TextSize.l.size,
                        ),
                      ),
                    ),
                  ],
                ),
                gapHXL,
                Text(
                  S.of(context).kGallery,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                    fontSize: TextSize.r.size,
                  ),
                ),
                gapHXL,
                Column(
                  children: [
                    CarouselSlider.builder(
                      itemCount: min(
                          servicesController.gallery.length + 1, gelleryLimit),
                      carouselController: controller,
                      itemBuilder: (context, index, realIndex) {
                        return buildImage(servicesController.gallery, index) ??
                            const SizedBox();
                      },
                      options: CarouselOptions(
                        height: 200,
                        aspectRatio: 16 / 9,
                        autoPlay: false,
                        enableInfiniteScroll: false,
                        autoPlayAnimationDuration: const Duration(seconds: 2),
                        enlargeCenterPage: true,
                        onPageChanged: (index, reason) =>
                            setState(() => _activeIndex = index),
                      ),
                    ),
                    if (_galleryError)
                      Text(
                        S.of(context).kYouMustAddAtLeastOneImage,
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: TextSize.r.size,
                        ),
                      ),
                  ],
                ),
                gapHXXL,
                Center(child: buildIndicator()),
                gapHXL,
                ANLoadingElevatedButton(
                  onPressed: () async {
                    setState(() {
                      _galleryError = servicesController.gallery.isEmpty;
                      _logoError = servicesController.logo == null;
                      locationError = _pickedLocation == null;
                    });
                    if (!_formKey.currentState!.validate() ||
                        _galleryError ||
                        locationError ||
                        _logoError) {
                      return;
                    }
                    try {
                      final result = await ref.read(servicesProvider).createBranch(
                        serviceName: _serviceNameController.text,
                        description: _descriptionController.text,
                        offers: _offersController.text,
                        type: servicesController.selectedProviderType!.id,
                        location: ServiceLocation(
                          id: "-1",
                          hotline: _clinicHotlineController.text,
                          phone: _clinicPhoneNumberController.text.trim().isNotEmpty 
                            ? _clinicPhoneNumberController.text 
                            : "", // Secondary phone is now optional
                          cityDistrictId: selectedCityDistrict!.id,
                          address: _addressController.text,
                          // Keep legacy fields for backward compatibility during transition
                          city: selectedCityDistrict!.cityDisplayName,
                          district: selectedCityDistrict!.districtName.en,
                        ),
                        homeVisit: atHomeVisit,
                        coordinates: _pickedLocation!,
                      );

                      if (result) {
                        await ANDialogHelper.gShowSuccessDialog(
                            context: context,
                            title: S.of(context).kBranchCreatedSuccessfully,
                            primaryButtonLabel: S.of(context).kConfirm);
                        Navigator.pop(context);
                        ref.refresh(servicesDataProvider);
                      }
                    } catch (e) {
                      await ANDialogHelper.gShowConfirmationDialog(
                          context: context,
                          message: e.toString(),
                          type: DialogType.confirm);
                    }
                  },
                  label: Text(
                    S.of(context).kCreate,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  ),
                ),
                gapHL,
                if (enableGap) const SizedBox(height: 270),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildIndicator() {
    final servicesController = ref.watch(servicesProvider);
    return AnimatedSmoothIndicator(
      onDotClicked: animateToSlide,
      effect: const ExpandingDotsEffect(
        dotWidth: 10,
        activeDotColor: AppColors.primaryColor,
        dotHeight: 10,
      ),
      activeIndex: _activeIndex,
      count: min(servicesController.gallery.length + 1, gelleryLimit),
    );
  }

  void animateToSlide(int index) => controller.animateToPage(index);

  Widget? buildImage(List<ANImageModel> urlImages, int index) {
    final servicesController = ref.watch(servicesProvider);
    return index <= urlImages.length - 1
        ? Stack(
            children: [
              Image.network(
                urlImages[index].url.replaceAll("localhost", Config.IP),
              ),
              PositionedDirectional(
                top: 5.0,
                end: 5.0,
                child: CircleAvatar(
                  backgroundColor: Colors.white.withOpacity(0.7),
                  radius: 15,
                  child: IconButton(
                    icon: const Icon(
                      Icons.close,
                      size: 15,
                      color: Colors.black,
                    ),
                    onPressed: () {
                      servicesController.deletePhoto(index);
                      setState(() {
                        _galleryError = servicesController.gallery.isEmpty;
                      });
                    },
                  ),
                ),
              ),
            ],
          )
        : GestureDetector(
            onTap: () {
              ANDialogHelper.gShowCustomDialog(
                  context: context,
                  title: S.of(context).kPickProfileImage,
                  primaryButtonLabel: S.of(context).kPickFromGallery,
                  primaryButtonCallBack: () async {
                    Navigator.pop(context);
                    final tempImages =
                        await ANImagePickerHelper.galleryImagePicker();
                    if (tempImages != null) {
                      final mimeType = lookupMimeType(tempImages.path);
                      bool success = await servicesController.addPhoto(
                        await MultipartFile.fromFile(
                          tempImages.path,
                          filename: tempImages.path.split('/').last,
                          contentType: MediaType.parse(mimeType!),
                        ),
                        true,
                      );
                      if (success) {
                        setState(() {
                          _galleryError = false;
                        });
                      }
                    }
                  },
                  secondaryButtonLabel: S.of(context).kCaptureFromCamera,
                  secondaryButtonCallBack: () async {
                    Navigator.pop(context);
                    final tempImages =
                        await ANImagePickerHelper.cameraImagePicker();
                    if (tempImages != null) {
                      final mimeType = lookupMimeType(tempImages.path);
                      bool success = await servicesController.addPhoto(
                        await MultipartFile.fromFile(
                          tempImages.path,
                          filename: tempImages.path.split('/').last,
                          contentType: MediaType.parse(mimeType!),
                        ),
                        true,
                      );
                      if (success) {
                        setState(() {
                          _galleryError = false;
                        });
                      }
                    }
                  });
            },
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(10),
                border: _galleryError
                    ? Border.all(color: Colors.red, width: 1.0)
                    : null,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.image,
                    size: 75,
                    color: AppColors.primaryColor,
                  ),
                  Text(
                    "Add Image",
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: TextSize.r.size,
                    ),
                  ),
                ],
              ),
            ),
          );
  }
}
