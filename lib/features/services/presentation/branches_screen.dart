import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/presentation/service_provider_card.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANBranchScreen extends ConsumerStatefulWidget {
  const ANBranchScreen({super.key});

  @override
  ConsumerState createState() => _ANServicesScreenState();
}

class _ANServicesScreenState extends ConsumerState<ANBranchScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  bool loading = false;

  @override
  void initState() {
    // ref.read(servicesRepositoryProvider);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final servicesController = ref.watch(servicesProvider);
    final servicesData = ref.watch(servicesDataProvider);
    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
        disableBottomNavBar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      floatingActionButton: CircleAvatar(
        backgroundColor: AppColors.primaryColor,
        child: IconButton(
          icon: const Icon(Icons.add, color: Colors.white),
          onPressed: () {
            context.goNamed(AppRoute.createBranchScreen.name);
          },
        ),
      ),
      appbar: ANMainScreenAppbar(
        scaffoldKey: _scaffoldKey,
        centerTitle: true,
        title: Text(S.of(context).kBranches),
      ),
      body: RefreshIndicator(
        color: AppColors.primaryColor,
        onRefresh: () async {
          ref.refresh(servicesDataProvider.future);
        },
        child: ListView(
          children: [
            servicesData.when(
              data: (data) {
                return Column(
                  children: servicesController.loading
                      ? [
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 10.0),
                              child: CircularProgressIndicator(
                                  color: AppColors.primaryColor),
                            ),
                          ),
                        ]
                      : data.isEmpty
                          ? [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  gapHL,
                                  Text(
                                    S.of(context).kNoBranches,
                                    style: GoogleFonts.philosopher(
                                            textStyle: TextSize.l.lightStyle)
                                        .copyWith(color: Colors.black),
                                  ),
                                ],
                              ),
                            ]
                          : data
                              .map(
                                (service) => Column(
                                  children: [
                                    ANServiceProviderCard(
                                      serviceProvider: service,
                                    ),
                                    if (service != data.last) gapHXL
                                  ],
                                ),
                              )
                              .toList(),
                );
              },
              error: (error, _) => Center(
                child: ANError(
                  errorMessage: error.toString(),
                  refreshCallback: () async {},
                ),
              ),
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.0),
                  child:
                      CircularProgressIndicator(color: AppColors.primaryColor),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
