import 'dart:io';

import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/common/text_fields/text-field.dart';
import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/services/data/date_specific_availability_repository.dart';
import 'package:ajmal_now_doctor/features/services/data/services_repository.dart';
import 'package:ajmal_now_doctor/features/services/presentation/tabbed_schedule_selector.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/image_picker_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';


class ANUpdateDeleteServiceScreen extends ConsumerStatefulWidget {
  const ANUpdateDeleteServiceScreen({super.key});

  @override
  ConsumerState createState() => _ANUpdateDeleteServiceScreenState();
}

class _ANUpdateDeleteServiceScreenState
    extends ConsumerState<ANUpdateDeleteServiceScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController scrollController = ScrollController();
  final _formKey = GlobalKey<FormState>();
  bool _isDaySelectionError = false;
  final TextEditingController _serviceNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _discountController = TextEditingController();
  List<ANScheduleModel> schedules = [];
  bool isActive = false;
  bool _isPackage = false;
  File? _image;
  bool enableGap = false;
  
  // Date specific availability data
  List<DateTime> dateSpecificDates = [];
  Map<DateTime, List<ANSlotsModel>> dateSpecificSlots = {};
  Map<DateTime, bool> dateAvailability = {};

  void initService() {
    final servicesController = ref.read(servicesProvider);
    ANServicesModel service = servicesController.selectedService!;
    
    // Initialize service logo
    servicesController.logo = service.image;
    
    // Populate form fields with service data
    _serviceNameController.text = service.title;
    _descriptionController.text = service.description;
    
    // Fix price retrieval - ensure proper handling of null values
    _minPriceController.text = service.minPrice != null ? service.minPrice!.toString() : '';
    _maxPriceController.text = service.maxPrice != null ? service.maxPrice!.toString() : '';
    _priceController.text = service.price.toString();
    
    // Fix discount retrieval - ensure proper handling of null values
    if (service.discount != null) {
      _discountController.text = service.discount!.toString();
    } else {
      _discountController.text = '';
    }
    
    // Set discount active status - handle null properly
    isActive = service.discountActive ?? false;
    
    // Set package status
    _isPackage = service.isPackage;
    
    // Set schedules
    schedules = service.schedule;
    
    print("Service data loaded:");
    print("Min Price: ${service.minPrice}");
    print("Max Price: ${service.maxPrice}"); 
    print("Price: ${service.price}");
    print("Discount: ${service.discount}");
    print("Discount Active: ${service.discountActive}");
    print("Text field values after initialization:");
    print("Min Price Controller: ${_minPriceController.text}");
    print("Max Price Controller: ${_maxPriceController.text}");
    print("Price Controller: ${_priceController.text}");
    print("Discount Controller: ${_discountController.text}");
    print("isActive: $isActive");
  }

  @override
  void initState() {
    super.initState();
    // Initialize after the widget is built to ensure all data is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initService();
      setState(() {
        // Trigger a rebuild after data is initialized
      });
    });
    // Load any existing date-specific availability data
    _loadDateSpecificAvailability();
  }
  
  // Handle date-specific availability changes
  void _handleDateSpecificScheduleChanged(List<ANSlotsModel> slots, DateTime date, bool isAvailable) {
    setState(() {
      if (!dateSpecificDates.contains(date)) {
        dateSpecificDates.add(date);
      }
      dateSpecificSlots[date] = slots;
      dateAvailability[date] = isAvailable;
    });
  }
  
  Future<void> _loadDateSpecificAvailability() async {
    final servicesController = ref.read(servicesProvider);
    if (servicesController.selectedService != null) {
      // This would be a good place to load existing date-specific availability
      // But for this implementation, we'll just let users add new date-specific schedules
    }
  }

  @override
  Widget build(BuildContext context) {
    final servicesController = ref.watch(servicesProvider);
    List<String> days = [
      S.of(context).kSun,
      S.of(context).kMon,
      S.of(context).kTue,
      S.of(context).kWed,
      S.of(context).kThu,
      S.of(context).kFri,
      S.of(context).kSat,
    ];
    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
        disableBottomNavBar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: true,
      ),
      appbar: ANMainScreenAppbar(
        title: Text(
          S.of(context).kEditService,
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {});
        },
        child: SingleChildScrollView(
          controller: scrollController,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: SizedBox(
                    width: 85.0,
                    child: Stack(
                      children: [
                        CircleAvatar(
                          radius: 40.0,
                          backgroundColor: AppColors.primaryColor,
                          child: _image == null
                              ? servicesController.selectedService?.image !=
                                      null
                                  ? CircleAvatar(
                                      radius: 37.0,
                                      backgroundImage: NetworkImage(
                                        servicesController
                                            .selectedService!.image!.url
                                            .replaceAll("localhost", Config.IP),
                                      ),
                                    )
                                  : const CircleAvatar(
                                      radius: 37.0,
                                      backgroundColor:
                                          AppColors.backgroundColor,
                                      child: Icon(
                                        Icons.image,
                                        color: AppColors.primaryColor,
                                        size: 45.0,
                                      ),
                                    )
                              : CircleAvatar(
                                  radius: 37.0,
                                  backgroundImage: FileImage(_image!),
                                ),
                        ),
                        PositionedDirectional(
                          bottom: 5.0,
                          end: 0.0,
                          child: CircleAvatar(
                            radius: 15.0,
                            backgroundColor: Colors.white,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              splashRadius: 25.0,
                              icon: const Icon(
                                Icons.add,
                                size: 20.0,
                                color: Colors.black,
                              ),
                              onPressed: () {
                                ANDialogHelper.gShowCustomDialog(
                                    context: context,
                                    title: S.of(context).kPickProfileImage,
                                    primaryButtonLabel:
                                        S.of(context).kPickFromGallery,
                                    primaryButtonCallBack: () async {
                                      final tempImages =
                                          await ANImagePickerHelper
                                              .galleryImagePicker();
                                      if (tempImages != null) {
                                        setState(() {
                                          _image = File(tempImages.path);
                                        });
                                        Navigator.pop(context);
                                      }
                                    },
                                    secondaryButtonLabel:
                                        S.of(context).kCaptureFromCamera,
                                    secondaryButtonCallBack: () async {
                                      final tempImages =
                                          await ANImagePickerHelper
                                              .cameraImagePicker();
                                      if (tempImages != null) {
                                        setState(() {
                                          _image = File(tempImages.path);
                                        });
                                        Navigator.pop(context);
                                      }
                                    });
                              },
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _serviceNameController,
                  hint: S.of(context).kServiceName,
                  validator: (value) {
                    return ANValidationsHelper.getValidators(validation: {
                      'is_required': true,
                    }, value: value);
                  },
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _descriptionController,
                  hint: S.of(context).kDescription,
                  textInputType: TextInputType.multiline,
                  maxLines: 2,
                  maxLength: 500,
                  validator: (value) {
                    // Description is now optional
                    return null;
                  },
                ),
                gapHXL,
                Row(
                  children: [
                    Expanded(
                      child: ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _minPriceController,
                        hint: S.of(context).kMinPriceEGP,
                        textInputType: TextInputType.number
    
                      ),
                    ),
                    gapWM,
                    Expanded(
                      child: ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _maxPriceController,
                        hint: S.of(context).kMaxPriceEGP,
                        textInputType: TextInputType.number,
                        validator: (value) {
                          if (_minPriceController.text.isNotEmpty && _maxPriceController.text.isNotEmpty) {
                            double min = double.parse(_minPriceController.text);
                            double max = double.parse(_maxPriceController.text);
                            if (max < min) {
                              return S.of(context).kMaxPriceMustBeGreaterThanMinPrice;
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                gapHXL,
                ANTextField(
                  onFocusChanged: (isFocused) {
                    setState(() {
                      enableGap = isFocused;
                    });
                  },
                  controller: _discountController,
                  hint: S.of(context).kDiscountPercentage,
                  textInputType: TextInputType.number,
                  validator: isActive
                      ? (value) => ANValidationsHelper.getValidators(
                          validation: {'is_required': true}, value: value)
                      : null,
                ),
                gapHXL,
                Row(
                  children: [
                    Checkbox(
                      value: isActive,
                      side: const BorderSide(color: AppColors.primaryColor),
                      activeColor: AppColors.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      onChanged: (value) {
                        setState(() {
                          isActive = value!;
                        });
                      },
                    ),
                    Text(
                      S.of(context).kDiscountActive,
                      style: TextSize.r.lightStyle,
                    ),
                  ],
                ),
                Row(
                  children: [
                    Checkbox(
                      value: _isPackage,
                      side: const BorderSide(color: AppColors.primaryColor),
                      activeColor: AppColors.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.0),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _isPackage = value!;
                        });
                      },
                    ),
                    Text(
                      S.of(context).kIsThisAPackage,
                      style: TextSize.r.lightStyle,
                    ),
                  ],
                ),
                gapHXL,
                TabbedScheduleSelector(
                  weeklySchedules: schedules,
                  onWeeklySchedulesChanged: (newSchedules) => setState(() {
                    schedules = newSchedules;
                  }),
                  onDateSpecificScheduleChanged: _handleDateSpecificScheduleChanged,
                  serviceProviderId: servicesController.selectedBranch!.id,
                  serviceId: servicesController.selectedService!.id,
                  hasError: false,
                ),
                gapHXL,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: ANLoadingElevatedButton(
                        onPressed: () async {
                          if (await validateForm()) {
                            await _updateService();
                          }
                        },
                        label: Text(
                          S.of(context).kUpdateService,
                          style: const TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    gapWL,
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          ANDialogHelper.gShowCustomDialog(
                            context: context,
                            title: S.of(context).kDeleteService,
                            body: Text(S.of(context).kAreYouSureAboutDeletingThisService),
                            primaryButtonCallBack: () async {
                              try {
                                await servicesController.deleteService();
                                Navigator.pop(context);
                                Navigator.pop(context);
                                await ANDialogHelper.gShowSuccessDialog(
                                    context: context,
                                    title: S.of(context).kServiceDeletedSuccessfully,
                                    primaryButtonLabel: S.of(context).kConfirm);
                                ref.refresh(serviceProviderByIdProvider.future);
                              } catch (e) {
                                Navigator.pop(context);
                                await ANDialogHelper.gShowConfirmationDialog(
                                    context: context,
                                    message: e.toString(),
                                    type: DialogType.error);
                              }
                            },
                            primaryButtonLabel: S.of(context).kConfirm,
                            secondaryButtonLabel: S.of(context).kCancel,
                          );
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                        ),
                        child: Text(S.of(context).kDeleteService),
                      ),
                    ),
                  ],
                ),
                gapHXL,
                if (enableGap) const SizedBox(height: 270),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<bool> validateForm() {
    final servicesController = ref.read(servicesProvider);
    if (_formKey.currentState!.validate()) {
      // Check if service has an existing image or a new image is selected
      if (_image == null && servicesController.selectedService?.image == null) {
        ANDialogHelper.gShowConfirmationDialog(
          context: context,
          message: S.of(context).kServiceUploadImageValidation,
          type: DialogType.error,
        );
        return Future.value(false);
      }
      
      // We no longer require weekly schedules
      return Future.value(true);
    } else {
      if (_image == null && servicesController.selectedService?.image == null) {
        ANDialogHelper.gShowConfirmationDialog(
          context: context,
          message: S.of(context).kServiceUploadImageValidation,
          type: DialogType.error,
        );
      }
      return Future.value(false);
    }
  }

  Future<void> _updateService() async {
    final servicesController = ref.read(servicesProvider);
    try {
      print("Updating service: ${servicesController.selectedService!.id}");
      
      // First, handle image upload if a new image was selected
      ANImageModel? serviceImage = servicesController.selectedService!.image;
      if (_image != null) {
        print("Uploading new service image...");
        try {
          // Import the required packages
          final mimeType = lookupMimeType(_image!.path);
          MultipartFile imageFile = await MultipartFile.fromFile(
            _image!.path,
            filename: _image!.path.split('/').last,
            contentType: MediaType.parse(mimeType!),
          );
          
          bool uploadSuccess = await servicesController.addPhoto(imageFile, false);
          if (uploadSuccess && servicesController.logo != null) {
            serviceImage = servicesController.logo;
            print("Service image uploaded successfully: ${serviceImage!.id}");
          } else {
            print("Failed to upload service image");
          }
        } catch (e) {
          print("Error uploading service image: $e");
          // Continue with the old image if upload fails
        }
      }
      
      double? minPrice = _minPriceController.text.isNotEmpty 
          ? double.parse(_minPriceController.text) 
          : null;
      double? maxPrice = _maxPriceController.text.isNotEmpty 
          ? double.parse(_maxPriceController.text) 
          : null;
      
      // For backward compatibility, set a legacy price field value
      // If only one price is provided, use that value
      // If both prices are provided, use the minimum price for better representation
      double price = 0;
      if (minPrice != null && maxPrice == null) {
        price = minPrice;
      } else if (maxPrice != null && minPrice == null) {
        price = maxPrice;
      } else if (minPrice != null && maxPrice != null) {
        // Use minimum price for the legacy field
        price = minPrice;
      }
      
      // Handle discount value - only parse if active and field is not empty
      double? discountValue;
      if (isActive && _discountController.text.isNotEmpty) {
        try {
          discountValue = double.parse(_discountController.text);
        } catch (e) {
          print("Error parsing discount value: $e");
          discountValue = null;
        }
      }
      
      ANServicesModel updatedService = ANServicesModel(
        id: servicesController.selectedService!.id,
        title: _serviceNameController.text,
        description: _descriptionController.text,
        price: price,
        minPrice: minPrice,
        maxPrice: maxPrice,
        discount: discountValue,
        discountActive: isActive,
        isPackage: _isPackage,
        // Keep the schedule we've been updating
        schedule: schedules,
        image: serviceImage,
      );
      
      bool serviceUpdateSuccess = false;
      String errorMessage = "";
      
      try {
        bool serviceUpdated = await servicesController.updateService(
          newService: updatedService,
        );
        
        if (!serviceUpdated) {
          errorMessage = "Failed to update service. Continuing with availability updates.";
          print(errorMessage);
        } else {
          serviceUpdateSuccess = true;
          print("Service updated successfully on the backend");
        }
      } catch (updateError) {
        // Log but continue with availability updates
        errorMessage = "Error updating service: $updateError. Continuing with availability updates.";
        print(errorMessage);
      }
      
      // Continue with date-specific availability updates regardless of service update result
      bool availabilityUpdateSuccess = true;
      
      // Update date-specific availability
      final dateSpecificRepo = ref.read(dateSpecificAvailabilityProvider);
      for (final date in dateSpecificDates) {
        if (dateSpecificSlots.containsKey(date)) {
          print("Saving availability for branch ID: ${servicesController.selectedBranch!.id}, service ID: ${servicesController.selectedService!.id}, date: ${date.toIso8601String()}");
          try {
            await dateSpecificRepo.saveDateSpecificAvailability(
              providerId: servicesController.selectedBranch!.id,
              serviceId: servicesController.selectedService!.id,
              date: date,
              slots: dateSpecificSlots[date] ?? [],
            );
          } catch (e) {
            availabilityUpdateSuccess = false;
            print("Error saving date availability: $e");
            errorMessage += "\nError saving availability for ${DateFormat.yMMMd().format(date)}: $e";
            // Continue with other dates even if one fails
          }
        }
      }
      
      // Show appropriate message based on success/failure
      if (serviceUpdateSuccess && availabilityUpdateSuccess) {
        await ANDialogHelper.gShowSuccessDialog(
            context: context,
            title: S.of(context).kServiceUpdatedSuccessfully,
            primaryButtonLabel: S.of(context).kConfirm);
      } else {
        // Show partial success message
        String message = "Some changes were saved successfully, but there were also errors:\n$errorMessage";
        await ANDialogHelper.gShowConfirmationDialog(
            context: context,
            message: message,
            type: DialogType.confirm);
      }
      
      // Refresh data and navigate back regardless of errors
      ref.refresh(serviceProviderByIdProvider.future);
      Navigator.pop(context);
    } catch (e) {
      print("Unexpected error in update flow: $e");
      await ANDialogHelper.gShowConfirmationDialog(
          context: context,
          message: e.toString(),
          type: DialogType.error);
    }
  }
}
