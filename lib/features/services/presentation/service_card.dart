import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';
import 'package:flutter/material.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:google_fonts/google_fonts.dart';

class ANServiceCard extends StatelessWidget {
  final ANServicesModel service;
  final bool isSelected;
  final void Function() onPressed;

  const ANServiceCard(
      {Key? key,
      required this.service,
      required this.isSelected,
      required this.onPressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Function to format price display
    String getPriceDisplay() {
      if (service.minPrice != null && service.maxPrice != null) {
        // Price range
        return '${service.minPrice!.toStringAsFixed(0)} - ${service.maxPrice!.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else if (service.minPrice != null) {
        // Fixed price (min only)
        return '${service.minPrice!.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else if (service.maxPrice != null) {
        // Fixed price (max only)
        return '${service.maxPrice!.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else if (service.price > 0) {
        // Legacy price field
        return '${service.price.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else {
        // No price set
        return '';
      }
    }
    
    // Check if we should show price
    final bool shouldShowPrice = service.minPrice != null || service.maxPrice != null || service.price > 0;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 21.0, horizontal: 23.0),
      decoration: BoxDecoration(
        borderRadius: circularBorderXXS,
        color: isSelected ? AppColors.primaryColor : Colors.white,
      ),
      child: Row(
        children: [
          if (service.image != null)
            SizedBox(
              width: 50.0,
              child: Image.network(
                  service.image!.url.replaceAll("localhost", Config.IP)),
            ),
          gapWL,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        service.title,
                        style: GoogleFonts.philosopher(
                            textStyle: TextSize.s.boldStyle.copyWith(
                                color:
                                    isSelected ? Colors.white : Colors.black)),
                      ),
                    ),
                    if (shouldShowPrice)
                      Text(
                        getPriceDisplay(),
                        style: GoogleFonts.philosopher(
                            textStyle: TextSize.xs.boldStyle.copyWith(
                                color: isSelected
                                    ? Colors.white
                                    : AppColors.primaryColor)),
                      )
                  ],
                ),
                gapHL,
                Text(
                  service.description,
                  style: TextSize.xxs.regularStyle.copyWith(
                      color: isSelected ? Colors.white : Colors.black),
                  textAlign: TextAlign.start,
                ),
                if (service.discountActive == true)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      '${S.of(context).kDiscountPercentage}: ${service.discount}%',
                      style: TextSize.xxs.semiBoldStyle.copyWith(
                          color: isSelected
                              ? Colors.white
                              : AppColors.primaryColor),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: onPressed,
            icon: const Icon(
              Icons.edit,
            ),
          )
        ],
      ),
    );
  }
}

class ANPackageCard extends StatelessWidget {
  final ANServicesModel package;
  final bool isSelected;
  final void Function() onPressed;
  const ANPackageCard(
      {Key? key,
      required this.package,
      required this.isSelected,
      required this.onPressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Function to format price display
    String getPriceDisplay() {
      if (package.minPrice != null && package.maxPrice != null) {
        // Price range
        return '${package.minPrice!.toStringAsFixed(0)} - ${package.maxPrice!.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else if (package.minPrice != null) {
        // Fixed price (min only)
        return '${package.minPrice!.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else if (package.maxPrice != null) {
        // Fixed price (max only)
        return '${package.maxPrice!.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else if (package.price > 0) {
        // Legacy price field
        return '${package.price.toStringAsFixed(0)} ${S.of(context).kEGP}';
      } else {
        // No price set
        return '';
      }
    }
    
    // Check if we should show price
    final bool shouldShowPrice = package.minPrice != null || package.maxPrice != null || package.price > 0;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 21.0, horizontal: 23.0),
      decoration: BoxDecoration(
        borderRadius: circularBorderXXS,
        color: isSelected ? AppColors.primaryColor : Colors.white,
      ),
      child: Row(
        children: [
          if (package.image != null)
            SizedBox(
              width: 50.0,
              child: Image.network(
                package.image!.url.replaceAll(
                  "localhost",
                  Config.IP,
                ),
              ),
            ),
          gapWL,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      package.title,
                      style: GoogleFonts.philosopher(
                          textStyle: TextSize.s.boldStyle.copyWith(
                              color: isSelected ? Colors.white : Colors.black)),
                    ),
                    if (shouldShowPrice)
                      Text(
                        getPriceDisplay(),
                        style: GoogleFonts.philosopher(
                            textStyle: TextSize.xs.boldStyle.copyWith(
                                color: isSelected
                                    ? Colors.white
                                    : AppColors.primaryColor)),
                      )
                  ],
                ),
                gapHL,
                Text(
                  package.description,
                  style: TextSize.xxs.regularStyle.copyWith(
                      color: isSelected ? Colors.white : Colors.black),
                  textAlign: TextAlign.start,
                ),
                //if package.discountAction is true then add this
                if (package.discountActive == true)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      '${S.of(context).kDiscountPercentage}: ${package.discount}%',
                      style: TextSize.xxs.semiBoldStyle.copyWith(
                          color: isSelected
                              ? Colors.white
                              : AppColors.primaryColor),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: onPressed,
            icon: const Icon(
              Icons.edit,
            ),
          )
        ],
      ),
    );
  }
}
