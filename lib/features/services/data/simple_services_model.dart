import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';

// A simplified version of ANServicesModel that includes dateSpecificAvailability
// This is a workaround until we can properly regenerate the freezed files
class SimpleServicesModel {
  final String id;
  final String title;
  final String description;
  final double price;
  final double? minPrice;
  final double? maxPrice;
  final double? discount;
  final bool? discountActive;
  final ANImageModel? image;
  final bool isPackage;
  final List<ANScheduleModel> schedule;
  final List<dynamic> dateSpecificAvailability;

  SimpleServicesModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    this.minPrice,
    this.maxPrice,
    this.discount,
    this.discountActive,
    this.image,
    required this.isPackage,
    this.schedule = const [],
    this.dateSpecificAvailability = const [],
  });

  // Convert from regular ANServicesModel to our simplified model
  factory SimpleServicesModel.fromANServicesModel(dynamic model) {
    return SimpleServicesModel(
      id: model.id,
      title: model.title,
      description: model.description,
      price: model.price,
      minPrice: model.minPrice,
      maxPrice: model.maxPrice,
      discount: model.discount,
      discountActive: model.discountActive,
      image: model.image,
      isPackage: model.isPackage,
      schedule: model.schedule,
      // dateSpecificAvailability will be populated separately
    );
  }

  // Convert to a Map for API operations
  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "title": title,
      "description": description,
      "price": price,
      "minPrice": minPrice,
      "maxPrice": maxPrice,
      "discount": discount,
      "discountActive": discountActive,
      "image": image?.id,
      "isPackage": isPackage,
      "schedule": schedule.map((s) => {
        "day": s.day.name,
        "slots": s.slots.map((slot) => {
          "id": slot.id,
          "starts": slot.starts.toIso8601String(),
          "ends": slot.ends?.toIso8601String() ?? slot.starts.add(const Duration(minutes: 30)).toIso8601String(),
        }).toList(),
      }).toList(),
      "dateSpecificAvailability": dateSpecificAvailability,
    };
  }
} 