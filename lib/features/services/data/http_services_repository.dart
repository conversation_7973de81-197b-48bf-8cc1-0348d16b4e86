import 'dart:convert';
import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/features/services/data/simple_services_model.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/providers/providers.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_location/service_location.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/statistics/statistics.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:ajmal_now_doctor/utils/http_error_message.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/features/services/domain/service_wrapper.dart';

class ANHttpServicesRepository {
  final Ref ref;

  ANHttpServicesRepository({required this.ref});

  Future<List<ANServiceModel>> getServiceByOwnerId() async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      final ANResponseModel response = await ref.read(networkProvider).getData(
        path: 'services',
        headers: {
          'Content-Type': 'application/json',
          'X-localization': locale,
          'locale': locale,
        },
        queryParam: {
          'where[owners.user][equals]': ref.read(authRepositoryProvider).currentUser!.id,
        },
      );
      
      print("Services API Response: ${response.statusCode}");
      
      if (response.statusCode == 200 && response.data != null) {
        final List<ANServiceModel> serviceList = [];
        
        try {
          final docs = response.data!['docs'] as List<dynamic>;
          print("Number of docs received: ${docs.length}");
          
          // Process each service individually to prevent one bad service from breaking everything
          for (int i = 0; i < docs.length; i++) {
            try {
              print("Processing service $i of ${docs.length}");
              final e = docs[i];
              final service = ANServiceModel.fromJson(e);
              
              // Store offers in our cache if available
              try {
                final offersValue = e['offers'] as String?;
                if (offersValue != null) {
                  setOffers(service, offersValue);
                }
              } catch (offersError) {
                print("Error processing offers for service $i: $offersError");
                // Continue despite offers error
              }
              
              serviceList.add(service);
              print("Successfully processed service $i: ${service.name}");
            } catch (serviceError) {
              print("Error processing service $i: $serviceError");
              // Continue to next service
            }
          }
          
          print("Successfully processed ${serviceList.length} out of ${docs.length} services");
          return serviceList;
        } catch (docsError) {
          print("Error processing service docs: $docsError");
          print("Response data: ${response.data}");
          // Return empty list if docs processing fails completely
          return [];
        }
      }
      throw getErrorMessage(response);
    } catch (e) {
      print("Error in getServiceByOwnerId: $e");
      rethrow; // Changed from returning empty list to rethrowing
    }
  }

  Future<ANServiceProviderStatisticsModel> getServiceStatisticsById(
      {required String serviceId}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'services/$serviceId/stats',
          );
      if (response.statusCode == 200 && response.data != null) {
        return ANServiceProviderStatisticsModel.fromJson(response.data!);
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<ANImageModel> addPhoto({required MultipartFile? image}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).postData(
        path: 'service_photos',
        files: {
          "file": image,
        },
      );
      if (response.statusCode == 201 && response.data != null) {
        return ANImageModel.fromJson(response.data!['doc']);
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> createBranch({
    String? serviceName,
    String? description,
    String? offers,
    String? type,
    ServiceLocation? location,
    bool? homeVisit,
    String? logo,
    List<Map<String, dynamic>>? gallery,
    LatLng? coordinates,
  }) async {
    try {
      // Use the new toApiJson method that properly formats for the backend
      final locationData = location!.toApiJson();

      print("Location data: $locationData");

      final ANResponseModel locationResponse =
          await ref.read(networkProvider).postData(
                path: 'service_locations',
                headers: {'Content-Type': 'application/json'},
                body: locationData,
              );

      print("Location response: ${locationResponse.data}");

      if (locationResponse.statusCode == 201 && locationResponse.data != null) {
        ServiceLocation createdLocation =
            ServiceLocation.fromJson(locationResponse.data!['doc']);

        final providerResponse = await ref.read(networkProvider).getData(
          path: 'providers',
          headers: {'Content-Type': 'application/json'},
          queryParam: {
            'where[user][equals]':
                ref.read(authRepositoryProvider).currentUser!.id,
          },
        );

        if (providerResponse.statusCode == 200 &&
            providerResponse.data != null) {
          ANProviderModel provider =
              ANProviderModel.fromJson(providerResponse.data!['docs'][0]);

          Map<String, dynamic> serviceData = {
            "rating": 0,
            "owners": [provider.id],
            "location": createdLocation.id,
          };

          // Only add non-null fields
          if (serviceName != null) serviceData["name"] = serviceName;
          if (description != null) serviceData["description"] = description;
          if (offers != null && offers.isNotEmpty) serviceData["offers"] = offers;
          if (type != null) serviceData["type"] = type;
          if (coordinates != null) {
            serviceData["pin_location"] = [
              coordinates.latitude,
              coordinates.longitude
            ];
          }
          if (logo != null) serviceData["logo"] = logo;
          if (gallery != null && gallery.isNotEmpty) {
            serviceData["gallery"] = gallery;
            serviceData["gallery_limit"] = 10;
          }

          print("Service data: $serviceData");

          final branchResponse = await ref.read(networkProvider).postData(
                path: 'services',
                headers: {'Content-Type': 'application/json'},
                body: serviceData,
              );

          print("Branch response: ${branchResponse.data}");

          if (branchResponse.statusCode == 201) return true;
        }
        throw getErrorMessage(providerResponse);
      }

      if (locationResponse.statusCode == 400 && locationResponse.data != null) {
        final errors = locationResponse.data!['errors'] as List;
        if (errors.isNotEmpty) {
          final errorMessages = errors.first['data'] as List;
          if (errorMessages.isNotEmpty) {
            throw errorMessages.map((e) => e['message']).join('\n');
          }
        }
      }

      throw getErrorMessage(locationResponse);
    } catch (e) {
      print("Error creating branch: $e");
      rethrow;
    }
  }

  Future<bool> updateBranch({
    String? serviceName,
    String? description,
    String? offers,
    String? type,
    ServiceLocation? location,
    bool? homeVisit,
    String? logo,
    List<Map<String, dynamic>>? gallery,
    String? serviceId,
    LatLng? coordinates,
  }) async {
    try {
      // Use the new toApiJson method that properly formats for the backend
      final locationData = location!.toApiJson();
      
      final ANResponseModel locationResponse =
          await ref.read(networkProvider).patchData(
        path: 'service_locations/${location.id}',
        headers: {
          'Content-Type': 'application/json',
        },
        body: locationData,
      );
      if (locationResponse.statusCode == 200 && locationResponse.data != null) {
        Map<String, dynamic> body = {
          "name": serviceName,
          "description": description,
          "type": type,
          "location": location.id,
          "home_visit": homeVisit,
          "rating": 0,
          "logo": logo,
          "gallery_limit": 10,
          "gallery": gallery,
        };
        
        if (offers != null && offers.isNotEmpty) {
          body["offers"] = offers;
        }
        
        if (coordinates != null) {
          body["pin_location"] = [coordinates.latitude, coordinates.longitude];
        }
        
        final ANResponseModel response =
            await ref.read(networkProvider).patchData(
          path: 'services/$serviceId',
          headers: {
            'Content-Type': 'application/json',
          },
          body: body,
        );
        if (response.statusCode == 200) {
          return true;
        }
        throw getErrorMessage(response);
      }
      
      if (locationResponse.statusCode == 400 && locationResponse.data != null) {
        final errors = locationResponse.data!['errors'] as List;
        if (errors.isNotEmpty) {
          final errorMessages = errors.first['data'] as List;
          if (errorMessages.isNotEmpty) {
            throw errorMessages.map((e) => e['message']).join('\n');
          }
        }
      }
      
      throw getErrorMessage(locationResponse);
    } catch (e) {
      print("Error updating branch: $e");
      rethrow;
    }
  }

  Future<bool> deleteBranch({required String serviceId}) async {
    try {
      final ANResponseModel response =
          await ref.read(networkProvider).deleteData(
                path: 'services/$serviceId',
              );
      if (response.statusCode == 200) {
        return true;
      }
      throw getErrorMessage(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<ANServiceModel> createService({
    ANServicesModel? newService,
    String? serviceId,
  }) async {
    try {
      final ANResponseModel res = await ref.read(networkProvider).getData(
            path: 'services/$serviceId',
          );
      if (res.statusCode == 200 && res.data != null) {
        ANServiceModel service = ANServiceModel.fromJson(res.data!);
        List<ANServicesModel> services = [];
        services.addAll(service.services);
        services.add(newService!);
        Map<String, dynamic> body = {
          "services": services
              .map((e) => {
                    "title": e.title,
                    "description": e.description,
                    "price": e.price,
                    "minPrice": e.minPrice,
                    "maxPrice": e.maxPrice,
                    "discountActive": e.discountActive,
                    "isPackage": newService.isPackage,
                    "discount": e.discount,
                    "image": e.image?.id,
                    "schedule": e.schedule
                        .map((s) => {
                              "day": s.day.name,
                              "slots": s.slots
                                  .map((slot) => {
                                        "id": slot.id,
                                        "starts": slot.starts,
                                        "ends": slot.ends,
                                      })
                                  .toList(),
                            })
                        .toList(),
                  })
              .toList(),
        };
        final ANResponseModel response =
            await ref.read(networkProvider).patchData(
                  path: 'services/$serviceId',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: body,
                );
        if (response.statusCode == 200) {
          return ANServiceModel.fromJson(response.data!['doc']);
        } else {
          throw getErrorMessage(response);
        }
      } else {
        throw getErrorMessage(res);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> updateService({
    ANServicesModel? newService,
    String? serviceId,
  }) async {
    try {
      print("[UpdateService] Updating service: $serviceId, service title: ${newService?.title}");
      
      // First get the existing service to preserve dateSpecificAvailability
      final ANResponseModel res = await ref.read(networkProvider).getData(
            path: 'services/$serviceId',
          );
          
      if (res.statusCode == 200 && res.data != null) {
        ANServiceModel service = ANServiceModel.fromJson(res.data!);
        List<Map<String, dynamic>> updatedServices = [];
        
        // Process each service in the original service list
        if (res.data!['services'] is List) {
          for (var originalServiceData in res.data!['services']) {
            if (originalServiceData['id'] == newService!.id) {
              // This is the service being updated
              print("[UpdateService] Found service to update: ${originalServiceData['id']}");
              
              // Create a new service object with the updates
              Map<String, dynamic> updatedService = {
                "id": newService.id,
                "title": newService.title,
                "description": newService.description,
                "price": newService.price,
                "minPrice": newService.minPrice,
                "maxPrice": newService.maxPrice,
                "discount": newService.discount,
                "discountActive": newService.discountActive,
                "isPackage": newService.isPackage,
                "image": newService.image?.id,
                "schedule": newService.schedule.map((s) => s.toJson()).toList(),
              };
              
              // Preserve the dateSpecificAvailability if it exists in the original data
              if (originalServiceData.containsKey('dateSpecificAvailability')) {
                print("[UpdateService] Preserving dateSpecificAvailability with ${originalServiceData['dateSpecificAvailability']?.length ?? 0} entries");
                updatedService['dateSpecificAvailability'] = originalServiceData['dateSpecificAvailability'];
              }
              
              updatedServices.add(updatedService);
            } else {
              // Keep other services as they were
              updatedServices.add(originalServiceData);
            }
          }
        }
        
        // Prepare the updated service
        Map<String, dynamic> bodyData = {
          'services': updatedServices,
        };
        
        print("[UpdateService] Sending update to server with ${updatedServices.length} services");
        final response = await ref.read(networkProvider).patchData(
          path: 'services/$serviceId',
          body: bodyData,
        );
        
        if (response.statusCode == 200) {
          print("[UpdateService] Service updated successfully");
          return true;
        } else {
          print("[UpdateService] Error updating service: ${response.statusCode}");
          return false;
        }
      } else {
        print("[UpdateService] Could not get current service data");
        return false;
      }
    } catch (e) {
      print("[UpdateService] Exception when updating service: $e");
      return false;
    }
  }

  Future<bool> deleteService({
    String? serviceId,
    String? removeServiceId,
  }) async {
    try {
      final ANResponseModel res = await ref.read(networkProvider).getData(
            path: 'services/$serviceId',
          );
      if (res.statusCode == 200 && res.data != null) {
        ANServiceModel service = ANServiceModel.fromJson(res.data!);
        List<ANServicesModel> services = [];
        for (ANServicesModel s in service.services) {
          if (s.id == removeServiceId) {
            continue;
          } else {
            services.add(s);
          }
        }
        Map<String, dynamic> body = {
          "services": services
              .map((e) => {
                    "title": e.title,
                    "description": e.description,
                    "price": e.price,
                    "minPrice": e.minPrice,
                    "maxPrice": e.maxPrice,
                    "discountActive": e.discountActive,
                    "isPackage": false,
                    "discount": e.discount,
                    "image": e.image?.id,
                    "schedule": e.schedule
                        .map((s) => {
                              "day": s.day.name,
                              "slots": s.slots
                                  .map((slot) => {
                                        "id": slot.id,
                                        "starts": slot.starts.toIso8601String(),
                                        "ends": slot.ends?.toIso8601String() ?? slot.starts.add(const Duration(minutes: 30)).toIso8601String(),
                                      })
                                  .toList(),
                            })
                        .toList(),
                  })
              .toList(),
        };
        
        // Use the network helper instead of direct HTTP call for consistent headers
        final ANResponseModel response = await ref.read(networkProvider).patchData(
          path: 'services/$serviceId',
          headers: {
            'Content-Type': 'application/json',
          },
          body: body,
        );
        
        if (response.statusCode == 200) {
          return true;
        }
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }

  Future<ANServiceModel> getServiceProviderById(
      {required String serviceProviderId}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'services/$serviceProviderId',
          );
      if (response.statusCode == 200 && response.data != null) {
        try {
          return ANServiceModel.fromJson(response.data!);
        } catch (parseError) {
          print("Error parsing service provider: $parseError");
          print("Response data: ${response.data}");
          throw "Failed to parse service provider data";
        }
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      print("Error in getServiceProviderById: $e");
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getServiceOwnersById(
      {required String serviceProviderId}) async {
    try {
      final service =
          await getServiceProviderById(serviceProviderId: serviceProviderId);
      List<Map<String, dynamic>> owners = [];
      for (ANProviderModel model in service.owners) {
        final response = await ref
            .read(networkProvider)
            .getData(path: 'users/${model.user}', headers: {
          'Content-Type': 'application/json',
        });
        if (response.statusCode == 200) {
          Map<String, dynamic> owner = {};
          owner["full_name"] = response.data!["full_name"];
          owner["biography"] = model.biography;
          owners.add(owner);
        }
      }
      return owners;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<ANSlotsModel>> getServiceTimeSlots(
      {required String serviceId, required DateTime date}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
          path: 'services/$serviceId/slots',
          queryParam: {'date': date.toIso8601String()});
      if (response.statusCode == 200 && response.data != null) {
        return (response.data!['slots'] as List)
            .map((e) => ANSlotsModel.fromJson(e))
            .toList();
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<List<ANServiceTypesModel>> getProviderTypes() async {
    try {
      final ANResponseModel response = await ref
          .read(networkProvider)
          .getData(path: 'provider_types', queryParam: {
        'limit': 100,
      });
      if (response.statusCode == 200 && response.data != null) {
        return (response.data!['docs'] as List)
            .map((e) => ANServiceTypesModel.fromJson(e))
            .toList();
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<List<ServiceLocation>> getLocations() async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'service_locations',
          );
      if (response.statusCode == 200 && response.data != null) {
        return (response.data!['docs'] as List)
            .map((e) => ServiceLocation.fromJson(e))
            .toList();
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }
}

final httpServicesProvider = Provider<ANHttpServicesRepository>(
    (ref) => ANHttpServicesRepository(ref: ref));
