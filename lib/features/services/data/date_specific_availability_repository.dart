import 'dart:convert';
import 'package:ajmal_now_doctor/common/domain/availability/date_specific_availability.dart';
import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANDateSpecificAvailabilityRepository {
  final Ref ref;

  ANDateSpecificAvailabilityRepository({required this.ref});

  Future<void> saveDateSpecificAvailability({
    required String providerId,
    required String serviceId,
    required DateTime date,
    required List<ANSlotsModel> slots,
  }) async {
    try {
      print("[Repository] Saving availability - Provider: $providerId, Service: $serviceId, Date: ${date.toIso8601String()}, Slots: ${slots.length}");
      
      // Use toMapForServer to ensure consistent local time formatting
      final slotsData = slots.map((slot) => slot.toMapForServer()).toList();
      
      print("[Repository] Formatted slots data: $slotsData");
      
      await ref.read(networkProvider).postJsonData(
        path: 'services/$providerId/date-specific-availability',
        body: {
          'serviceId': serviceId,
          'date': date.toIso8601String(),
          'slots': slotsData,
        },
      );
      
      print("[Repository] Successfully saved availability for service $serviceId");
    } catch (e) {
      print("[Repository] Error saving availability: $e");
      rethrow;
    }
  }
  
  Future<Map<String, dynamic>> getDateSpecificAvailability({
    required String providerId,
    required String serviceId,
    required DateTime date,
  }) async {
    try {
      print("[Repository] Getting availability - Provider: $providerId, Service: $serviceId, Date: ${date.toIso8601String()}");
      
      final response = await ref.read(networkProvider).getData(
        path: 'services/$providerId/date-specific-availability',
        queryParam: {
          'serviceId': serviceId,
          'date': date.toIso8601String(),
        },
      );
      
      print("[Repository] Got availability response: ${response.statusCode}");
      return response.data ?? {};
    } catch (e) {
      print("[Repository] Error getting availability: $e");
      // Return an empty result instead of throwing, so UI can still function
      return {'exists': false, 'error': e.toString()};
    }
  }

  Future<List<ANAvailabilityDateInfo>> getMonthlyAvailability({
    required String providerId,
    required String serviceId,
    required int year,
    required int month,
  }) async {
    try {
      print("[Repository] Getting monthly availability - Provider: $providerId, Service: $serviceId, Year: $year, Month: $month");
      
      final response = await ref.read(networkProvider).getData(
        path: 'services/$providerId/monthly-availability',
        queryParam: {
          'serviceId': serviceId,
          'year': year.toString(),
          'month': month.toString(),
        },
      );

      final data = response.data ?? {};
      final availabilityDates = data['availabilityDates'] as List? ?? [];
      
      print("[Repository] Got ${availabilityDates.length} availability dates");
      return availabilityDates
          .map((item) {
            // Parse the UTC date and convert to local time for proper calendar comparison
            final utcDate = DateTime.parse(item['date']);
            final localDate = utcDate.toLocal();
            print("[Repository] Converting UTC date ${item['date']} to local date: ${localDate.toString()}");
            
            return ANAvailabilityDateInfo(
              date: localDate,
              hasSlots: item['hasSlots'] as bool,
              isAvailable: item['isAvailable'] as bool,
            );
          })
          .toList();
    } catch (e) {
      print("[Repository] Error getting monthly availability: $e");
      // Return an empty list instead of throwing
      return [];
    }
  }
}

final dateSpecificAvailabilityProvider = Provider.autoDispose((ref) => 
  ANDateSpecificAvailabilityRepository(ref: ref));

final monthlyAvailabilityProvider = 
    FutureProvider.autoDispose.family<List<ANAvailabilityDateInfo>, Map<String, dynamic>>(
  (ref, params) {
    final Map<String, dynamic> decodedParams = 
        params is String ? jsonDecode(params as String) : params;
        
    final String providerId = decodedParams['providerId'];
    final String serviceId = decodedParams['serviceId'];
    final int year = decodedParams['year'];
    final int month = decodedParams['month'];
    
    return ref.read(dateSpecificAvailabilityProvider).getMonthlyAvailability(
      providerId: providerId,
      serviceId: serviceId,
      year: year,
      month: month,
    );
  },
);

final dateSpecificAvailabilityDataProvider = 
    FutureProvider.autoDispose.family<Map<String, dynamic>, Map<String, dynamic>>(
  (ref, params) {
    final Map<String, dynamic> decodedParams = 
        params is String ? jsonDecode(params as String) : params;
        
    final String providerId = decodedParams['providerId'];
    final String serviceId = decodedParams['serviceId'];
    final DateTime date = DateTime.parse(decodedParams['date']);
    
    return ref.read(dateSpecificAvailabilityProvider).getDateSpecificAvailability(
      providerId: providerId,
      serviceId: serviceId,
      date: date,
    );
  },
); 