import 'dart:convert';

import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/config.dart';
import 'package:ajmal_now_doctor/features/services/data/http_services_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_location/service_location.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/statistics/statistics.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class ANServicesRepository extends ChangeNotifier {
  final Ref ref;

  bool loading = false;

  ANServicesRepository({required this.ref});

  List<ANServiceModel> services = [];
  ANServiceModel? selectedBranch;
  ANServicesModel? selectedService;
  List<ANServiceTypesModel> providerTypes = [];
  ANServiceTypesModel? selectedProviderType;
  List<ANImageModel> gallery = [];
  ANImageModel? logo;

  Future<List<ANServiceModel>> getServiceByOwnerId() async {
    try {
      loading = true;
      notifyListeners();
      final List<ANServiceModel> data =
          await ref.read(httpServicesProvider).getServiceByOwnerId();

      services = data;
      loading = false;
      Future.microtask(() => notifyListeners());
      return services;
    } catch (e) {
      print("Error fetching services: $e");
      rethrow;
    }
  }

  void resetData() {
    services.clear();
    selectedBranch = null;
    notifyListeners();
  }

  Future<void> reloadData() async {
    services.clear();
    selectedBranch = null;
    selectedProviderType = null;
    gallery.clear();
    logo = null;
    Future.microtask(() => notifyListeners());
  }

  Future<bool> addPhoto(MultipartFile? image, bool toGallery) async {
    try {
      final ANImageModel data =
          await ref.read(httpServicesProvider).addPhoto(image: image);
      if (data.url.isNotEmpty) {
        String url = data.url;
        url = url.replaceAll("localhost", Config.IP.toString());
        ANImageModel imageModel =
            ANImageModel(url: url, id: data.id, fileName: data.fileName);
        if (toGallery) {
          gallery.add(imageModel);
        } else {
          logo = imageModel;
        }
        notifyListeners();
        return true;
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> createBranch({
    String? serviceName,
    String? description,
    String? offers,
    String? type,
    ServiceLocation? location,
    bool? homeVisit,
    LatLng? coordinates,
  }) async {
    try {
      List<Map<String, dynamic>> galleryData = [];
      for (var element in gallery) {
        galleryData.add({
          "logo": element.id,
        });
      }
      final bool result = await ref.read(httpServicesProvider).createBranch(
            serviceName: serviceName,
            description: description,
            offers: offers,
            type: type,
            location: location,
            homeVisit: homeVisit,
            logo: logo!.id,
            gallery: galleryData,
            coordinates: coordinates,
          );

      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> updateBranch({
    String? serviceName,
    String? description,
    String? offers,
    String? type,
    ServiceLocation? location,
    bool? homeVisit,
    LatLng? coordinates,
  }) async {
    try {
      List<Map<String, dynamic>> galleryData = [];
      for (var element in gallery) {
        galleryData.add({
          "logo": element.id,
        });
      }
      final bool result = await ref.read(httpServicesProvider).updateBranch(
            serviceName: serviceName,
            description: description,
            offers: offers,
            type: type,
            location: location,
            homeVisit: homeVisit,
            logo: logo!.id,
            gallery: galleryData,
            serviceId: selectedBranch!.id,
            coordinates: coordinates,
          );
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteBranch() async {
    try {
      final bool result = await ref
          .read(httpServicesProvider)
          .deleteBranch(serviceId: selectedBranch!.id);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ANServiceModel> createService({
    ANServicesModel? newService,
  }) async {
    try {
      final ANServiceModel result =
          await ref.read(httpServicesProvider).createService(
                serviceId: selectedBranch!.id,
                newService: newService,
              );
      print(
          "Service createdddddddddddddddddddddddddddddddddddddddddddddddd: ${result.id}");
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> updateService({
    ANServicesModel? newService,
  }) async {
    try {
      final bool result = await ref.read(httpServicesProvider).updateService(
            serviceId: selectedBranch!.id,
            newService: newService,
          );

      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteService() async {
    try {
      final bool result = await ref.read(httpServicesProvider).deleteService(
          serviceId: selectedBranch!.id, removeServiceId: selectedService!.id);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  void resetAll() {
    services.clear();
    selectedBranch = null;
    providerTypes.clear();
    selectedProviderType = null;
    gallery.clear();
    logo = null;
    notifyListeners();
  }

  void selectBranch(ANServiceModel serviceProvider) {
    selectedBranch = serviceProvider;
    notifyListeners();
  }

  void selectService(ANServicesModel serviceProvider) {
    selectedService = serviceProvider;
    notifyListeners();
  }

  void selectProviderType(ANServiceTypesModel providerType) {
    selectedProviderType = providerType;
    notifyListeners();
  }

  Future<ANServiceProviderStatisticsModel> getServiceStatisticsById() async {
    try {
      return await ref
          .read(httpServicesProvider)
          .getServiceStatisticsById(serviceId: selectedBranch!.id);
    } catch (e) {
      rethrow;
    }
  }

  Future<List> getServiceTimeSlots(
      {required String serviceId, required DateTime date}) async {
    try {
      return await ref
          .read(httpServicesProvider)
          .getServiceTimeSlots(serviceId: serviceId, date: date);
    } catch (e) {
      rethrow;
    }
  }

  Future<List<ANServiceTypesModel>> getProviderTypes() async {
    try {
      if (providerTypes.isEmpty) {
        final List<ANServiceTypesModel> data =
            await ref.read(httpServicesProvider).getProviderTypes();
        if (data.isNotEmpty) {
          providerTypes.addAll(data);
        }
      }
      return providerTypes;
    } catch (e) {
      rethrow;
    }
  }

  Future<ANServiceModel> getServiceProviderById() async {
    try {
      loading = true;
      notifyListeners();
      ANServiceModel data = await ref
          .read(httpServicesProvider)
          .getServiceProviderById(serviceProviderId: selectedBranch!.id);
      loading = false;
      notifyListeners();
      return data;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getServiceOwnersById() async {
    try {
      return await ref
          .read(httpServicesProvider)
          .getServiceOwnersById(serviceProviderId: selectedBranch!.id);
    } catch (e) {
      rethrow;
    }
  }

  void deletePhoto(int index) {
    gallery.removeAt(index);
    notifyListeners();
  }
}

final servicesProvider =
    ChangeNotifierProvider.autoDispose<ANServicesRepository>(
        (ref) => ANServicesRepository(ref: ref));

final servicesDataProvider = FutureProvider.autoDispose<List<ANServiceModel>>(
    (ref) async => await ref.read(servicesProvider).getServiceByOwnerId());

final serviceProviderStatisticsByIdProvider =
    FutureProvider.autoDispose<ANServiceProviderStatisticsModel>(
        (ref) async => ref.read(servicesProvider).getServiceStatisticsById());

final providerTypesProvider =
    FutureProvider.autoDispose<List<ANServiceTypesModel>>(
        (ref) async => ref.read(servicesProvider).getProviderTypes());

final serviceTimeSlotsProvider =
    FutureProvider.family.autoDispose((ref, String data) async {
  var decodeData = jsonDecode(data);
  return ref.read(servicesProvider).getServiceTimeSlots(
      serviceId: decodeData['serviceId'],
      date: DateTime.parse(decodeData['date']));
});

final serviceProviderByIdProvider = FutureProvider.autoDispose<ANServiceModel>(
    (ref) async => ref.read(servicesProvider).getServiceProviderById());

// Add branchProvider to make the service branch available in the edit screen
final branchProvider = Provider.autoDispose<ANServiceModel>((ref) {
  final services = ref.watch(servicesProvider);
  return services.selectedBranch!;
});
