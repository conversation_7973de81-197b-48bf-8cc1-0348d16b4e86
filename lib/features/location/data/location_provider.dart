import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../common/domain/location/city_model.dart';
import '../../../helpers/networking/api_services/location_api_service.dart';

// State classes
class LocationState {
  final List<CityDistrict> districts;
  final List<CityInfo> cities;
  final String? selectedCityCode;
  final CityDistrict? selectedDistrict;
  final bool isLoading;
  final String? error;

  LocationState({
    this.districts = const [],
    this.cities = const [],
    this.selectedCityCode,
    this.selectedDistrict,
    this.isLoading = false,
    this.error,
  });

  LocationState copyWith({
    List<CityDistrict>? districts,
    List<CityInfo>? cities,
    String? selectedCityCode,
    CityDistrict? selectedDistrict,
    bool? isLoading,
    String? error,
  }) {
    return LocationState(
      districts: districts ?? this.districts,
      cities: cities ?? this.cities,
      selectedCityCode: selectedCityCode ?? this.selectedCityCode,
      selectedDistrict: selectedDistrict ?? this.selectedDistrict,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

// State notifier
class LocationNotifier extends StateNotifier<LocationState> {
  final Ref ref;
  
  LocationNotifier(this.ref) : super(LocationState());

  // Fetch all districts and extract unique cities
  Future<void> fetchDistrictsAndCities() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await ref.read(locationApiServiceProvider).fetchCityDistricts();
      
      if (response.statusCode == 200 && response.data != null) {
        final districtsData = response.data!['docs'] as List? ?? [];
        final districts = districtsData
            .map((districtJson) => CityDistrict.fromJson(districtJson))
            .where((district) => district.isActive)
            .toList();
        
        // Sort districts by city and display order
        districts.sort((a, b) {
          final cityCompare = a.city.compareTo(b.city);
          if (cityCompare != 0) return cityCompare;
          return a.displayOrder.compareTo(b.displayOrder);
        });

        // Extract unique cities
        final uniqueCityCodes = districts.map((d) => d.city).toSet().toList();
        final cities = uniqueCityCodes
            .map((code) {
              // Try to get predefined city info first
              final predefinedCity = CityInfo.getByCode(code);
              if (predefinedCity != null) {
                return predefinedCity;
              }
              // If not predefined, create city info from the district data
              final firstDistrict = districts.firstWhere((d) => d.city == code);
              return CityInfo(
                code: code,
                displayName: firstDistrict.cityDisplayName,
                displayNameAr: firstDistrict.cityDisplayNameAr,
              );
            })
            .toList();

        // Sort cities: predefined cities first, then others alphabetically
        cities.sort((a, b) {
          final aIndex = CityInfo.predefinedCities.indexWhere((c) => c.code == a.code);
          final bIndex = CityInfo.predefinedCities.indexWhere((c) => c.code == b.code);
          
          if (aIndex != -1 && bIndex != -1) {
            // Both are predefined, sort by predefined order
            return aIndex.compareTo(bIndex);
          } else if (aIndex != -1) {
            // Only a is predefined, a comes first
            return -1;
          } else if (bIndex != -1) {
            // Only b is predefined, b comes first
            return 1;
          } else {
            // Neither is predefined, sort alphabetically
            return a.displayName.compareTo(b.displayName);
          }
        });
        
        state = state.copyWith(
          districts: districts,
          cities: cities,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to fetch districts',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch districts: $e',
      );
    }
  }

  // Select a city by code
  void selectCity(String cityCode) {
    state = state.copyWith(
      selectedCityCode: cityCode,
      selectedDistrict: null, // Clear district when city changes
    );
  }

  // Select a district
  void selectDistrict(CityDistrict district) {
    state = state.copyWith(
      selectedDistrict: district,
      selectedCityCode: district.city, // Ensure city is also set
    );
  }

  // Get districts for the selected city
  List<CityDistrict> getSelectedCityDistricts() {
    if (state.selectedCityCode == null) return [];
    
    return state.districts
        .where((district) => 
            district.city == state.selectedCityCode && district.isActive)
        .toList()
      ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
  }

  // Get districts for a specific city
  List<CityDistrict> getDistrictsForCity(String cityCode) {
    return state.districts
        .where((district) => 
            district.city == cityCode && district.isActive)
        .toList()
      ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
  }

  // Clear selections
  void clearSelections() {
    state = state.copyWith(
      selectedCityCode: null,
      selectedDistrict: null,
    );
  }

  // Get district by ID
  CityDistrict? getDistrictById(String districtId) {
    try {
      return state.districts.firstWhere((district) => district.id == districtId);
    } catch (e) {
      return null;
    }
  }

  // Get district by code
  CityDistrict? getDistrictByCode(String districtCode) {
    try {
      return state.districts.firstWhere((district) => district.districtCode == districtCode);
    } catch (e) {
      return null;
    }
  }

  // Get city info by code
  CityInfo? getCityByCode(String cityCode) {
    try {
      return state.cities.firstWhere((city) => city.code == cityCode);
    } catch (e) {
      return null;
    }
  }
}

// Providers
final locationProvider = StateNotifierProvider<LocationNotifier, LocationState>(
  (ref) => LocationNotifier(ref),
);

// Helper providers
final districtsProvider = Provider<List<CityDistrict>>((ref) {
  return ref.watch(locationProvider).districts;
});

final citiesProvider = Provider<List<CityInfo>>((ref) {
  return ref.watch(locationProvider).cities;
});

final selectedCityCodeProvider = Provider<String?>((ref) {
  return ref.watch(locationProvider).selectedCityCode;
});

final selectedDistrictProvider = Provider<CityDistrict?>((ref) {
  return ref.watch(locationProvider).selectedDistrict;
});

final selectedCityDistrictsProvider = Provider<List<CityDistrict>>((ref) {
  final locationNotifier = ref.watch(locationProvider.notifier);
  return locationNotifier.getSelectedCityDistricts();
}); 