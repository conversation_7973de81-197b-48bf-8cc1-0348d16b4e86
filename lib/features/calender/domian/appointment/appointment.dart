import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/calender/data/calender_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_location/service_location.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service_types/service_types.dart';

// Manual implementation instead of freezed
class ANAppointmentModel {
  final String? id;
  final String purpose;
  final DateTime appointmentDate;
  final ANUserModel client;
  final ANServiceModel service;
  final HistoryStatus status;
  final String? token;
  final DateTime? arrivedAt;

  ANAppointmentModel({
    this.id,
    required this.purpose,
    required this.appointmentDate,
    required this.client,
    required this.service,
    required this.status,
    this.token,
    this.arrivedAt,
  });

  factory ANAppointmentModel.fromJson(Map<String, dynamic> json) {
    try {
      return ANAppointmentModel(
        id: json['id'] as String?,
        purpose: json['purpose'] as String? ?? 'Consultation',
        appointmentDate: json['appointment_date'] != null 
          ? DateTime.parse(json['appointment_date'] as String)
          : DateTime.now(),
        client: json['client'] != null
          ? ANUserModel.fromJson(json['client'] as Map<String, dynamic>)
          : ANUserModel(
              id: '',
              firstName: 'Unknown',
              lastName: 'Client',
              fullName: 'Unknown Client',
              email: '',
              phoneNumber: '',
              gender: Gender.other,
              role: 'client',
            ),
        service: json['service'] != null
          ? ANServiceModel.fromJson(json['service'] as Map<String, dynamic>)
          : ANServiceModel(
              id: '',
              name: 'Unknown Service',
              type: ANServiceTypesModel.empty(),
              location: ServiceLocation.empty(),
              homeVisit: false,
              owners: [],
              services: [],
              gallery: [],
            ),
        status: json['status'] != null
          ? HistoryStatusExtension.fromString(json['status'] as String)
          : HistoryStatus.pending,
        token: json['token'] as String?,
        arrivedAt: json['visited_at'] != null
            ? DateTime.parse(json['visited_at'] as String)
            : null,
      );
    } catch (e) {
      print('Error parsing appointment: $e');
      print('JSON data: $json');
      
      // Return a minimal appointment model with default values
      return ANAppointmentModel(
        id: json['id'] as String? ?? '',
        purpose: 'Consultation',
        appointmentDate: DateTime.now(),
        client: ANUserModel(
          id: '',
          firstName: 'Unknown',
          lastName: 'Client',
          fullName: 'Unknown Client',
          email: '',
          phoneNumber: '',
          gender: Gender.other,
          role: 'client',
        ),
        service: ANServiceModel(
          id: '',
          name: 'Unknown Service',
          type: ANServiceTypesModel.empty(),
          location: ServiceLocation.empty(),
          homeVisit: false,
          owners: [],
          services: [],
          gallery: [],
        ),
        status: HistoryStatus.pending,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'purpose': purpose,
      'appointment_date': appointmentDate.toIso8601String(),
      'client': client.toJson(),
      'service': service.toJson(),
      'status': status.toJsonString(),
      'token': token,
      'visited_at': arrivedAt?.toIso8601String(),
    };
  }

  ANAppointmentModel copyWith({
    String? id,
    String? purpose,
    DateTime? appointmentDate,
    ANUserModel? client,
    ANServiceModel? service,
    HistoryStatus? status,
    String? token,
    DateTime? arrivedAt,
  }) {
    return ANAppointmentModel(
      id: id ?? this.id,
      purpose: purpose ?? this.purpose,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      client: client ?? this.client,
      service: service ?? this.service,
      status: status ?? this.status,
      token: token ?? this.token,
      arrivedAt: arrivedAt ?? this.arrivedAt,
    );
  }
}
