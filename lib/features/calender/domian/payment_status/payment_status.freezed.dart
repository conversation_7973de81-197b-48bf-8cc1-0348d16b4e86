// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'payment_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANPaymentStatusModel _$ANPaymentStatusModelFromJson(Map<String, dynamic> json) {
  return _ANPaymentStatusModel.fromJson(json);
}

/// @nodoc
mixin _$ANPaymentStatusModel {
  @JsonKey(name: 'payment_status')
  HistoryStatus get paymentStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'paymob_refid')
  String? get paymobRefId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANPaymentStatusModelCopyWith<ANPaymentStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANPaymentStatusModelCopyWith<$Res> {
  factory $ANPaymentStatusModelCopyWith(ANPaymentStatusModel value,
          $Res Function(ANPaymentStatusModel) then) =
      _$ANPaymentStatusModelCopyWithImpl<$Res, ANPaymentStatusModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid') String? paymobRefId});
}

/// @nodoc
class _$ANPaymentStatusModelCopyWithImpl<$Res,
        $Val extends ANPaymentStatusModel>
    implements $ANPaymentStatusModelCopyWith<$Res> {
  _$ANPaymentStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentStatus = null,
    Object? paymobRefId = freezed,
  }) {
    return _then(_value.copyWith(
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatus,
      paymobRefId: freezed == paymobRefId
          ? _value.paymobRefId
          : paymobRefId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ANPaymentStatusModelCopyWith<$Res>
    implements $ANPaymentStatusModelCopyWith<$Res> {
  factory _$$_ANPaymentStatusModelCopyWith(_$_ANPaymentStatusModel value,
          $Res Function(_$_ANPaymentStatusModel) then) =
      __$$_ANPaymentStatusModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'payment_status') HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid') String? paymobRefId});
}

/// @nodoc
class __$$_ANPaymentStatusModelCopyWithImpl<$Res>
    extends _$ANPaymentStatusModelCopyWithImpl<$Res, _$_ANPaymentStatusModel>
    implements _$$_ANPaymentStatusModelCopyWith<$Res> {
  __$$_ANPaymentStatusModelCopyWithImpl(_$_ANPaymentStatusModel _value,
      $Res Function(_$_ANPaymentStatusModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentStatus = null,
    Object? paymobRefId = freezed,
  }) {
    return _then(_$_ANPaymentStatusModel(
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as HistoryStatus,
      paymobRefId: freezed == paymobRefId
          ? _value.paymobRefId
          : paymobRefId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANPaymentStatusModel implements _ANPaymentStatusModel {
  const _$_ANPaymentStatusModel(
      {@JsonKey(name: 'payment_status') required this.paymentStatus,
      @JsonKey(name: 'paymob_refid') this.paymobRefId});

  factory _$_ANPaymentStatusModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANPaymentStatusModelFromJson(json);

  @override
  @JsonKey(name: 'payment_status')
  final HistoryStatus paymentStatus;
  @override
  @JsonKey(name: 'paymob_refid')
  final String? paymobRefId;

  @override
  String toString() {
    return 'ANPaymentStatusModel(paymentStatus: $paymentStatus, paymobRefId: $paymobRefId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANPaymentStatusModel &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.paymobRefId, paymobRefId) ||
                other.paymobRefId == paymobRefId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, paymentStatus, paymobRefId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANPaymentStatusModelCopyWith<_$_ANPaymentStatusModel> get copyWith =>
      __$$_ANPaymentStatusModelCopyWithImpl<_$_ANPaymentStatusModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANPaymentStatusModelToJson(
      this,
    );
  }
}

abstract class _ANPaymentStatusModel implements ANPaymentStatusModel {
  const factory _ANPaymentStatusModel(
      {@JsonKey(name: 'payment_status')
          required final HistoryStatus paymentStatus,
      @JsonKey(name: 'paymob_refid')
          final String? paymobRefId}) = _$_ANPaymentStatusModel;

  factory _ANPaymentStatusModel.fromJson(Map<String, dynamic> json) =
      _$_ANPaymentStatusModel.fromJson;

  @override
  @JsonKey(name: 'payment_status')
  HistoryStatus get paymentStatus;
  @override
  @JsonKey(name: 'paymob_refid')
  String? get paymobRefId;
  @override
  @JsonKey(ignore: true)
  _$$_ANPaymentStatusModelCopyWith<_$_ANPaymentStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}
