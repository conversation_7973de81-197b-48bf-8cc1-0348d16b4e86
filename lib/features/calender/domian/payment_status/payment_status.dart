import 'package:ajmal_now_doctor/features/calender/data/calender_repository.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_status.freezed.dart';
part 'payment_status.g.dart';

@freezed
class ANPaymentStatusModel with _$ANPaymentStatusModel {
  const factory ANPaymentStatusModel({
    @JsonKey(name: 'payment_status') required HistoryStatus paymentStatus,
    @JsonKey(name: 'paymob_refid') String? paymobRefId,
  }) = _ANPaymentStatusModel;

  factory ANPaymentStatusModel.fromJson(Map<String, Object?> json) =>
      _$ANPaymentStatusModelFromJson(json);
}
