// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ANPaymentStatusModel _$$_ANPaymentStatusModelFromJson(
        Map<String, dynamic> json) =>
    _$_ANPaymentStatusModel(
      paymentStatus:
          $enumDecode(_$HistoryStatusEnumMap, json['payment_status']),
      paymobRefId: json['paymob_refid'] as String?,
    );

Map<String, dynamic> _$$_ANPaymentStatusModelToJson(
        _$_ANPaymentStatusModel instance) =>
    <String, dynamic>{
      'payment_status': _$HistoryStatusEnumMap[instance.paymentStatus]!,
      'paymob_refid': instance.paymobRefId,
    };

const _$HistoryStatusEnumMap = {
  HistoryStatus.paid: 'paid',
  HistoryStatus.complete: 'complete',
  HistoryStatus.pending: 'pending',
  HistoryStatus.blocked: 'blocked',
};
