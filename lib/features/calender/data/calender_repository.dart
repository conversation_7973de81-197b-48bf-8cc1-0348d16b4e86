import 'package:ajmal_now_doctor/features/calender/data/http_calender_repository.dart';
import 'package:ajmal_now_doctor/features/calender/domian/appointment/appointment.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum HistoryStatus { paid, complete, pending, blocked }

extension HistoryStatusExtension on HistoryStatus {
  static HistoryStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return HistoryStatus.paid;
      case 'complete':
        return HistoryStatus.complete;
      case 'pending':
        return HistoryStatus.pending;
      case 'blocked':
        return HistoryStatus.blocked;
      default:
        return HistoryStatus.pending;
    }
  }
  
  String toJsonString() {
    switch (this) {
      case HistoryStatus.paid:
        return 'paid';
      case HistoryStatus.complete:
        return 'complete';
      case HistoryStatus.pending:
        return 'pending';
      case HistoryStatus.blocked:
        return 'blocked';
    }
  }
}

extension HistoryStatusOperations on HistoryStatus {
  String get name {
    switch (this) {
      case HistoryStatus.paid:
        return 'Upcoming'.hardcoded;
      case HistoryStatus.complete:
        return 'Complete'.hardcoded;
      case HistoryStatus.pending:
        return 'Cancelled'.hardcoded;
      case HistoryStatus.blocked:
        return 'Blocked'.hardcoded;
    }
  }

  Color get cardColor {
    switch (this) {
      case HistoryStatus.paid:
        return Colors.white;
      case HistoryStatus.complete:
        return Colors.white;
      case HistoryStatus.pending:
        return const Color(0xFFE5E5E5);
      case HistoryStatus.blocked:
        return const Color(0xFFE5E5E5);
    }
  }

  Color get textColor {
    switch (this) {
      case HistoryStatus.paid:
        return Colors.black;
      case HistoryStatus.complete:
        return Colors.black;
      case HistoryStatus.pending:
        return const Color(0xFFA3A3A3);
      case HistoryStatus.blocked:
        return const Color(0xFFA3A3A3);
    }
  }
}

class ANCalenderRepository {
  final Ref ref;

  ANCalenderRepository({required this.ref});

  Future<List<ANAppointmentModel>> getAppointmentsByDay(
      {required DateTime date}) async {
    try {
      return await ref
          .read(httpCalenderProvider)
          .getAppointmentsByDay(date: date);
    } catch (e) {
      rethrow;
    }
  }
  
  Future<List<ANAppointmentModel>> getAppointmentsByBranch({
    required String branchId,
  }) async {
    try {
      return await ref
          .read(httpCalenderProvider)
          .getAppointmentsByBranch(branchId: branchId);
    } catch (e) {
      rethrow;
    }
  }
  
  // Get upcoming appointments (today and future)
  Future<List<ANAppointmentModel>> getUpcomingAppointments({
    required String branchId,
  }) async {
    final now = DateTime.now();
    final appointments = await getAppointmentsByBranch(branchId: branchId);
    
    return appointments.where((appointment) => 
      appointment.appointmentDate.isAfter(now) || 
      appointment.appointmentDate.day == now.day && 
      appointment.appointmentDate.month == now.month && 
      appointment.appointmentDate.year == now.year
    ).toList();
  }
  
  // Get past appointments (before today)
  Future<List<ANAppointmentModel>> getPastAppointments({
    required String branchId,
  }) async {
    final now = DateTime.now();
    final startOfToday = DateTime(now.year, now.month, now.day);
    final appointments = await getAppointmentsByBranch(branchId: branchId);
    
    return appointments.where((appointment) => 
      appointment.appointmentDate.isBefore(startOfToday)
    ).toList();
  }
}

final calenderProvider =
    Provider.autoDispose((ref) => ANCalenderRepository(ref: ref));

final calenderDataProvider = FutureProvider.autoDispose.family(
    (ref, DateTime date) =>
        ref.read(calenderProvider).getAppointmentsByDay(date: date));

// New providers for the tabbed interface
final upcomingAppointmentsProvider = FutureProvider.autoDispose.family(
    (ref, String branchId) =>
        ref.read(calenderProvider).getUpcomingAppointments(branchId: branchId));

final pastAppointmentsProvider = FutureProvider.autoDispose.family(
    (ref, String branchId) =>
        ref.read(calenderProvider).getPastAppointments(branchId: branchId));
