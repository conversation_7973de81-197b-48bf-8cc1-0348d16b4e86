import 'package:ajmal_now_doctor/features/calender/domian/appointment/appointment.dart';
import 'package:ajmal_now_doctor/features/statistics/data/service/service_repository.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANHttpCalenderRepository {
  final Ref ref;

  ANHttpCalenderRepository({required this.ref});

  Future<List<ANAppointmentModel>> getAppointmentsByDay(
      {required DateTime date}) async {
    try {
      final startDate = DateTime(date.year, date.month, date.day);
      final endDate = startDate.add(const Duration(days: 1));

      final ANResponseModel response = await ref.read(networkProvider).getData(
        path: 'appointments',
        queryParam: {
          'limit': '100',
          'where[and][0][service][equals]':
              ref.read(servicesProvider).selectedService!.id,
          'where[and][1][appointment_date][greater_than_equal]':
              startDate.toIso8601String(),
          'where[and][2][appointment_date][less_than]':
              endDate.toIso8601String(),
          'where[and][3][status][not_equals]': 'cancelled'
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final List<dynamic> docs = (response.data!['docs'] ?? []) as List;
        print("Appointments data: ${response.data}"); // Debug print
        
        final List<ANAppointmentModel> appointments = [];
        for (final doc in docs) {
          try {
            appointments.add(ANAppointmentModel.fromJson(doc));
          } catch (e) {
            print('Error parsing appointment: $e');
          }
        }
        return appointments;
      }
      return [];
    } catch (e, stack) {
      print('Error fetching appointments: $e\n$stack');
      return []; // Return empty list instead of throwing
    }
  }

  Future<List<ANAppointmentModel>> getAppointmentsByBranch({
    required String branchId,
  }) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
        path: 'appointments',
        headers: {
          'Content-Type': 'application/json',
        },
        queryParam: {
          'limit': '100',
          'where[and][0][service][equals]': branchId,
          'where[and][1][status][not_equals]': 'cancelled',
          'sort': '-appointment_date', // Sort by appointment date descending
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final List<dynamic> docs = (response.data!['docs'] ?? []) as List;
        
        final List<ANAppointmentModel> appointments = [];
        for (final doc in docs) {
          try {
            appointments.add(ANAppointmentModel.fromJson(doc));
          } catch (e) {
            print('Error parsing appointment in getAppointmentsByBranch: $e');
            // Continue to next appointment
          }
        }
        return appointments;
      }
      return [];
    } catch (e, stack) {
      print('Error fetching appointments by branch: $e\n$stack');
      return []; // Return empty list instead of throwing
    }
  }
}

final httpCalenderProvider = Provider<ANHttpCalenderRepository>(
    (ref) => ANHttpCalenderRepository(ref: ref));
