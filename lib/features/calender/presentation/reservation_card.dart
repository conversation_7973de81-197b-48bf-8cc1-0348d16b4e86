import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/appointments/data/appointments/appointments_repository.dart';
import 'package:ajmal_now_doctor/features/calender/data/calender_repository.dart';
import 'package:ajmal_now_doctor/features/calender/domian/appointment/appointment.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class ANAppointmentCard extends ConsumerWidget {
  final ANAppointmentModel appointment;
  final void Function() callback;

  const ANAppointmentCard(
      {Key? key, required this.appointment, required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () => context.pushNamed(AppRoute.appointmentDetailsScreen.name,
          pathParameters: {'id': appointment.id!}),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 17.0),
        decoration: BoxDecoration(
          color: appointment.status.cardColor,
          borderRadius: circularBorderXXS,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (appointment.status != HistoryStatus.blocked)
              SizedBox(
                width: 70.0,
                child: CircleAvatar(
                  radius: 35.0,
                  backgroundColor: AppColors.primaryColor,
                  child: appointment.client.profilePicture != null
                      ? CircleAvatar(
                          radius: 33.0,
                          backgroundImage: NetworkImage(
                              appointment.client.profilePicture!.url),
                        )
                      : const CircleAvatar(
                          radius: 33.0,
                          backgroundColor: AppColors.backgroundColor,
                          child: Icon(
                            Icons.person,
                            color: AppColors.primaryColor,
                            size: 40.0,
                          ),
                        ),
                ),
              ),
            gapWL,
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${appointment.client.firstName} ${appointment.client.lastName}',
                    style: GoogleFonts.philosopher(
                        textStyle: TextSize.r.boldStyle
                            .copyWith(color: appointment.status.textColor)),
                  ),
                  gapHS,
                  Text(
                    appointment.purpose,
                    style: TextSize.xs.regularStyle
                        .copyWith(color: appointment.status.textColor),
                  ),
                  gapHS,
                  Row(
                    children: [
                      Icon(Icons.calendar_today, 
                          size: 14, 
                          color: appointment.status.textColor.withOpacity(0.7)),
                      gapWXS,
                      Text(
                        DateFormat.yMMMd().format(appointment.appointmentDate.toLocal()),
                        style: TextSize.xs.regularStyle
                            .copyWith(color: appointment.status.textColor.withOpacity(0.7)),
                      ),
                      gapWL,
                      Icon(Icons.access_time, 
                          size: 14, 
                          color: appointment.status.textColor.withOpacity(0.7)),
                      gapWXS,
                      Text(
                        DateFormat.jm().format(appointment.appointmentDate.toLocal()),
                        style: TextSize.xs.regularStyle
                            .copyWith(color: appointment.status.textColor.withOpacity(0.7)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Column(
              children: [
                if (appointment.status == HistoryStatus.blocked)
                  Column(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                            shape: BoxShape.circle, color: Colors.green),
                        child: IconButton(
                          onPressed: () async {
                            try {
                              final bool result = await ref
                                  .read(appointmentsProvider)
                                  .deleteAppointment(
                                      appointmentId: appointment.id!);
                              if (result) {
                                ANDialogHelper.gShowSuccessDialog(
                                        context: context,
                                        title: S
                                            .of(context)
                                            .kTimeSlotIsNowAvailable,
                                        primaryButtonLabel:
                                            S.of(context).kConfirm)
                                    .then((value) {
                                  callback.call();
                                });
                              }
                            } catch (e) {
                              ANDialogHelper.gShowCustomDialog(
                                  context: context,
                                  title: S.of(context).kSomethingWentWrong,
                                  body: Text(e.toString()),
                                  primaryButtonLabel: S.of(context).kConfirm,
                                  primaryButtonCallBack: () {
                                    context.pop();
                                  });
                            }
                          },
                          icon: const Icon(Icons.done),
                          color: Colors.white,
                        ),
                      ),
                      gapHR,
                    ],
                  ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: appointment.status == HistoryStatus.paid 
                        ? AppColors.primaryColor.withOpacity(0.1) 
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    appointment.status.name,
                    style: TextSize.xs.regularStyle.copyWith(
                      color: appointment.status == HistoryStatus.paid 
                          ? AppColors.primaryColor 
                          : const Color(0xFF838BA1),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
