import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/appointments/data/appointments/appointments_repository.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class ANTimeSlotCard extends ConsumerWidget {
  final ANSlotsModel timeSlot;
  final String serviceProviderId;
  final String serviceId;
  final void Function() callback;

  const ANTimeSlotCard(
      {Key? key,
      required this.timeSlot,
      required this.serviceProviderId,
      required this.serviceId,
      required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 17.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: circularBorderXXS,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  DateFormat().add_yMMMMd().format(timeSlot.starts.toLocal()),
                  style:
                      GoogleFonts.philosopher(textStyle: TextSize.r.boldStyle),
                ),
                gapHR,
                Text(
                  DateFormat().add_jm().format(timeSlot.starts.toLocal()),
                  style: TextSize.xs.regularStyle,
                ),
              ],
            ),
          ),
          Container(
            decoration:
                const BoxDecoration(shape: BoxShape.circle, color: Colors.red),
            child: IconButton(
              onPressed: () async {
                try {
                  final appointment = await ref
                      .read(appointmentsProvider)
                      .createAppointment(
                          user: ref.read(authRepositoryProvider).currentUser!,
                          serviceProviderId: serviceProviderId,
                          serviceId: serviceId,
                          timeSlot: timeSlot,
                          purpose: 'Blocked by service provider');
                  ANDialogHelper.gShowSuccessDialog(
                          context: context,
                          title: S.of(context).kTimeSlotBlockedSuccessfully,
                          primaryButtonLabel: S.of(context).kConfirm)
                      .then((value) {
                    callback.call();
                  });
                } catch (e) {
                  ANDialogHelper.gShowCustomDialog(
                      context: context,
                      title: S.of(context).kSomethingWentWrong,
                      body: Text(e.toString()),
                      primaryButtonLabel: S.of(context).kConfirm,
                      primaryButtonCallBack: () {
                        context.pop();
                      });
                }
              },
              icon: const Icon(Icons.remove),
              color: Colors.white,
            ),
          )
        ],
      ),
    );
  }
}
