import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';

class ANTableCalender extends StatefulWidget {
  final List<ANScheduleModel>? schedule;
  final Function(DateTime) selectDateCallback;

  const ANTableCalender(
      {Key? key, required this.selectDateCallback, required this.schedule})
      : super(key: key);

  @override
  State<ANTableCalender> createState() => _ANTableCalenderState();
}

class _ANTableCalenderState extends State<ANTableCalender> {
  CalendarFormat _calendarFormat = CalendarFormat.month;

  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 32.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: circularBorderXS,
      ),
      child: TableCalendar(
        focusedDay: _focusedDay,
        firstDay: DateTime.now().subtract(const Duration(days: 120)),
        lastDay: DateTime.now().add(const Duration(days: 30)),
        calendarFormat: _calendarFormat,
        calendarStyle: const CalendarStyle(
            selectedDecoration: BoxDecoration(
                color: AppColors.primaryColor, shape: BoxShape.circle)),
        enabledDayPredicate: (day) {
          if (widget.schedule == null) return true;
          return (DateTime.now().isBefore(day) ||
                  DateTime.now().difference(day) < const Duration(days: 1)) &&
              widget.schedule!
                  .any((element) => element.day.weekday() == day.weekday);
        },
        // enabledDayPredicate: (day) {
        //   return DateTime.now().isBefore(day) ||
        //       DateTime.now().difference(day) < const Duration(days: 1);
        // },
        selectedDayPredicate: (day) {
          // Use `selectedDayPredicate` to determine which day is currently selected.
          // If this returns true, then `day` will be marked as selected.

          // Using `isSameDay` is recommended to disregard
          // the time-part of compared DateTime objects.
          return isSameDay(_selectedDay, day);
        },
        onDaySelected: (selectedDay, focusedDay) {
          if (!isSameDay(_selectedDay, selectedDay)) {
            // Call `setState()` when updating the selected day
            setState(() {
              _selectedDay = DateTime(
                  selectedDay.year, selectedDay.month, selectedDay.day);
              _focusedDay = focusedDay;
            });
            widget.selectDateCallback.call(selectedDay);
          }
        },
        onFormatChanged: (format) {
          setState(() {
            _calendarFormat = format;
          });
        },
      ),
    );
  }
}
