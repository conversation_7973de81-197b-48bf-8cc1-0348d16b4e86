import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/constants/images.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANLandingScreen extends ConsumerStatefulWidget {
  final bool isSessionExpired;

  const ANLandingScreen({Key? key, this.isSessionExpired = false})
      : super(key: key);

  @override
  ConsumerState createState() => _ANLandingScreenState();
}

class _ANLandingScreenState extends ConsumerState<ANLandingScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // if (widget.isSessionExpired) {
      //   ANDialogHelper.gShowConfirmationDialog(
      //       context: context,
      //       message: S.of(context).kYourSessionHasExpiredPleaseLoginAgain,
      //       type: DialogType.confirm);
      // }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final languageController = ref.watch(languageProvider);

    return ANScaffold(
      // image: Images.welcomeScreen,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: true,
        disableDefaultPadding: true,
        disableDrawer: true,
      ),
      body: Padding(
        padding: screenPadding,
        child: Column(
          // mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 50.0),
            Image.asset(
              Images.logoImage,
              height: 200.0,
            ),
            gapHXXL,
            Text(
              'Welcome to AjmalNow'.hardcoded,
              style: GoogleFonts.philosopher(
                  fontSize: 40.0, color: AppColors.primaryColor),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 100.0),
            ANElevatedButton(
              onPressed: () => context.pushNamed(AppRoute.loginScreen.name),
              child: Text(
                S.of(context).kLogin,
                style: TextSize.r.semiBoldStyle.copyWith(color: Colors.white),
              ),
            ),
            gapHXXL,
            // ANElevatedButton(
            //     // TODO: implement the Register and onPressed func
            //     onPressed: () {},
            //     child: Text(S.of(context).kRegister)),
            const SizedBox(height: 100.0),
            if (languageController.lang!.languageCode == 'en')
              Center(
                child: GestureDetector(
                  onTap: () async =>
                      await languageController.switchLang(langCode: 'ar'),
                  child: Text(
                    'للغة العربية'.hardcoded,
                    style: TextSize.r.regularStyle
                        .copyWith(color: AppColors.primaryColor),
                  ),
                ),
              )
            else
              Center(
                child: GestureDetector(
                  onTap: () async =>
                      await languageController.switchLang(langCode: 'en'),
                  child: Text(
                    'For English language'.hardcoded,
                    style: TextSize.r.regularStyle
                        .copyWith(color: AppColors.primaryColor),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
