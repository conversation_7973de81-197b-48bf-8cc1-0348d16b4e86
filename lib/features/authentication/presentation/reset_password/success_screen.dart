import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/constants/images.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANSuccessScreen extends StatelessWidget {
  const ANSuccessScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDrawer: true,
        disableAppbar: true,
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 100.0,
            width: 100.0,
            child: Image.asset(Images.successSticker),
          ),
          gapHXXXL,
          gapHXXS,
          Text(
            S.of(context).kPasswordChanged,
            style: GoogleFonts.philosopher(textStyle: TextSize.xl.boldStyle),
          ),
          gapHR,
          Text(
            S.of(context).kYourPasswordHasBeenChangedSuccessfully,
            style:
                TextSize.r.mediumStyle.copyWith(color: const Color(0xFF8391A1)),
          ),
          gapHXXXL,
          gapHR,
          Center(
            child: ANElevatedButton(
              onPressed: () {
                context.pop();
                context.pop();
                context.pop();
              },
              child: Text(
                S.of(context).kBackToLogin,
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
