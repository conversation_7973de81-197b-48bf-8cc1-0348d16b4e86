import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/text_fields/password_text_field.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANResetPasswordScreen extends StatelessWidget {
  ANResetPasswordScreen(
      {Key? key, required this.resetPassword, required this.token})
      : super(key: key);

  final String token;
  final Function(String, String) resetPassword;
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDrawer: true,
        disableAppbar: true,
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const ANBackButton(),
              gapHXXXL,
              Text(
                S.of(context).kCreateNewPassword,
                style:
                    GoogleFonts.philosopher(textStyle: TextSize.xxl.boldStyle),
              ),
              gapHR,
              Text(
                S
                    .of(context)
                    .kYourNewPasswordMustBeUniqueFromThosePreviouslyUsed,
                style: TextSize.r.mediumStyle
                    .copyWith(color: const Color(0xFF8391A1)),
              ),
              gapHXXXL,
              ANPasswordTextField(
                controller: _newPasswordController,
                hint: S.of(context).kNewPassword,
                validator: (value) {
                  return ANValidationsHelper.getValidators(validation: {
                    'is_required': true,
                  }, value: value);
                },
              ),
              gapHL,
              ANPasswordTextField(
                controller: _confirmPasswordController,
                hint: S.of(context).kConfirmPassword,
                validator: (value) {
                  return ANValidationsHelper.getValidators(
                    validation: {
                      'is_required': true,
                      'validation_type': 'confirmPassword',
                    },
                    value: value,
                    confirmPassword: _newPasswordController.text,
                  );
                },
              ),
              gapHXXXL,
              gapHXXL,
              Center(
                child: ANLoadingElevatedButton(
                  onPressed: () async {
                    if (!_formKey.currentState!.validate()) {
                      return; // Return early if validation fails
                    }

                    try {
                      bool result = await resetPassword(
                          token, _newPasswordController.text);

                      if (result) {
                        // ignore: use_build_context_synchronously
                        context.pushNamed(AppRoute.successScreen.name);
                      }
                    } catch (error) {
                      // ignore: use_build_context_synchronously
                      ANDialogHelper.gShowConfirmationDialog(
                        context: context,
                        message: error.toString(),
                        type: DialogType.confirm,
                      );
                    }
                  },
                  label: Text(
                    S.of(context).kResetPassword,
                    style: TextSize.l.mediumStyle.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
