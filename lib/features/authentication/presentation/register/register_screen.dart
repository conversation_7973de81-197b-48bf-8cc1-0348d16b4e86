// import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
// import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
// import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
// import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
// import 'package:ajmal_now_doctor/common/buttons/social_media_button.dart';
// import 'package:ajmal_now_doctor/common/text_fields/password_text_field.dart';
// import 'package:ajmal_now_doctor/common/text_fields/text_field.dart';
// import 'package:ajmal_now_doctor/constants/colors.dart';
// import 'package:ajmal_now_doctor/constants/dimensions.dart';
// import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
// import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
// import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
// import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
// import 'package:ajmal_now_doctor/routing/app_router.dart';
// import 'package:ajmal_now_doctor/utils/string_extensions.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_vector_icons/flutter_vector_icons.dart';
// import 'package:go_router/go_router.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:intl/intl.dart';
//
// class ANRegisterScreen extends ConsumerStatefulWidget {
//   const ANRegisterScreen({Key? key}) : super(key: key);
//
//   @override
//   ConsumerState createState() => _ANRegisterScreenState();
// }
//
// class _ANRegisterScreenState extends ConsumerState<ANRegisterScreen> {
//   final _formKey = GlobalKey<FormState>();
//
//   final TextEditingController _firstNameController = TextEditingController(),
//       _middleNameController = TextEditingController(),
//       _lasNameController = TextEditingController(),
//       _phoneNumberController = TextEditingController(),
//       _emailController = TextEditingController(),
//       _birthDateController = TextEditingController();
//
//   Gender? _selectedGender;
//   DateTime _birthDate = DateTime.now().subtract(const Duration(days: 12 * 365));
//
//   @override
//   Widget build(BuildContext context) {
//     return ANScaffold(
//       scaffoldOptions: const ANScaffoldOptions(
//         disableDrawer: true,
//         disableAppbar: true,
//       ),
//       body: Column(
//         children: [
//           Expanded(
//             child: SingleChildScrollView(
//               child: Form(
//                 key: _formKey,
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     const ANBackButton(),
//                     gapHXXXL,
//                     Text(
//                       'Welcome to AjmalNow! Register to get started.'.hardcoded,
//                       style: GoogleFonts.philosopher(
//                           textStyle: TextSize.xxl.boldStyle),
//                     ),
//                     gapHXXXL,
//                     ANTextFormField(
//                       controller: _firstNameController,
//                       hint: 'First name *'.hardcoded,
//                       validator: (value) {
//                         return ANValidationsHelper.validateNotEmpty(value!);
//                       },
//                     ),
//                     gapHL,
//                     Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         Expanded(
//                           child: ANTextFormField(
//                             controller: _middleNameController,
//                             hint: 'Middle name'.hardcoded,
//                           ),
//                         ),
//                         gapWL,
//                         Expanded(
//                           child: ANTextFormField(
//                             controller: _lasNameController,
//                             hint: 'Last name *'.hardcoded,
//                             validator: (value) {
//                               return ANValidationsHelper.validateNotEmpty(
//                                   value!);
//                             },
//                           ),
//                         ),
//                       ],
//                     ),
//                     gapHL,
//                     ANTextFormField(
//                       controller: _phoneNumberController,
//                       hint: 'Phone number *'.hardcoded,
//                       validator: (value) {
//                         return ANValidationsHelper.getValidators(validation: {
//                           'is_required': true,
//                           'validation_type': 'phone',
//                         }, value: value);
//                       },
//                     ),
//                     gapHL,
//                     ANGenderSelector(callback: (gender) => setState(() {
//                               _selectedGender = gender;
//                             })),
//                     gapHL,
//                     ANTextFormField(
//                       controller: _birthDateController,
//                       hint: 'Birth date'.hardcoded,
//                       icon: Icons.calendar_month_outlined,
//                       readOnly: true,
//                       iconAction: () {
//                         showModalBottomSheet(
//                           context: context,
//                           isDismissible: false,
//                           shape: const RoundedRectangleBorder(
//                               borderRadius: BorderRadius.vertical(
//                                   top: Radius.circular(30.0))),
//                           backgroundColor: Colors.white,
//                           builder: (BuildContext context) {
//                             return Padding(
//                               padding: const EdgeInsets.all(10.0),
//                               child: Column(
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   Container(
//                                     width: 100.0,
//                                     height: 10.0,
//                                     decoration: BoxDecoration(
//                                         color: AppColors.primaryColor,
//                                         borderRadius: circularBorderL),
//                                   ),
//                                   gapHXL,
//                                   SizedBox(
//                                     height: 200.0,
//                                     child: CupertinoDatePicker(
//                                       initialDateTime: _birthDate,
//                                       maximumDate: DateTime.now().subtract(
//                                           const Duration(days: 12 * 365)),
//                                       mode: CupertinoDatePickerMode.date,
//                                       use24hFormat: true,
//                                       // This is called when the user changes the date.
//                                       onDateTimeChanged: (DateTime newDate) {
//                                         setState(() {
//                                           _birthDate = newDate;
//                                         });
//                                       },
//                                     ),
//                                   ),
//                                   gapHL,
//                                   ANElevatedButton(
//                                     onPressed: () {
//                                       if (_birthDate
//                                               .difference(DateTime.now()
//                                                   .subtract(const Duration(
//                                                       days: 12 * 365)))
//                                               .inSeconds <
//                                           0) {
//                                         setState(() {
//                                           _birthDateController.text =
//                                               DateFormat()
//                                                   .add_yMMMMd()
//                                                   .format(_birthDate);
//                                         });
//                                         Navigator.pop(context);
//                                       } else {
//                                         ANDialogHelper.gShowConfirmationDialog(
//                                           context: context,
//                                           message:
//                                               'Please select a valid birth date',
//                                           type: DialogType.confirm,
//                                         );
//                                       }
//                                     },
//                                     child: Text('Done'),
//                                   ),
//                                 ],
//                               ),
//                             );
//                           },
//                         );
//                       },
//                     ),
//                     gapHL,
//                     ANTextFormField(
//                       controller: _emailController,
//                       hint: 'Email *'.hardcoded,
//                       validator: (value) {
//                         return ANValidationsHelper.getValidators(validation: {
//                           'is_required': true,
//                           'validation_type': 'email',
//                         }, value: value);
//                       },
//                     ),
//                     // gapHL,
//                     // ANPasswordTextField(
//                     //   hint: 'Password'.hardcoded,
//                     // ),
//                     // gapHL,
//                     // ANPasswordTextField(
//                     //   hint: 'Confirm password'.hardcoded,
//                     // ),
//                     gapHXXXL,
//                     Center(
//                       child: ANLoadingElevatedButton(
//                         onPressed: () async {
//                           if (_formKey.currentState!.validate()) {
//                             try {
//                               await ref
//                                   .read(authRepositoryProvider)
//                                   .register(
//                                     context,
//                                     firstName: _firstNameController.text,
//                                     middleName: _middleNameController.text,
//                                     lastName: _lasNameController.text,
//                                     phoneNumber: _phoneNumberController.text,
//                                     gender: _selectedGender,
//                                     email: _emailController.text,
//                                     birthDate: _birthDate,
//                                   )
//                                   .then((value) {
//                                 if (value) {
//                                   context.pop();
//                                   context
//                                       .pushReplacementNamed(AppRoute.homeScreen.name);
//                                 }
//                               });
//                             } catch (e) {
//                               ANDialogHelper.gShowConfirmationDialog(
//                                   context: context,
//                                   message: e.toString(),
//                                   type: DialogType.confirm);
//                             }
//                           }
//                         },
//                         label: Text('Register'.hardcoded),
//                       ),
//                     ),
//                     gapHXXXL,
//                     Row(
//                       children: [
//                         const Expanded(
//                           child: Divider(
//                             endIndent: 15.0,
//                             thickness: 1.0,
//                             color: Color(0xFFE8ECF4),
//                           ),
//                         ),
//                         Text(
//                           'Or Register with',
//                           style: TextSize.s.mediumStyle
//                               .copyWith(color: Color(0xFF6A707C)),
//                         ),
//                         const Expanded(
//                           child: Divider(
//                             indent: 15.0,
//                             thickness: 1.0,
//                             color: Color(0xFFE8ECF4),
//                           ),
//                         ),
//                       ],
//                     ),
//                     gapHXXL,
//                     Row(
//                       children: [
//                         Expanded(child: ANSocialMediaButton(onPressed: () async {
//                           try{
//                             await ref.read(authRepositoryProvider).facebookSingIn(context).then((value){
//                               if(value){
//                                 context.pop();
//                                 context.pushReplacementNamed(AppRoute.homeScreen.name);
//                               }
//                             });
//                           }catch(e){
//                             ANDialogHelper.gShowConfirmationDialog(context: context, message: e.toString(), type: DialogType.confirm);
//                           }
//                         }, child: const Icon(FontAwesome.facebook))),
//                         gapWS,
//                         Expanded(child: ANSocialMediaButton(onPressed: () async {
//                           try{
//                             await ref.read(authRepositoryProvider).googleSignIn(context).then((value){
//                               if(value){
//                                 context.pop();
//                                 context.pushReplacementNamed(AppRoute.homeScreen.name);
//                               }
//                             });
//                           }catch(e){
//                             ANDialogHelper.gShowConfirmationDialog(context: context, message: e.toString(), type: DialogType.confirm);
//                           }
//                         }, child: const Icon(FontAwesome.google, color: Colors.green,))),
//                         gapWS,
//                         Expanded(child: ANSocialMediaButton(onPressed: () async {
//                           try{
//                             await ref.read(authRepositoryProvider).appleSignIn(context).then((value){
//                               if(value){
//                                 context.pop();
//                                 context.pushReplacementNamed(AppRoute.homeScreen.name);
//                               }
//                             });
//                           }catch(e){
//                             ANDialogHelper.gShowConfirmationDialog(context: context, message: e.toString(), type: DialogType.confirm);
//                           }
//                         }, child: const Icon(Icons.apple, color: Colors.black,))),
//                       ],
//                     ),
//                     gapHXXL,
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Text(
//                           "Already have an account? ".hardcoded,
//                           style: TextSize.r.mediumStyle,
//                         ),
//                         GestureDetector(
//                           onTap: () =>
//                               context.pushReplacementNamed(AppRoute.loginScreen.name),
//                           child: Text(
//                             "Login Now".hardcoded,
//                             style: TextSize.r.boldStyle
//                                 .copyWith(color: AppColors.primaryColor),
//                           ),
//                         )
//                       ],
//                     )
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
