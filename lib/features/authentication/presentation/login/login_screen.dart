import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/text_fields/password_text_field.dart';
import 'package:ajmal_now_doctor/common/text_fields/text_field.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANLoginScreen extends ConsumerWidget {
  ANLoginScreen({Key? key}) : super(key: key);

  final _formKey = GlobalKey<FormState>();
  //final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // _emailController.text = "<EMAIL>";
    // _passwordController.text = "test";
    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDrawer: true,
        disableAppbar: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const ANBackButton(),
                    gapHXXXL,
                    Text(
                      S.of(context).kWelcomeBackGladToSeeYouAgain,
                      style: GoogleFonts.philosopher(
                          textStyle: TextSize.xxl.boldStyle),
                    ),
                    gapHXXXL,
                    ANTextFormField(
                      controller: _emailController,
                      hint: S.of(context).kEnterYourEmail,
                      textInputType: TextInputType.emailAddress,
                      validator: (value) {
                        return ANValidationsHelper.getValidators(validation: {
                          'is_required': true,
                          'validation_type': 'email',
                        }, value: value);
                      },
                    ),
                    gapHXXXL,
                    ANPasswordTextField(
                      controller: _passwordController,
                      hint: S.of(context).kEnterYourPassword,
                      validator: (value) {
                        return ANValidationsHelper.getValidators(validation: {
                          'is_required': true,
                        }, value: value);
                      },
                    ),
                    gapHS,
                    Align(
                      alignment: Alignment.centerRight,
                      child: GestureDetector(
                        onTap: () => context
                            .pushNamed(AppRoute.forgetPasswordScreen.name),
                        child: Text(
                          S.of(context).kForgetPassword,
                          style: TextSize.r.boldStyle
                              .copyWith(color: AppColors.primaryColor),
                        ),
                      ),
                    ),
                    gapHXXXL,
                    Center(
                      child: ANLoadingElevatedButton(
                          onPressed: () async {
                            if (_formKey.currentState!.validate()) {
                              try {
                                await ref
                                    .read(authRepositoryProvider)
                                    .login(context,
                                        email: _emailController.text,
                                        password: _passwordController.text)
                                    .then((value) {
                                  if (value) {
                                    context.pop();
                                    context.pushReplacementNamed(
                                        AppRoute.homeScreen.name);
                                  } else {
                                    ref.read(authRepositoryProvider).logout();
                                    ANDialogHelper.gShowConfirmationDialog(
                                        context: context,
                                        message:
                                            'Incorrect email or password. Please try again.',
                                        type: DialogType.confirm);
                                  }
                                });
                              } catch (e) {
                                ANDialogHelper.gShowConfirmationDialog(
                                    context: context,
                                    message: e.toString(),
                                    type: DialogType.confirm);
                              }
                            }
                          },
                          label: Text(
                            S.of(context).kLogin,
                            style: const TextStyle(
                              color: Colors.white,
                            ),
                          )),
                    ),
                    gapHXXXL
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
