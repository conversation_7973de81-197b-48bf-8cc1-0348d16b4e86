import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/text_fields/text_field.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANForgetPasswordScreen extends ConsumerStatefulWidget {
  const ANForgetPasswordScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ANForgetPasswordScreen> createState() =>
      _ANForgetPasswordScreenState();
}

// Create the state class
class _ANForgetPasswordScreenState
    extends ConsumerState<ANForgetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDrawer: true,
        disableAppbar: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const ANBackButton(),
                    gapHXXXL,
                    Text(
                      S.of(context).kForgetPassword,
                      style: GoogleFonts.philosopher(
                          textStyle: TextSize.xxl.boldStyle),
                    ),
                    gapHR,
                    Text(
                      S
                          .of(context)
                          .kNoWorriesPleaseEnterTheEmailAddressLinkedWithYourAccount,
                      style: TextSize.r.mediumStyle
                          .copyWith(color: const Color(0xFF8391A1)),
                    ),
                    gapHXXXL,
                    ANTextFormField(
                      controller: _emailController,
                      hint: S.of(context).kEnterYourEmail,
                      textInputType: TextInputType.emailAddress,
                      validator: (value) {
                        return ANValidationsHelper.getValidators(validation: {
                          'is_required': true,
                          'validation_type': 'email',
                        }, value: value);
                      },
                    ),
                    gapHXXXL,
                    gapHXXL,
                    Center(
                      child: ANLoadingElevatedButton(
                        onPressed: () async {
                          if (!_formKey.currentState!.validate()) {
                            return; // Return early if validation fails
                          }
                          try {
                            String token = await ref
                                .read(authRepositoryProvider)
                                .sendOtp(_emailController.text);
                            if (token.isNotEmpty) {
                              // ignore: use_build_context_synchronously
                              context
                                  .pushNamed(AppRoute.otpScreen.name, extra: {
                                'verifyOtp':
                                    ref.read(authRepositoryProvider).verifyOtp,
                                'resendOtp':
                                    ref.read(authRepositoryProvider).sendOtp,
                                'resetPassword': ref
                                    .read(authRepositoryProvider)
                                    .resetPassword,
                                'email': _emailController.text,
                                'token': token,
                              });
                            } else {}
                          } catch (error) {
                            // ignore: use_build_context_synchronously
                            ANDialogHelper.gShowConfirmationDialog(
                              context: context,
                              message: error.toString(),
                              type: DialogType.confirm,
                            );
                          }
                        },
                        label: Text(
                          S.of(context).kSendCode,
                          style: TextSize.l.mediumStyle
                              .copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   children: [
          //     Text(
          //       S.of(context).kRememberPassword,
          //       style: TextSize.r.mediumStyle,
          //     ),
          //     GestureDetector(
          //       onTap: () =>
          //           context.pushReplacementNamed(AppRoute.registerScreen.name),
          //       child: Text(
          //         S.of(context).kLogin,
          //         style: TextSize.r.boldStyle
          //             .copyWith(color: AppColors.primaryColor),
          //       ),
          //     )
          //   ],
          // )
        ],
      ),
    );
  }
}
