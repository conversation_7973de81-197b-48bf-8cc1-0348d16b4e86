import 'dart:async';

import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class ANValidateOtpScreen extends StatefulWidget {
  final Function(String, String) verifyOtp;
  final Function(String) sendOtp;
  final Function(String, String) resetPassword;
  final String email;
  String token;

  ANValidateOtpScreen({
    Key? key,
    required this.verifyOtp,
    required this.sendOtp,
    required this.resetPassword,
    required this.email,
    required this.token,
  }) : super(key: key);

  @override
  State<ANValidateOtpScreen> createState() => _ANValidateOtpScreenState();
}

class _ANValidateOtpScreenState extends State<ANValidateOtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool isPressed = false;

  // Add timer related variables
  bool canResend = true;
  int timeLeft = 30;
  Timer? _timer;
  bool _isResendLoading = false;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    // _otpController.dispose();
    super.dispose();
  }

  void startTimer() {
    setState(() {
      canResend = false;
      timeLeft = 30;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (timeLeft > 0) {
        setState(() {
          timeLeft--;
        });
      } else {
        setState(() {
          canResend = true;
        });
        timer.cancel();
      }
    });
  }

  // Replace the bottom Row widget with this
  Widget buildResendSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          S.of(context).kDidNotReceiveCode,
          style: TextSize.r.mediumStyle,
        ),
        gapWXXS,
        if (_isResendLoading)
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
            ),
          )
        else
          GestureDetector(
            onTap: canResend
                ? () async {
                    try {
                      setState(() {
                        _isResendLoading = true;
                      });
                      String token = await widget.sendOtp(widget.email);
                      if (token.isNotEmpty) {
                        widget.token = token;
                      }
                      startTimer();
                    } catch (error) {
                      // Handle error if needed
                      // ignore: use_build_context_synchronously
                      ANDialogHelper.gShowConfirmationDialog(
                        context: context,
                        message: error.toString(),
                        type: DialogType.confirm,
                      );
                    } finally {
                      setState(() {
                        _isResendLoading = false;
                      });
                    }
                  }
                : null,
            child: Text(
              canResend
                  ? S.of(context).kResendAgain
                  : '${S.of(context).kResendAgain} (${timeLeft}s)',
              style: TextSize.r.boldStyle.copyWith(
                color: canResend
                    ? AppColors.primaryColor
                    : AppColors.primaryColor.withOpacity(0.5),
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDrawer: true,
        disableAppbar: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const ANBackButton(),
                    gapHXXXL,
                    Text(
                      S.of(context).kOtpVerification,
                      style: GoogleFonts.philosopher(
                          textStyle: TextSize.xxl.boldStyle),
                    ),
                    gapHR,
                    Text(
                      S
                          .of(context)
                          .kEnterTheVerificationCodeWeJustSentOnYourPhone,
                      style: TextSize.r.mediumStyle
                          .copyWith(color: const Color(0xFF8391A1)),
                    ),
                    gapHXXXL,
                    PinCodeTextField(
                      controller: _otpController,
                      appContext: context,
                      length: 6,
                      onChanged: (value) {},
                      keyboardType: TextInputType.number,
                      autoFocus: false,
                      obscureText: false,
                      animationType: AnimationType.fade,
                      cursorColor: AppColors.primaryColor,
                      validator: (v) {
                        if (isPressed) {
                          if (v!.isEmpty) {
                            return S.of(context).kThisFieldCantBeEmpty;
                          }
                          if (v.length < 6) {
                            return S.of(context).kThatsNotTheRightCode;
                          }
                        }
                        return null;
                      },
                      pinTheme: PinTheme(
                        shape: PinCodeFieldShape.box,
                        activeColor: AppColors.primaryColor,
                        selectedColor: AppColors.primaryColor,
                        inactiveColor: AppColors.primaryColor.withOpacity(0.3),
                        borderRadius: otpFieldBorderRadius,
                        fieldHeight: 60,
                        fieldWidth: 50,
                        activeFillColor: Colors.transparent,
                      ),
                      animationDuration: const Duration(milliseconds: 300),
                      backgroundColor: Colors.transparent,
                      enableActiveFill: false,
                      beforeTextPaste: (text) => true,
                    ),
                    gapHXXXL,
                    gapHXXL,
                    Center(
                      child: ANLoadingElevatedButton(
                        onPressed: () async {
                          isPressed = true;
                          if (!_formKey.currentState!.validate()) {
                            return; // Return early if validation fails
                          }
                          try {
                            bool result = await widget.verifyOtp(
                                widget.email, _otpController.text);
                            if (result) {
                              // ignore: use_build_context_synchronously
                              context.pushReplacementNamed(
                                  AppRoute.resetPasswordScreen.name,
                                  extra: {
                                    'resetPassword': widget.resetPassword,
                                    'token': widget.token,
                                  });
                            }
                          } catch (error) {
                            // ignore: use_build_context_synchronously
                            ANDialogHelper.gShowConfirmationDialog(
                              context: context,
                              message: error.toString(),
                              type: DialogType.confirm,
                            );
                          }
                        },
                        label: Text(
                          S.of(context).kVerify,
                          style: TextSize.l.mediumStyle
                              .copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          buildResendSection(), // Replace the existing Row with this method call
        ],
      ),
    );
  }
}
