import 'dart:io';

import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';

enum Gender {
  male,
  female,
  other;

  String get name {
    switch (this) {
      case Gender.male:
        return 'Male'.hardcoded;
      case Gender.female:
        return 'Female'.hardcoded;
      case Gender.other:
        return 'Other'.hardcoded;
    }
  }

  String get to<PERSON><PERSON> {
    switch (this) {
      case Gender.male:
        return 'male'.hardcoded;
      case Gender.female:
        return 'female'.hardcoded;
      case Gender.other:
        return 'other'.hardcoded;
    }
  }
  
  static Gender fromString(String? value) {
    if (value == null) return Gender.other;
    
    switch (value.toLowerCase()) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      default:
        return Gender.other;
    }
  }
}

// Manual implementation instead of freezed
class ANUserModel {
  final String id;
  final String firstName;
  final String? middleName;
  final String lastName;
  final String fullName;
  final String email;
  final String phoneNumber;
  final String? secondaryPhone;
  final Gender gender;
  final DateTime? birthdate;
  final String role;
  final String? token;
  final String? tokenType;
  final String? fcmToken;
  final ANImageModel? profilePicture;
  final String? firebasePhoto;
  final String? notificationPreference;
  final List<String>? notificationPlatforms;
  final List<dynamic>? paymentCards;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ANUserModel({
    required this.id,
    required this.firstName,
    this.middleName,
    required this.lastName,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    this.secondaryPhone,
    required this.gender,
    this.birthdate,
    required this.role,
    this.token,
    this.tokenType,
    this.fcmToken,
    this.profilePicture,
    this.firebasePhoto,
    this.notificationPreference,
    this.notificationPlatforms,
    this.paymentCards,
    this.createdAt,
    this.updatedAt,
  });

  factory ANUserModel.fromJson(Map<String, dynamic> json) {
    try {
      return ANUserModel(
        id: json['id'] as String? ?? '',
        firstName: json['first_name'] as String? ?? '',
        middleName: json['middle_name'] as String?,
        lastName: json['last_name'] as String? ?? '',
        fullName: json['full_name'] as String? ?? '',
        email: json['email'] as String? ?? '',
        phoneNumber: json['phone'] as String? ?? '',
        secondaryPhone: json['secondary_phone'] as String?,
        gender: Gender.fromString(json['gender'] as String?),
        birthdate: json['birth_date'] != null ? DateTime.parse(json['birth_date'] as String) : null,
        role: json['roles'] as String? ?? 'client',
        token: json['token'] as String?,
        tokenType: json['tokenType'] as String?,
        fcmToken: json['fcm_token'] as String?,
        profilePicture: json['profile_pic'] != null 
            ? ANImageModel.fromJson(json['profile_pic'] as Map<String, dynamic>) 
            : null,
        firebasePhoto: json['firebase_photo'] as String?,
        notificationPreference: json['notification_preference'] as String?,
        notificationPlatforms: (json['notification_platforms'] as List<dynamic>?)
            ?.map((e) => e as String)
            .toList(),
        paymentCards: json['payment_cards'] as List<dynamic>?,
        createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
        updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
      );
    } catch (e) {
      print('Error parsing user model: $e');
      print('JSON data: $json');
      // Return a minimal user with default values
      return ANUserModel(
        id: json['id'] as String? ?? '',
        firstName: json['first_name'] as String? ?? '',
        lastName: json['last_name'] as String? ?? '',
        fullName: json['full_name'] as String? ?? '',
        email: json['email'] as String? ?? '',
        phoneNumber: json['phone'] as String? ?? '',
        gender: Gender.other,
        role: 'client',
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'middle_name': middleName,
      'last_name': lastName,
      'full_name': fullName,
      'email': email,
      'phone': phoneNumber,
      'secondary_phone': secondaryPhone,
      'gender': gender.toJson,
      'birth_date': birthdate?.toIso8601String(),
      'roles': role,
      'token': token,
      'tokenType': tokenType,
      'fcm_token': fcmToken,
      'profile_pic': profilePicture?.toJson(),
      'firebase_photo': firebasePhoto,
      'notification_preference': notificationPreference,
      'notification_platforms': notificationPlatforms,
      'payment_cards': paymentCards,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Map<String, dynamic> toHeader() {
    return {HttpHeaders.authorizationHeader: '$tokenType $token'};
  }

  ANUserModel copyWith({
    String? id,
    String? firstName,
    String? middleName,
    String? lastName,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? secondaryPhone,
    Gender? gender,
    DateTime? birthdate,
    String? role,
    String? token,
    String? tokenType,
    String? fcmToken,
    ANImageModel? profilePicture,
    String? firebasePhoto,
    String? notificationPreference,
    List<String>? notificationPlatforms,
    List<dynamic>? paymentCards,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ANUserModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      middleName: middleName ?? this.middleName,
      lastName: lastName ?? this.lastName,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      secondaryPhone: secondaryPhone ?? this.secondaryPhone,
      gender: gender ?? this.gender,
      birthdate: birthdate ?? this.birthdate,
      role: role ?? this.role,
      token: token ?? this.token,
      tokenType: tokenType ?? this.tokenType,
      fcmToken: fcmToken ?? this.fcmToken,
      profilePicture: profilePicture ?? this.profilePicture,
      firebasePhoto: firebasePhoto ?? this.firebasePhoto,
      notificationPreference: notificationPreference ?? this.notificationPreference,
      notificationPlatforms: notificationPlatforms ?? this.notificationPlatforms,
      paymentCards: paymentCards ?? this.paymentCards,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
