import 'dart:io';

import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:ajmal_now_doctor/utils/http_error_message.dart';
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANHttpAuthRepository {
  final Ref ref;

  ANHttpAuthRepository({required this.ref});

  Future register(
      {required String idToken,
      required String firstName,
      String? middleName,
      required String lastName,
      required String phoneNumber,
      required String email,
      DateTime? birthDate,
      Gender? gender}) async {
    try {
      final ANResponseModel response =
          await ref.read(networkProvider).postData(path: 'users', headers: {
        HttpHeaders.authorizationHeader: 'Bearer $idToken',
      }, body: {
        'first_name': firstName,
        'middle_name': middleName,
        'last_name': lastName,
        'phone': phoneNumber,
        'birth_date': birthDate?.toIso8601String(),
        'gender': gender?.toJson,
        'email': email,
        'fcm_token': await FirebaseMessaging.instance.getToken(),
      });
      if (response.statusCode == 201 && response.data != null) {
        return ANUserModel.fromJson(response.data!['doc']);
      } else {
        throw '${response.error!.first.message}\n${response.error!.first.data.first.message}';
      }
    } catch (e) {
      rethrow;
    }
  }

  Future login({required String idToken}) async {
    try {
      final ANResponseModel response =
          await ref.read(networkProvider).getData(path: 'users/me', headers: {
        HttpHeaders.authorizationHeader: 'Bearer $idToken',
      });
      if (response.statusCode == 200 && response.data != null) {
        ref.read(networkProvider).patchData(
            path: 'users/${ANUserModel.fromJson(response.data!['doc']).id}',
            headers: {
              HttpHeaders.authorizationHeader: 'Bearer $idToken',
            },
            body: {
              'fcm_token': await FirebaseMessaging.instance.getToken(),
            });
        return ANUserModel.fromJson(response.data!['doc']);
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future updateAccountData(
      {required String idToken,
      required String firstName,
      String? middleName,
      required String lastName,
      DateTime? birthDate,
      Gender? gender,
      File? profilePic}) async {
    try {
      Map<String, dynamic> data = {};
      data['first_name'] = firstName;
      data['middle_name'] = middleName;
      data['last_name'] = lastName;
      data['birth_date'] = birthDate?.toIso8601String();
      data['gender'] = gender?.toJson;
      data['fcm_token'] = await FirebaseMessaging.instance.getToken();
      if (profilePic != null) {
        print('Adding profile pic: ${profilePic.path.split('/').last}');
        data['profile_pic'] = MultipartFile.fromFileSync(profilePic.path,
            filename: profilePic.path.split('/').last);
      }
      final ANResponseModel response = await ref
          .read(networkProvider)
          .patchData(
            path: 'users/${ref.read(authRepositoryProvider).currentUser!.id}',
            headers: {
              HttpHeaders.authorizationHeader: 'Bearer $idToken',
            },
            body: data,
          );
      if (response.statusCode == 200 && response.data != null) {
        return ANUserModel.fromJson(response.data!['doc']);
      } else {
        throw '${response.error!.first.message}\n${response.error!.first.data.first.message}';
      }
    } catch (e) {
      rethrow;
    }
  }
}
