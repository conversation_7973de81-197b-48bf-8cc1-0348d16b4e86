import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/helpers/shared_preferences_helper.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../config.dart';

class ANAuthRepository {
  final Ref ref;

  ANUserModel? _currentUser;

  ANAuthRepository({required this.ref});

  Stream authStateChanges() {
    return ANSharedPreferencesHelper().observeData<ANUserModel?>(
        key: 'USER',
        decoder: (value) {
          if (value != null) {
            _currentUser = ANUserModel.fromJson(
                jsonDecode(value as String) as Map<String, dynamic>);
            return _currentUser;
          } else {
            return null;
          }
        });
  }

  ANUserModel? get currentUser => _currentUser;

  void setUser(ANUserModel user, {String? token, String? tokenType}) {
    if (token != null) {
      _currentUser = user.copyWith(token: token, tokenType: tokenType);
    } else {
      _currentUser = user;
    }
    print("SAVING USERRRRR: ${_currentUser!.toJson()}");
    ANSharedPreferencesHelper().storeData(
      key: 'USER',
      value: _currentUser,
      encoder: (user) {
        return jsonEncode(user.toJson());
      },
    );
    print("USER SAVED: ${_currentUser!.toJson()}");
  }

  Future<bool> login(BuildContext context,
      {required String email, required String password}) async {
    try {
      final url = Uri.parse('${Config.baseUrl}users/login');
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );
      if (response.statusCode == 200) {
        // print("Response: ${response.body}");
        Map<String, dynamic> userMap = jsonDecode(response.body)["user"];
        print("UserMap: $userMap");
        _currentUser = ANUserModel(
          id: userMap["id"],
          firstName: userMap["first_name"],
          fullName: userMap["full_name"],
          lastName: userMap["last_name"],
          email: userMap["email"],
          phoneNumber: userMap["phone"],
          gender: (userMap["gender"] == "male")
              ? Gender.male
              : userMap["gender"] == "female"
                  ? Gender.female
                  : Gender.other,
          role: userMap["roles"],
          middleName: userMap["middle_name"],
          profilePicture: userMap["profile_pic"] != null
              ? ANImageModel.fromJson(userMap["profile_pic"])
              : null,
          birthdate: userMap["birth_date"] != null
              ? DateTime.parse(userMap["birth_date"])
              : null,
        );
        await FirebaseMessaging.instance.subscribeToTopic(_currentUser!.id);
        setUser(_currentUser!,
            token: jsonDecode(response.body)["token"], tokenType: 'Bearer');
        return true;
      } else {
        return false;
      }
    } catch (err) {
      return false;
    }
  }

  Future<bool> register(BuildContext context,
      {required String firstName,
      String? middleName,
      required String lastName,
      required String phoneNumber,
      required String email,
      DateTime? birthDate,
      Gender? gender}) async {
    var completer = Completer<bool>();

    try {
      FirebaseAuth auth = FirebaseAuth.instance;
      // Future.delayed(const Duration(seconds: 20));
      await auth.verifyPhoneNumber(
        phoneNumber: '+2$phoneNumber',
        timeout: const Duration(minutes: 1),
        verificationCompleted: (PhoneAuthCredential credential) async {
          // ANDROID ONLY!

          // Sign the user in (or link) with the auto-generated credential
          await auth.signInWithCredential(credential);
          completer.complete(true);
        },
        verificationFailed: (FirebaseAuthException e) {
          if (e.code == 'invalid-phone-number') {
            print('The provided phone number is not valid.');
          }

          //TODO: Handle other errors
          completer.complete(false);
        },
        codeSent: (String verificationId, int? resendToken) async {
          try {
            // //TODO: Update the UI - wait for the user to enter the SMS code
            // String smsCode = await Navigator.of(context).push(
            //   MaterialPageRoute(
            //     builder: (BuildContext context) =>
            //         ANValidateOtpScreen(verifyOtp: (otp) {
            //       Navigator.pop(context, otp);
            //     }),
            //   ),
            // );

            // // Create a PhoneAuthCredential with the code
            // PhoneAuthCredential credential = PhoneAuthProvider.credential(
            //     verificationId: verificationId, smsCode: smsCode);

            // // Sign the user in (or link) with the credential
            // final UserCredential userCredential =
            //     await auth.signInWithCredential(credential);
            // final String idToken = await userCredential.user!.getIdToken(true);
            // final ANUserModel user =
            //     await ref.read(httpAuthRepositoryProvider).register(
            //           idToken: idToken,
            //           firstName: firstName,
            //           middleName: middleName,
            //           lastName: lastName,
            //           phoneNumber: phoneNumber,
            //           email: email,
            //           gender: gender,
            //           birthDate: birthDate,
            //         );
            // setUser(user, token: idToken, tokenType: 'Bearer');
            // completer.complete(true);
          } catch (e) {
            completer.completeError(e);
          }
        },
        codeAutoRetrievalTimeout: (String verificationId) {},
      );
      return completer.future;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    FirebaseAuth auth = FirebaseAuth.instance;
    await FirebaseMessaging.instance.unsubscribeFromTopic(_currentUser!.id);
    _currentUser = null;
    await Future.wait([
      auth.signOut(),
      ANSharedPreferencesHelper().deleteData(key: 'USER'),
    ]);
  }

  Future<bool> updateAccount(
      {required String firstName,
      String? middleName,
      required String lastName,
      DateTime? birthDate,
      Gender? gender,
      File? profilePic}) async {
    Completer<bool> completer = Completer();
    try {
      final ANUserModel user =
          await ref.read(httpAuthRepositoryProvider).updateAccountData(
                idToken: _currentUser!.token!,
                firstName: firstName,
                middleName: middleName,
                lastName: lastName,
                gender: gender,
                birthDate: birthDate,
                profilePic: profilePic,
              );
      setUser(user,
          token: _currentUser!.token!, tokenType: _currentUser!.tokenType!);
      completer.complete(true);
    } catch (e) {
      completer.completeError(e);
      rethrow;
    }
    return completer.future;
  }

  Future<String> sendOtp(String email) async {
    try {
      final url = Uri.parse('${Config.baseUrl}users/send-otp');
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
        }),
      );
      if (response.statusCode == 200) {
        print("Response: ${response.body}");
        return jsonDecode(response.body)["token"];
      } else {
        String message = jsonDecode(response.body)["error"];
        throw message;
      }
    } catch (error) {
      throw "An error occurred, please try again later";
    }
  }

  Future<bool> verifyOtp(String email, String otp) async {
    try {
      final url = Uri.parse('${Config.baseUrl}users/verify-otp');
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'otp': otp,
        }),
      );
      if (response.statusCode == 200) {
        return true;
      } else {
        String message = jsonDecode(response.body)["error"];
        throw message;
      }
    } catch (error) {
      throw "An error occurred, please try again later";
    }
  }

  Future<bool> resetPassword(String token, String password) async {
    try {
      final url = Uri.parse('${Config.baseUrl}users/$token');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'password': password,
        }),
      );
      if (response.statusCode == 200) {
        return true;
      } else {
        String message;
        if (jsonDecode(response.body)["error"] != null) {
          message = jsonDecode(response.body)["error"];
        } else {
          message = "An error occurred, please try again later";
        }
        throw message;
      }
    } catch (error) {
      throw "An error occurred, please try again later";
    }
  }

  Future<bool> validateUserToken() async {
    var completer = Completer<bool>();
    try {
      FirebaseAuth auth = FirebaseAuth.instance;
      if (auth.currentUser != null) {
        final String? idToken = await auth.currentUser!.getIdToken(true);
        if (idToken != null) {
          final ANUserModel user =
              await ref.read(httpAuthRepositoryProvider).login(idToken: idToken);
          setUser(user, token: idToken, tokenType: 'Bearer');
        } else {
          completer.complete(false);
        }
        completer.complete(true);
      } else {
        completer.complete(false);
      }
    } catch (e) {
      print('Error: ${e.toString()}');
      completer.complete(false);
    }
    return completer.future;
  }

  Future<void> dispose() async =>
      await ANSharedPreferencesHelper().closeStream();

  Future<bool> deleteAccount() async {
    final url = Uri.parse('${Config.baseUrl}/users/${_currentUser!.id}');
    print("URL: $url");
    final response = await http.delete(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
    );
    print("Response: ${response.body}");
    if (response.statusCode == 200) {
      // Login successful, handle the response accordingly
      // Return true if login is successful
      return true;
    } else {
      return false;
    }
  }
}
