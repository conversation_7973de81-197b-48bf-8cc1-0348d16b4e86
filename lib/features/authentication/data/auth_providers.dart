import 'package:ajmal_now_doctor/features/authentication/data/auth_repository.dart';
import 'package:ajmal_now_doctor/features/authentication/data/http_auth_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final httpAuthRepositoryProvider =
    Provider<ANHttpAuthRepository>((ref) => ANHttpAuthRepository(ref: ref));

final authRepositoryProvider = Provider<ANAuthRepository>((ref) {
  final auth = ANAuthRepository(ref: ref);
  ref.onDispose(() => auth.dispose());
  return auth;
});

final authStateChangesProvider = StreamProvider((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return authRepository.authStateChanges();
});
