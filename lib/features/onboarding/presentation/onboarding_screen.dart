import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/onboarding/data/onboarding_repository.dart';
import 'package:ajmal_now_doctor/features/onboarding/presentation/indicator.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:ajmal_now_doctor/config.dart';

class ANOnboardingScreen extends ConsumerStatefulWidget {
  const ANOnboardingScreen({Key? key}) : super(key: key);

  @override
  ConsumerState createState() => _ANOnboardingScreenState();
}

class _ANOnboardingScreenState extends ConsumerState<ANOnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final onboardingData = ref.watch(onboardingDataProvider);

    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: true,
      ),
      body: onboardingData.when(
          skipLoadingOnRefresh: false,
          data: (data) => Stack(
                children: [
                  PageView.builder(
                    itemCount: data.length,
                    controller: _pageController,
                    itemBuilder: (BuildContext context, int index) {
                      return Column(
                        children: [
                          gapHXXXL,
                          gapHXXXL,
                          gapHXL,
                          Image.network(
                            data
                                .elementAt(index)
                                .image
                                .url
                                .replaceFirst("localhost", Config.IP),
                            height: 250,
                          ),
                          if (data.elementAt(index).title != null) ...[
                            gapHXXXL,
                            gapHXXXL,
                            gapHXXXL,
                            Text(
                              data.elementAt(index).title!,
                              style: GoogleFonts.philosopher(
                                textStyle: TextSize.xxr.boldStyle,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ],
                          gapHXL,
                          if (data.elementAt(index).description != null)
                            SizedBox(
                              width: 240,
                              child: Text(
                                data.elementAt(index).description!,
                                textAlign: TextAlign.center,
                                style: TextSize.l.mediumStyle.copyWith(
                                    color: Colors.black.withOpacity(0.5)),
                              ),
                            ),
                        ],
                      );
                    },
                    onPageChanged: (index) {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                  ),
                  Align(
                    alignment: AlignmentDirectional.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(
                                data.length,
                                (index) => ANIndicator(
                                    currentIndex: _currentIndex,
                                    itemIndex: index)),
                          ),
                          gapHXXXL,
                          Center(
                            child: _currentIndex < data.length - 1
                                ? ANElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _pageController.nextPage(
                                            duration: const Duration(
                                                milliseconds: 200),
                                            curve: Curves.easeInOut);
                                        _currentIndex++;
                                      });
                                    },
                                    child: Text(
                                      S.of(context).kNext,
                                      style: const TextStyle(
                                        color: Colors.white,
                                      ),
                                    ))
                                : ANElevatedButton(
                                    onPressed: () => context
                                            .pushReplacementNamed(
                                                AppRoute.landingScreen.name,
                                                queryParameters: {
                                              'sessionExpiration':
                                                  false.toString()
                                            }),
                                    child: Text(
                                      S.of(context).kGetStarted,
                                      style: const TextStyle(
                                        color: Colors.white,
                                      ),
                                    )),
                          ),
                          gapHXXXL,
                          if (_currentIndex < data.length - 1)
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _pageController.animateToPage(4,
                                      duration:
                                          const Duration(milliseconds: 200),
                                      curve: Curves.easeInOut);
                                  _currentIndex = data.length - 1;
                                });
                              },
                              child: Text(
                                S.of(context).kSkip,
                                style: TextSize.r.boldStyle
                                    .copyWith(color: AppColors.primaryColor),
                              ),
                            )
                        ],
                      ),
                    ),
                  )
                ],
              ),
          error: (error, _) => Center(
                child: ANError(
                  errorMessage: error.toString(),
                  refreshCallback: () async {
                    await ref.refresh(onboardingDataProvider.future);
                  },
                ),
              ),
          loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.0),
                  child:
                      CircularProgressIndicator(color: AppColors.primaryColor),
                ),
              )),
    );
  }
}
