import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:flutter/material.dart';

class ANIndicator extends StatelessWidget {
  final int currentIndex;
  final int itemIndex;

  const ANIndicator(
      {Key? key, required this.currentIndex, required this.itemIndex})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 100),
      margin: const EdgeInsets.only(right: 6),
      height: 10.0,
      width: currentIndex == itemIndex ? 40.0 : 10.0,
      decoration: BoxDecoration(
        color: AppColors.primaryColor,
        borderRadius: BorderRadius.circular(10),
      ),
    );
  }
}
