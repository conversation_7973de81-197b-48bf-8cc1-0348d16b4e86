import 'package:ajmal_now_doctor/features/onboarding/domain/onboarding.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:ajmal_now_doctor/utils/http_error_message.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANHttpOnboardingRepository {
  final Ref ref;

  ANHttpOnboardingRepository({required this.ref});

  Future<List<ANOnboardingModel>> getOnboardingScreens() async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'globals/onboarding_screens_providers',
          );
      if (response.statusCode == 200 && response.data != null) {
        if (ref.read(languageProvider).lang!.languageCode == 'en') {
          return (response.data!['screens_en'] as List)
              .map((e) => ANOnboardingModel.fromJson(e))
              .toList();
        } else {
          return (response.data!['screens_ar'] as List)
              .map((e) => ANOnboardingModel.fromJson(e))
              .toList();
        }
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }
}

final httpOnboardingProvider = Provider<ANHttpOnboardingRepository>(
    (ref) => ANHttpOnboardingRepository(ref: ref));
