import 'package:ajmal_now_doctor/features/onboarding/data/http_onboarding_repository.dart';
import 'package:ajmal_now_doctor/features/onboarding/domain/onboarding.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANOnboardingRepository {
  final Ref ref;

  ANOnboardingRepository({required this.ref});

  Future<List<ANOnboardingModel>> getOnboardingScreens() async {
    try {
      final List<ANOnboardingModel> onboardingScreens =
          await ref.read(httpOnboardingProvider).getOnboardingScreens();
      return onboardingScreens;
    } catch (e) {
      rethrow;
    }
  }
}

final onboardingProvider = Provider((ref) => ANOnboardingRepository(ref: ref));

final onboardingDataProvider = FutureProvider(
    (ref) => ref.read(onboardingProvider).getOnboardingScreens());
