import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'onboarding.freezed.dart';
part 'onboarding.g.dart';

@freezed
class ANOnboardingModel with _$ANOnboardingModel {
  const factory ANOnboardingModel({
    required String id,
    String? title,
    String? description,
    required ANImageModel image,
  }) = _ANOnboardingModel;

  factory ANOnboardingModel.fromJson(Map<String, Object?> json) =>
      _$ANOnboardingModelFromJson(json);
}

/*
{
    "image": {
        "id": "639307d9963e49e8794f319e",
        "filename": "Onboarding - 1.png",
        "mimeType": "image/png",
        "filesize": 123497,
        "width": 1500,
        "height": 3248,
        "createdAt": "2022-12-09T10:03:05.707Z",
        "updatedAt": "2022-12-09T10:03:05.707Z",
        "url": "https://ajmalnow.com/onbaording/Onboarding - 1.png"
    },
    "id": "63930817ffdea742abc188ef"
}
*/
