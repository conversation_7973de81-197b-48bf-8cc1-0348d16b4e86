// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'onboarding.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ANOnboardingModel _$ANOnboardingModelFromJson(Map<String, dynamic> json) {
  return _ANOnboardingModel.fromJson(json);
}

/// @nodoc
mixin _$ANOnboardingModel {
  String get id => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  ANImageModel get image => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ANOnboardingModelCopyWith<ANOnboardingModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANOnboardingModelCopyWith<$Res> {
  factory $ANOnboardingModelCopyWith(
          ANOnboardingModel value, $Res Function(ANOnboardingModel) then) =
      _$ANOnboardingModelCopyWithImpl<$Res, ANOnboardingModel>;
  @useResult
  $Res call(
      {String id, String? title, String? description, ANImageModel image});

  $ANImageModelCopyWith<$Res> get image;
}

/// @nodoc
class _$ANOnboardingModelCopyWithImpl<$Res, $Val extends ANOnboardingModel>
    implements $ANOnboardingModelCopyWith<$Res> {
  _$ANOnboardingModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? image = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      image: null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ANImageModelCopyWith<$Res> get image {
    return $ANImageModelCopyWith<$Res>(_value.image, (value) {
      return _then(_value.copyWith(image: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ANOnboardingModelCopyWith<$Res>
    implements $ANOnboardingModelCopyWith<$Res> {
  factory _$$_ANOnboardingModelCopyWith(_$_ANOnboardingModel value,
          $Res Function(_$_ANOnboardingModel) then) =
      __$$_ANOnboardingModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String? title, String? description, ANImageModel image});

  @override
  $ANImageModelCopyWith<$Res> get image;
}

/// @nodoc
class __$$_ANOnboardingModelCopyWithImpl<$Res>
    extends _$ANOnboardingModelCopyWithImpl<$Res, _$_ANOnboardingModel>
    implements _$$_ANOnboardingModelCopyWith<$Res> {
  __$$_ANOnboardingModelCopyWithImpl(
      _$_ANOnboardingModel _value, $Res Function(_$_ANOnboardingModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? image = null,
  }) {
    return _then(_$_ANOnboardingModel(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      image: null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as ANImageModel,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ANOnboardingModel implements _ANOnboardingModel {
  const _$_ANOnboardingModel(
      {required this.id, this.title, this.description, required this.image});

  factory _$_ANOnboardingModel.fromJson(Map<String, dynamic> json) =>
      _$$_ANOnboardingModelFromJson(json);

  @override
  final String id;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final ANImageModel image;

  @override
  String toString() {
    return 'ANOnboardingModel(id: $id, title: $title, description: $description, image: $image)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ANOnboardingModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, description, image);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ANOnboardingModelCopyWith<_$_ANOnboardingModel> get copyWith =>
      __$$_ANOnboardingModelCopyWithImpl<_$_ANOnboardingModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ANOnboardingModelToJson(
      this,
    );
  }
}

abstract class _ANOnboardingModel implements ANOnboardingModel {
  const factory _ANOnboardingModel(
      {required final String id,
      final String? title,
      final String? description,
      required final ANImageModel image}) = _$_ANOnboardingModel;

  factory _ANOnboardingModel.fromJson(Map<String, dynamic> json) =
      _$_ANOnboardingModel.fromJson;

  @override
  String get id;
  @override
  String? get title;
  @override
  String? get description;
  @override
  ANImageModel get image;
  @override
  @JsonKey(ignore: true)
  _$$_ANOnboardingModelCopyWith<_$_ANOnboardingModel> get copyWith =>
      throw _privateConstructorUsedError;
}
