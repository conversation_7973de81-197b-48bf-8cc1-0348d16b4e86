import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/calender/domian/appointment/appointment.dart';
import 'package:ajmal_now_doctor/helpers/networking/domain/response.dart';
import 'package:ajmal_now_doctor/helpers/networking/network_helper.dart';
import 'package:ajmal_now_doctor/utils/http_error_message.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANHttpAppointmentsRepository {
  final Ref ref;

  ANHttpAppointmentsRepository({required this.ref});

  Future<ANAppointmentModel> createAppointment(
      {required String serviceProviderId,
      required ANSlotsModel timeSlot,
      required String purpose,
      required ANUserModel user,
      required String serviceId}) async {
    try {
      final ANResponseModel response = await ref
          .read(networkProvider)
          .postData(path: 'appointments', headers: user.toHeader(), body: {
        'client': ref.read(authRepositoryProvider).currentUser!.id,
        'service': serviceProviderId,
        'appointment_date': timeSlot.starts.toIso8601String(),
        'appointment_start': timeSlot.starts.toIso8601String(),
        'appointment_ends': timeSlot.ends?.toIso8601String() ?? 
            timeSlot.starts.add(const Duration(minutes: 30)).toIso8601String(),
        'appointment_slot': timeSlot.id,
        'appointment_service': serviceId,
        'purpose': purpose,
        'status': 'blocked',
      });
      if (response.statusCode == 201 && response.data != null) {
        return ANAppointmentModel.fromJson(response.data!['doc']);
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<ANAppointmentModel> getAppointmentById(
      {required String appointmentId}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).getData(
            path: 'appointments/$appointmentId',
            headers: ref.read(authRepositoryProvider).currentUser!.toHeader(),
          );
      if (response.statusCode == 200 && response.data != null) {
        return ANAppointmentModel.fromJson(response.data!);
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> completeAppointment({required String appointmentId}) async {
    try {
      final ANResponseModel response = await ref.read(networkProvider).postData(
            path: 'appointments/$appointmentId/complete',
            headers: ref.read(authRepositoryProvider).currentUser!.toHeader(),
          );
      print(response);
      if (response.statusCode == 200) {
        return true;
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteAppointment({required String appointmentId}) async {
    try {
      final ANResponseModel response = await ref
          .read(networkProvider)
          .deleteData(
            path: 'appointments/$appointmentId',
            headers: ref.read(authRepositoryProvider).currentUser!.toHeader(),
          );
      if (response.statusCode == 200) {
        return true;
      } else {
        throw getErrorMessage(response);
      }
    } catch (e) {
      rethrow;
    }
  }
}

final httpAppointmentsProvider = Provider<ANHttpAppointmentsRepository>(
    (ref) => ANHttpAppointmentsRepository(ref: ref));
