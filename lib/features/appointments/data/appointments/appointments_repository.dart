import 'dart:async';
import 'dart:convert';

import 'package:ajmal_now_doctor/common/domain/slots/slots.dart';
import 'package:ajmal_now_doctor/features/appointments/data/appointments/http_appointments_repository.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/calender/domian/appointment/appointment.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANAppointmentsRepository {
  final Ref ref;

  ANAppointmentsRepository({required this.ref});

  Future<ANAppointmentModel> createAppointment(
      {required String serviceProviderId,
      required String serviceId,
      required ANSlotsModel timeSlot,
      required String purpose,
      required ANUserModel user}) async {
    try {
      return await ref.read(httpAppointmentsProvider).createAppointment(
          user: user,
          serviceProviderId: serviceProviderId,
          serviceId: serviceId,
          timeSlot: timeSlot,
          purpose: purpose);
    } catch (e) {
      rethrow;
    }
  }

  Future<ANAppointmentModel> getAppointmentById(
      {required String appointmentId}) async {
    try {
      return await ref
          .read(httpAppointmentsProvider)
          .getAppointmentById(appointmentId: appointmentId);
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> completeAppointment({required String appointmentId}) async {
    try {
      return await ref
          .read(httpAppointmentsProvider)
          .completeAppointment(appointmentId: appointmentId);
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteAppointment({required String appointmentId}) async {
    try {
      return await ref
          .read(httpAppointmentsProvider)
          .deleteAppointment(appointmentId: appointmentId);
    } catch (e) {
      rethrow;
    }
  }

  void resetData() {}
}

final appointmentsProvider =
    Provider.autoDispose((ref) => ANAppointmentsRepository(ref: ref));

final appointmentByIdProvider = FutureProvider.family.autoDispose(
    (ref, String appointmentId) async => ref
        .read(appointmentsProvider)
        .getAppointmentById(appointmentId: appointmentId));

final createAppointmentProvider = FutureProvider.family.autoDispose(
  (ref, String reservation) async {
    var data = jsonDecode(reservation);
    return ref.read(appointmentsProvider).createAppointment(
        user: ANUserModel.fromJson(data['client']),
        serviceProviderId: ANServiceModel.fromJson(data['serviceProvider']).id,
        serviceId: data['serviceId'],
        timeSlot: ANSlotsModel.fromJson(data['timeSlot']),
        purpose: data['purpose']);
  },
);
