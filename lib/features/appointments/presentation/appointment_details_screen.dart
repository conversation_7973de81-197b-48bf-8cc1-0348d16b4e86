import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/appointments/data/appointments/appointments_repository.dart';
import 'package:ajmal_now_doctor/features/calender/data/calender_repository.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class ANAppointmentDetailsScreen extends ConsumerStatefulWidget {
  final String appointmentId;

  const ANAppointmentDetailsScreen({Key? key, required this.appointmentId})
      : super(key: key);

  @override
  ConsumerState createState() => _ANAppointmentDetailsScreenState();
}

class _ANAppointmentDetailsScreenState
    extends ConsumerState<ANAppointmentDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final reservationController =
        ref.watch(appointmentByIdProvider(widget.appointmentId));

    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
      ),
      appbar: ANMainScreenAppbar(
        centerTitle: true,
        title: Text(
          S.of(context).kAppointmentDetails,
        ),
      ),
      body: reservationController.when(
        skipLoadingOnRefresh: false,
        data: (data) => SingleChildScrollView(
          child: Column(
            children: [
              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: circularBorderR,
                ),
                elevation: 5.0,
                child: Padding(
                  padding: screenPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(
                        width: 70.0,
                        child: CircleAvatar(
                          radius: 40.0,
                          backgroundColor: AppColors.primaryColor,
                          child: data.client.profilePicture != null
                              ? CircleAvatar(
                                  radius: 37.0,
                                  backgroundImage: NetworkImage(
                                      data.client.profilePicture!.url),
                                )
                              : const CircleAvatar(
                                  radius: 37.0,
                                  backgroundColor: AppColors.backgroundColor,
                                  child: Icon(
                                    Icons.person,
                                    color: AppColors.primaryColor,
                                    size: 45.0,
                                  ),
                                ),
                        ),
                      ),
                      gapHR,
                      Text(
                        '${data.client.firstName} ${data.client.lastName}',
                        style: GoogleFonts.montserrat(
                            textStyle: TextSize.xl.regularStyle),
                        textAlign: TextAlign.center,
                      ),
                      gapHXXL,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Text(
                                S.of(context).kGender,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                          Expanded(
                              flex: 3,
                              child: Text(
                                data.client.gender.name,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                        ],
                      ),
                      if (data.client.birthdate != null)
                        Column(
                          children: [
                            const Divider(
                              thickness: 0.5,
                              color: Colors.grey,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                    flex: 2,
                                    child: Text(
                                      S.of(context).kBirthDate,
                                      style: GoogleFonts.montserrat(
                                          textStyle: TextSize.r.lightStyle),
                                    )),
                                Expanded(
                                    flex: 3,
                                    child: Text(
                                      DateFormat()
                                          .add_yMMMMd()
                                          .format(data.client.birthdate!),
                                      style: GoogleFonts.montserrat(
                                          textStyle: TextSize.r.lightStyle),
                                    )),
                              ],
                            ),
                          ],
                        ),
                      const Divider(
                        thickness: 0.5,
                        color: Colors.grey,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Text(
                                S.of(context).kPhoneNumber,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                          Expanded(
                              flex: 3,
                              child: Text(
                                data.client.phoneNumber,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                        ],
                      ),
                      const Divider(
                        thickness: 0.5,
                        color: Colors.grey,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Text(
                                S.of(context).kBookingId,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                          Expanded(
                              flex: 3,
                              child: Text(
                                data.id!,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                        ],
                      ),
                      const Divider(
                        thickness: 0.5,
                        color: Colors.grey,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Text(
                                S.of(context).kDate,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                          Expanded(
                            flex: 3,
                            child: Text(
                              DateFormat()
                                  .add_yMMMMd()
                                  .format(data.appointmentDate.toLocal()),
                              style: GoogleFonts.montserrat(
                                  textStyle: TextSize.r.lightStyle),
                            ),
                          ),
                        ],
                      ),
                      const Divider(
                        thickness: 0.5,
                        color: Colors.grey,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Text(
                                S.of(context).kTime,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                          Expanded(
                            flex: 3,
                            child: Text(
                              DateFormat()
                                  .add_jm()
                                  .format(data.appointmentDate.toLocal()),
                              style: GoogleFonts.montserrat(
                                  textStyle: TextSize.r.lightStyle),
                            ),
                          ),
                        ],
                      ),
                      const Divider(
                        thickness: 0.5,
                        color: Colors.grey,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 2,
                              child: Text(
                                S.of(context).kStatus,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                          Expanded(
                              flex: 3,
                              child: Text(
                                data.status.name.capsEveryFirstLetter,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              )),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              if (data.arrivedAt != null)
                Column(
                  children: [
                    gapHR,
                    Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: circularBorderR,
                      ),
                      elevation: 5.0,
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Padding(
                          padding: screenPadding,
                          child: Column(
                            children: [
                              Text(
                                S.of(context).kClientArrivedAt,
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              ),
                              Text(
                                DateFormat()
                                    .add_yMMMMd()
                                    .add_jm()
                                    .format(data.arrivedAt!.toLocal()),
                                style: GoogleFonts.montserrat(
                                    textStyle: TextSize.r.lightStyle),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              gapHXXXL,
              // Temporarily removed Check in client button
              /*
              ANElevatedButton(
                onPressed: data.status != HistoryStatus.paid
                    ? null
                    : () async {
                        try {
                          final bool result = await ref
                              .read(appointmentsProvider)
                              .completeAppointment(appointmentId: data.id!);
                          if (result) {
                            ANDialogHelper.gShowSuccessDialog(
                                    context: context,
                                    title: S
                                        .of(context)
                                        .kClientCheckedInSuccessfully,
                                    primaryButtonLabel: S.of(context).kConfirm)
                                .then((value) {
                              ref.refresh(
                                  appointmentByIdProvider(widget.appointmentId)
                                      .future);
                            });
                          }
                        } catch (e) {
                          ANDialogHelper.gShowCustomDialog(
                              context: context,
                              title: S.of(context).kSomethingWentWrong,
                              body: Text(
                                e.toString(),
                                textAlign: TextAlign.center,
                              ),
                              primaryButtonLabel: S.of(context).kConfirm,
                              primaryButtonCallBack: () {
                                Navigator.pop(context);
                              });
                        }
                      },
                child: Text(
                  S.of(context).kCheckInClient,
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
              */
            ],
          ),
        ),
        error: (error, _) => Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
                child: ANError(
              errorMessage: error.toString(),
              refreshCallback: () async {
                await ref.refresh(
                    appointmentByIdProvider(widget.appointmentId).future);
              },
            )),
          ],
        ),
        loading: () => const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 10.0),
                child: CircularProgressIndicator(color: AppColors.primaryColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
