import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/profile/presentation/age_card.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';

class ANAgeSelector extends StatefulWidget {
  const ANAgeSelector({Key? key}) : super(key: key);

  @override
  State<ANAgeSelector> createState() => _ANAgeSelectorState();
}

class _ANAgeSelectorState extends State<ANAgeSelector> {
  String _selectedAge = '';

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Age group'.hardcoded,
          style: TextSize.xs.mediumStyle,
        ),
        gapHXL,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ANAgeCard(
                label: '20+'.hardcoded,
                groupSelection: _selectedAge,
                onTap: (value) => setState(() => _selectedAge = value)),
            ANAgeCard(
                label: '30+'.hardcoded,
                groupSelection: _selectedAge,
                onTap: (value) => setState(() => _selectedAge = value)),
            ANAgeCard(
                label: '40+'.hardcoded,
                groupSelection: _selectedAge,
                onTap: (value) => setState(() => _selectedAge = value)),
            ANAgeCard(
                label: '50+'.hardcoded,
                groupSelection: _selectedAge,
                onTap: (value) => setState(() => _selectedAge = value)),
            ANAgeCard(
                label: '60+'.hardcoded,
                groupSelection: _selectedAge,
                onTap: (value) => setState(() => _selectedAge = value)),
            ANAgeCard(
                label: '70+'.hardcoded,
                groupSelection: _selectedAge,
                onTap: (value) => setState(() => _selectedAge = value)),
          ],
        )
      ],
    );
  }
}
