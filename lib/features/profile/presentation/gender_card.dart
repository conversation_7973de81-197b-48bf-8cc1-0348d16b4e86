import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class ANGenderCard extends StatelessWidget {
  final Gender gender;
  Gender? selectedGender;
  final Function(Gender) onTap;

  ANGenderCard(
      {Key? key,
      required this.gender,
      required this.onTap,
      required this.selectedGender})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: ANElevatedButton(
        onPressed: () => onTap.call(gender),
        backgroundColor:
            selectedGender == gender ? AppColors.primaryColor : Colors.white,
        child: Text(
          gender.name,
          style: TextSize.xs.mediumStyle.copyWith(
              color: selectedGender == gender
                  ? Colors.white
                  : AppColors.primaryColor),
        ),
      ),
    );
  }
}
