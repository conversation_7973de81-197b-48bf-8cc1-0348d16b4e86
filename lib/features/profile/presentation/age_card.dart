import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class ANAgeCard extends StatelessWidget {
  final String label;
  String groupSelection;
  final Function(String) onTap;
  ANAgeCard(
      {Key? key,
      required this.label,
      required this.groupSelection,
      required this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(label),
      child: Container(
        height: 60.0,
        width: 40.0,
        padding: const EdgeInsets.symmetric(vertical: 15.0),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color:
              groupSelection == label ? AppColors.primaryColor : Colors.white,
          borderRadius: circularBorderXXS,
        ),
        child: Text(
          label,
          style: TextSize.xs.mediumStyle.copyWith(
              color: groupSelection == label
                  ? Colors.white
                  : AppColors.primaryColor),
        ),
      ),
    );
  }
}
