import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/text_fields/text-field.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/experience/experience.dart';
import 'package:ajmal_now_doctor/features/profile/data/background_provider.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class ANExperienceScreen extends ConsumerStatefulWidget {
  const ANExperienceScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ANExperienceScreen> createState() => _ANExperienceScreenState();
}

class _ANExperienceScreenState extends ConsumerState<ANExperienceScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<ANExperienceModel> experiences = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadExperiences();
  }

  Future<void> _loadExperiences() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final provider = await ref.read(backgroundRepositoryProvider).getProviderData();
      if (provider != null) {
        setState(() {
          experiences = provider.experience;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _showExperienceDialog({ANExperienceModel? experience}) {
    final TextEditingController positionController = TextEditingController(
      text: experience?.jobPosition ?? '',
    );
    final TextEditingController institutionController = TextEditingController(
      text: experience?.institution ?? '',
    );
    final TextEditingController startDateController = TextEditingController(
      text: experience?.startDate != null 
          ? DateFormat('yyyy-MM-dd').format(experience!.startDate!)
          : '',
    );
    final TextEditingController endDateController = TextEditingController(
      text: experience?.endDate != null 
          ? DateFormat('yyyy-MM-dd').format(experience!.endDate!)
          : '',
    );
    
    DateTime selectedStartDate = experience?.startDate ?? DateTime.now();
    DateTime? selectedEndDate = experience?.endDate;
    bool currentlyWorking = experience?.currentlyWorking ?? false;

    ANDialogHelper.gShowCustomDialog(
      context: context,
      title: experience == null ? 'Add Experience' : 'Edit Experience',
      body: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ANTextField(
                controller: positionController,
                hint: 'Job Position (Optional)',
              ),
              gapHL,
              ANTextField(
                controller: institutionController,
                hint: 'Institution/Company (Optional)',
              ),
              gapHL,
              ANTextField(
                controller: startDateController,
                hint: 'Start Date (Optional)',
                readOnly: true,
                onTap: () async {
                  final DateTime? picked = await showDatePicker(
                    context: context,
                    initialDate: selectedStartDate,
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: Theme.of(context).colorScheme.copyWith(
                            primary: AppColors.primaryColor,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  );
                  if (picked != null) {
                    setDialogState(() {
                      selectedStartDate = picked;
                      startDateController.text = DateFormat('yyyy-MM-dd').format(picked);
                    });
                  }
                },
              ),
              gapHL,
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.primaryColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  children: [
                    Checkbox(
                      value: currentlyWorking,
                      onChanged: (bool? value) {
                        setDialogState(() {
                          currentlyWorking = value ?? false;
                          if (currentlyWorking) {
                            selectedEndDate = null;
                            endDateController.clear();
                          }
                        });
                      },
                      activeColor: AppColors.primaryColor,
                    ),
                    Expanded(
                      child: Text(
                        'Currently working here',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (!currentlyWorking) ...[
                gapHL,
                ANTextField(
                  controller: endDateController,
                  hint: 'End Date (Optional)',
                  readOnly: true,
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedEndDate ?? DateTime.now(),
                      firstDate: selectedStartDate,
                      lastDate: DateTime.now(),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: Theme.of(context).colorScheme.copyWith(
                              primary: AppColors.primaryColor,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setDialogState(() {
                        selectedEndDate = picked;
                        endDateController.text = DateFormat('yyyy-MM-dd').format(picked);
                      });
                    }
                  },
                ),
              ],
            ],
          );
        },
      ),
      primaryButtonLabel: experience == null ? 'Add Experience' : 'Update Experience',
      primaryButtonCallBack: () async {
        // Allow saving even with minimal information
        final newExperience = ANExperienceModel(
          id: experience?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
          jobPosition: positionController.text.trim().isEmpty ? null : positionController.text,
          institution: institutionController.text.trim().isEmpty ? null : institutionController.text,
          startDate: startDateController.text.isEmpty ? null : selectedStartDate,
          currentlyWorking: currentlyWorking,
          endDate: currentlyWorking ? null : selectedEndDate,
        );

        bool success;
        if (experience == null) {
          success = await ref.read(backgroundRepositoryProvider).addExperience(newExperience);
        } else {
          success = await ref.read(backgroundRepositoryProvider).updateExperience(newExperience);
        }

        Navigator.of(context).pop();
        if (success) {
          _loadExperiences();
          ANDialogHelper.gShowSuccessDialog(
            context: context,
            title: experience == null 
                ? 'Experience added successfully!'
                : 'Experience updated successfully!',
            primaryButtonLabel: 'OK',
          );
        } else {
          ANDialogHelper.gShowConfirmationDialog(
            context: context,
            message: 'Failed to save experience. Please try again.',
            type: DialogType.confirm,
          );
        }
      },
      secondaryButtonLabel: 'Cancel',
      secondaryButtonCallBack: () => Navigator.of(context).pop(),
    );
  }

  String _formatDateRange(ANExperienceModel experience) {
    if (experience.startDate == null) {
      return 'Date not specified';
    }
    
    final startDate = DateFormat('MMM yyyy').format(experience.startDate!);
    if (experience.currentlyWorking == true) {
      return '$startDate - Present';
    } else if (experience.endDate != null) {
      final endDate = DateFormat('MMM yyyy').format(experience.endDate!);
      return '$startDate - $endDate';
    }
    return startDate;
  }

  String _calculateDuration(ANExperienceModel experience) {
    if (experience.startDate == null) {
      return '';
    }
    
    final startDate = experience.startDate!;
    final endDate = experience.currentlyWorking == true ? DateTime.now() : experience.endDate;
    
    if (endDate != null) {
      final difference = endDate.difference(startDate);
      final months = (difference.inDays / 30).round();
      final years = months ~/ 12;
      final remainingMonths = months % 12;
      
      if (years > 0 && remainingMonths > 0) {
        return '$years yr ${remainingMonths} mos';
      } else if (years > 0) {
        return '$years yr';
      } else {
        return '$remainingMonths mos';
      }
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableBottomNavBar: false,
        disableAppbar: true,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Custom header with back button and title
          Padding(
            padding: const EdgeInsets.only(top: 50, left: 16, right: 16, bottom: 20),
            child: Row(
              children: [
                const ANBackButton(),
                gapWL,
                Expanded(
                  child: Text(
                    'Work Experience',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                  ))
                : Column(
                    children: [
                      Expanded(
                        child: experiences.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.work_outline,
                                      size: 80,
                                      color: Colors.grey[400],
                                    ),
                                    gapHL,
                                    Text(
                                      'No work experience added yet',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    gapHS,
                                    Text(
                                      'Add your professional experience to\nshowcase your career journey (Optional)',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: experiences.length,
                                itemBuilder: (context, index) {
                                  final experience = experiences[index];
                                  final duration = _calculateDuration(experience);
                                  
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primaryColor.withOpacity(0.1),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: AppColors.primaryColor.withOpacity(0.2),
                                        width: 1,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Container(
                                                padding: const EdgeInsets.all(8),
                                                decoration: BoxDecoration(
                                                  color: AppColors.primaryColor.withOpacity(0.1),
                                                  borderRadius: BorderRadius.circular(8),
                                                ),
                                                child: Icon(
                                                  Icons.work,
                                                  color: AppColors.primaryColor,
                                                  size: 20,
                                                ),
                                              ),
                                              gapWL,
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      experience.jobPosition ?? 'Position',
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                        fontWeight: FontWeight.w600,
                                                        color: Colors.black87,
                                                      ),
                                                    ),
                                                    gapHXXS,
                                                    Text(
                                                      experience.institution ?? 'Institution',
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        color: Colors.grey[600],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              if (experience.currentlyWorking == true)
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                  decoration: BoxDecoration(
                                                    color: Colors.green.withOpacity(0.1),
                                                    borderRadius: BorderRadius.circular(12),
                                                    border: Border.all(
                                                      color: Colors.green.withOpacity(0.3),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  child: Text(
                                                    'Current',
                                                    style: TextStyle(
                                                      fontSize: 11,
                                                      color: Colors.green[700],
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                  ),
                                                ),
                                              gapWS,
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.edit,
                                                      color: AppColors.primaryColor,
                                                      size: 20,
                                                    ),
                                                    onPressed: () => _showExperienceDialog(experience: experience),
                                                    padding: const EdgeInsets.all(4),
                                                    constraints: const BoxConstraints(
                                                      minWidth: 32,
                                                      minHeight: 32,
                                                    ),
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.delete,
                                                      color: Colors.red,
                                                      size: 20,
                                                    ),
                                                    onPressed: () {
                                                      ANDialogHelper.gShowCustomDialog(
                                                        context: context,
                                                        title: 'Delete Experience',
                                                        body: const Text('Are you sure you want to delete this work experience?'),
                                                        primaryButtonLabel: 'Delete',
                                                        primaryButtonCallBack: () async {
                                                          final success = await ref
                                                              .read(backgroundRepositoryProvider)
                                                              .deleteExperience(experience.id);
                                                          Navigator.of(context).pop();
                                                          if (success) {
                                                            _loadExperiences();
                                                            ANDialogHelper.gShowSuccessDialog(
                                                              context: context,
                                                              title: 'Experience deleted successfully!',
                                                              primaryButtonLabel: 'OK',
                                                            );
                                                          }
                                                        },
                                                        secondaryButtonLabel: 'Cancel',
                                                        secondaryButtonCallBack: () => Navigator.of(context).pop(),
                                                      );
                                                    },
                                                    padding: const EdgeInsets.all(4),
                                                    constraints: const BoxConstraints(
                                                      minWidth: 32,
                                                      minHeight: 32,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          gapHS,
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.calendar_today,
                                                size: 14,
                                                color: Colors.grey[500],
                                              ),
                                              gapWXS,
                                              Text(
                                                _formatDateRange(experience),
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey[500],
                                                ),
                                              ),
                                              if (duration.isNotEmpty) ...[
                                                Text(
                                                  ' • ',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.grey[500],
                                                  ),
                                                ),
                                                Text(
                                                  duration,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.grey[500],
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                      
                      // Add button
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ANElevatedButton(
                          onPressed: () => _showExperienceDialog(),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.add, color: Colors.white),
                              SizedBox(width: 8),
                              Text('Add Experience', style: TextStyle(color: Colors.white)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
} 