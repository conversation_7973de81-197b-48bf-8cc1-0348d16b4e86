import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/profile/presentation/gender_card.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:flutter/material.dart';

class ANGenderSelector extends StatefulWidget {
  final Gender? initialGender;
  final bool isRow;
  final Function(Gender?) callback;
  const ANGenderSelector(
      {Key? key,
      this.isRow = false,
      required this.callback,
      this.initialGender})
      : super(key: key);

  @override
  State<ANGenderSelector> createState() => _ANGenderSelectorState();
}

class _ANGenderSelectorState extends State<ANGenderSelector> {
  Gender? _selectedGender;

  @override
  void initState() {
    _selectedGender = widget.initialGender;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isRow) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: textFieldBorderRadius),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              S.of(context).kGender,
              style: TextSize.r.mediumStyle,
            ),
            gapWXL,
            ANGenderCard(
                gender: Gender.male,
                onTap: (gender) {
                  setState(() => _selectedGender = gender);
                  widget.callback.call(_selectedGender);
                },
                selectedGender: _selectedGender),
            // Flexible(child: ANElevatedButton(onPressed: () => setState(() => _selectedGender = Gender.male), child: Text('Male'.hardcoded, style: TextSize.xs.mediumStyle.copyWith(color: _selectedGender == Gender.male ? Colors.white : AppColors.primaryColor),), backgroundColor: _selectedGender == Gender.male ? AppColors.primaryColor : Colors.white,)),
            gapWXS,
            ANGenderCard(
                gender: Gender.female,
                onTap: (gender) {
                  setState(() => _selectedGender = gender);
                  widget.callback.call(_selectedGender);
                },
                selectedGender: _selectedGender),
            // Flexible(child: ANElevatedButton(onPressed: () => setState(() => _selectedGender = Gender.female), child: Text('Female'.hardcoded, style: TextSize.xs.mediumStyle.copyWith(color: _selectedGender == Gender.female ? Colors.white : AppColors.primaryColor),), backgroundColor: _selectedGender == Gender.female ? AppColors.primaryColor : Colors.white,)),
            // gapWXS,
            // ANGenderCard(gender: Gender.na, onTap: (gender) => setState(() => _selectedGender = gender), selectedGender: _selectedGender),
            // Flexible(child: ANElevatedButton(onPressed: () => setState(() => _selectedGender = Gender.na), child: Text('Prefer not to say'.hardcoded, style: TextSize.xs.mediumStyle.copyWith(color: _selectedGender == Gender.na ? Colors.white : AppColors.primaryColor),), backgroundColor: _selectedGender == Gender.na ? AppColors.primaryColor : Colors.white,)),
          ],
        ),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context).kGender,
          style: TextSize.xs.mediumStyle,
        ),
        gapHXL,
        Row(
          children: [
            ANGenderCard(
                gender: Gender.male,
                onTap: (gender) {
                  setState(() => _selectedGender = gender);
                  widget.callback.call(_selectedGender);
                },
                selectedGender: _selectedGender),
            // Flexible(child: ANElevatedButton(onPressed: () => setState(() => _selectedGender = Gender.male), child: Text('Male'.hardcoded, style: TextSize.xs.mediumStyle.copyWith(color: _selectedGender == Gender.male ? Colors.white : AppColors.primaryColor),), backgroundColor: _selectedGender == Gender.male ? AppColors.primaryColor : Colors.white,)),
            gapWXS,
            ANGenderCard(
                gender: Gender.female,
                onTap: (gender) {
                  setState(() => _selectedGender = gender);
                  widget.callback.call(_selectedGender);
                },
                selectedGender: _selectedGender),
            // Flexible(child: ANElevatedButton(onPressed: () => setState(() => _selectedGender = Gender.female), child: Text('Female'.hardcoded, style: TextSize.xs.mediumStyle.copyWith(color: _selectedGender == Gender.female ? Colors.white : AppColors.primaryColor),), backgroundColor: _selectedGender == Gender.female ? AppColors.primaryColor : Colors.white,)),
            // gapWXS,
            // ANGenderCard(gender: Gender.na, onTap: (gender) => setState(() => _selectedGender = gender), selectedGender: _selectedGender),
            // Flexible(child: ANElevatedButton(onPressed: () => setState(() => _selectedGender = Gender.na), child: Text('Prefer not to say'.hardcoded, style: TextSize.xs.mediumStyle.copyWith(color: _selectedGender == Gender.na ? Colors.white : AppColors.primaryColor),), backgroundColor: _selectedGender == Gender.na ? AppColors.primaryColor : Colors.white,)),
          ],
        )
      ],
    );
  }
}
