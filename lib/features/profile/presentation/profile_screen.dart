import 'dart:io';
import 'package:ajmal_now_doctor/common/text_fields/text-field.dart';
import 'package:ajmal_now_doctor/features/profile/data/profile_provider.dart';

import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/general_widgets/guest.dart';
import 'package:ajmal_now_doctor/common/text_fields/text_field.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/profile/presentation/gender_selector.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/image_picker_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class ANProfileScreen extends ConsumerStatefulWidget {
  const ANProfileScreen({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _ANProfileScreenState();
}

class _ANProfileScreenState extends ConsumerState<ANProfileScreen> {
  late bool isLoggedIn;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final TextEditingController _firstNameController = TextEditingController(),
      _middleNameController = TextEditingController(),
      _lastNameController = TextEditingController(),
      _phoneNumberController = TextEditingController(),
      _emailController = TextEditingController(),
      _birthDateController = TextEditingController(),
      _biographyController = TextEditingController();
  String? biography;
  Gender? _selectedGender;
  DateTime _birthDate = DateTime.now().subtract(const Duration(days: 1));
  bool enableGap = false;

  File? _image;

  void initUserData(ANUserModel user) async {
    String bio = await ref.read(profileRepositoryProvider).getBiography();
    setState(() {
      _firstNameController.text = user.firstName;
      _middleNameController.text = user.middleName ?? '';
      _lastNameController.text = user.lastName;
      _phoneNumberController.text = user.phoneNumber;
      _emailController.text = user.email;
      _biographyController.text = bio;
      biography = bio;
      if (user.birthdate != null) {
        _birthDate = user.birthdate!;
        _birthDateController.text =
            DateFormat().add_yMMMMd().format(user.birthdate!);
      }

      _selectedGender = user.gender;
    });
  }

  @override
  void initState() {
    final authController = ref.read(authRepositoryProvider);
    if (authController.currentUser != null) {
      initUserData(authController.currentUser!);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authController = ref.watch(authRepositoryProvider);

    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableBottomNavBar: false,
        disableAppbar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      appbar: ANMainScreenAppbar(
        scaffoldKey: _scaffoldKey,
        centerTitle: true,
        title: Text(
          S.of(context).kAccountDetails,
        ),
      ),
      body: authController.currentUser != null
          ? SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Center(
                    child: SizedBox(
                      width: 85.0,
                      child: Stack(
                        children: [
                          CircleAvatar(
                            radius: 40.0,
                            backgroundColor: AppColors.primaryColor,
                            child: _image == null
                                ? authController.currentUser!.profilePicture !=
                                        null
                                    ? CircleAvatar(
                                        radius: 37.0,
                                        backgroundImage: NetworkImage(
                                            authController.currentUser!
                                                .profilePicture!.url),
                                      )
                                    : const CircleAvatar(
                                        radius: 37.0,
                                        backgroundColor:
                                            AppColors.backgroundColor,
                                        child: Icon(
                                          Icons.person,
                                          color: AppColors.primaryColor,
                                          size: 45.0,
                                        ),
                                      )
                                : CircleAvatar(
                                    radius: 37.0,
                                    backgroundImage: FileImage(_image!),
                                  ),
                          ),
                          PositionedDirectional(
                            bottom: 5.0,
                            end: 0.0,
                            child: CircleAvatar(
                              radius: 15.0,
                              backgroundColor: Colors.white,
                              child: IconButton(
                                padding: EdgeInsets.zero,
                                splashRadius: 25.0,
                                icon: const Icon(
                                  Icons.image,
                                  size: 20.0,
                                  color: Colors.black,
                                ),
                                onPressed: () {
                                  ANDialogHelper.gShowCustomDialog(
                                      context: context,
                                      title: S.of(context).kPickProfileImage,
                                      primaryButtonLabel:
                                          S.of(context).kPickFromGallery,
                                      primaryButtonCallBack: () async {
                                        final tempImages =
                                            await ANImagePickerHelper
                                                .galleryImagePicker();
                                        if (tempImages != null) {
                                          setState(() {
                                            _image = File(tempImages.path);
                                          });
                                          Navigator.pop(context);
                                        }
                                      },
                                      secondaryButtonLabel:
                                          S.of(context).kCaptureFromCamera,
                                      secondaryButtonCallBack: () async {
                                        final tempImages =
                                            await ANImagePickerHelper
                                                .cameraImagePicker();
                                        if (tempImages != null) {
                                          setState(() {
                                            _image = File(tempImages.path);
                                          });
                                          Navigator.pop(context);
                                        }
                                      });
                                },
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  gapHXXL,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${S.of(context).kFirstName} *',
                        style: TextSize.xs.mediumStyle,
                      ),
                      gapHXXS,
                      ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _firstNameController,
                        hint: S.of(context).kFirstName,
                        validator: (value) {
                          return ANValidationsHelper.validateNotEmpty(value!);
                        },
                      ),
                    ],
                  ),
                  gapHL,
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              S.of(context).kMiddleName,
                              style: TextSize.xs.mediumStyle,
                            ),
                            gapHXXS,
                            ANTextField(
                              onFocusChanged: (isFocused) {
                                setState(() {
                                  enableGap = isFocused;
                                });
                              },
                              controller: _middleNameController,
                              hint: S.of(context).kMiddleName,
                            ),
                          ],
                        ),
                      ),
                      gapWL,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${S.of(context).kLastName} *',
                              style: TextSize.xs.mediumStyle,
                            ),
                            gapHXXS,
                            ANTextField(
                              onFocusChanged: (isFocused) {
                                setState(() {
                                  enableGap = isFocused;
                                });
                              },
                              controller: _lastNameController,
                              hint: S.of(context).kLastName,
                              validator: (value) {
                                return ANValidationsHelper.validateNotEmpty(
                                    value!);
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  gapHL,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${S.of(context).kPhoneNumber} *',
                        style: TextSize.xs.mediumStyle,
                      ),
                      gapHXXS,
                      ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _phoneNumberController,
                        hint: S.of(context).kPhoneNumber,
                        readOnly: true,
                        enabled: false,
                        validator: (value) {
                          return ANValidationsHelper.getValidators(validation: {
                            'is_required': true,
                            'validation_type': 'phone',
                          }, value: value);
                        },
                      ),
                    ],
                  ),
                  gapHL,
                  ANGenderSelector(
                      initialGender: _selectedGender,
                      callback: (gender) => setState(() {
                            _selectedGender = gender;
                          })),
                  gapHL,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).kBirthDate,
                        style: TextSize.xs.mediumStyle,
                      ),
                      gapHXXS,
                      ANTextFormField(
                        controller: _birthDateController,
                        hint: S.of(context).kBirthDate,
                        icon: Icons.calendar_month_outlined,
                        readOnly: true,
                        iconAction: () {
                          showModalBottomSheet(
                            context: context,
                            isDismissible: false,
                            shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(30.0))),
                            backgroundColor: Colors.white,
                            builder: (BuildContext context) {
                              return Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      width: 100.0,
                                      height: 10.0,
                                      decoration: BoxDecoration(
                                          color: AppColors.primaryColor,
                                          borderRadius: circularBorderL),
                                    ),
                                    gapHXL,
                                    SizedBox(
                                      height: 200.0,
                                      child: CupertinoDatePicker(
                                        initialDateTime: _birthDate,
                                        // maximumDate: DateTime.now().subtract(
                                        //     const Duration(days: 12 * 365)),
                                        mode: CupertinoDatePickerMode.date,
                                        use24hFormat: true,
                                        // This is called when the user changes the date.
                                        onDateTimeChanged: (DateTime newDate) {
                                          setState(() {
                                            _birthDate = newDate;
                                          });
                                        },
                                      ),
                                    ),
                                    gapHL,
                                    ANElevatedButton(
                                      onPressed: () {
                                        if (_birthDate
                                                .difference(DateTime.now()
                                                    .subtract(const Duration(
                                                        days: 12 * 365)))
                                                .inSeconds <
                                            0) {
                                          setState(() {
                                            _birthDateController.text =
                                                DateFormat()
                                                    .add_yMMMMd()
                                                    .format(_birthDate);
                                          });
                                          Navigator.pop(context);
                                        } else {
                                          ANDialogHelper
                                              .gShowConfirmationDialog(
                                            context: context,
                                            message:
                                                'Please select a valid birth date',
                                            type: DialogType.confirm,
                                          );
                                        }
                                      },
                                      child: const Text(
                                        'Done',
                                        style: TextStyle(
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                  gapHL,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${S.of(context).kEmail} *',
                        style: TextSize.xs.mediumStyle,
                      ),
                      gapHXXS,
                      ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _emailController,
                        hint: S.of(context).kEmail,
                        readOnly: true,
                        enabled: false,
                        validator: (value) {
                          return ANValidationsHelper.getValidators(validation: {
                            'is_required': true,
                            'validation_type': 'email',
                          }, value: value);
                        },
                      ),
                    ],
                  ),
                  gapHXXXL,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Biography',
                        style: TextSize.xs.mediumStyle,
                      ),
                      gapHXXS,
                      ANTextField(
                        onFocusChanged: (isFocused) {
                          setState(() {
                            enableGap = isFocused;
                          });
                        },
                        controller: _biographyController,
                        hint: "Biography",
                      ),
                    ],
                  ),
                  gapHXXXL,
                  ANLoadingElevatedButton(
                      onPressed: () async {
                        try {
                          final bool result = await ref
                              .read(profileRepositoryProvider)
                              .updateProviderData(
                                firstName: _firstNameController.text,
                                middleName: _middleNameController.text,
                                lastName: _lastNameController.text,
                                fullName:
                                    '${_firstNameController.text} ${_middleNameController.text} ${_lastNameController.text}',
                                phoneNumber: _phoneNumberController.text,
                                gender: _selectedGender!.toString(),
                                birthDate: _birthDate,
                                biography:
                                    _biographyController.text != biography
                                        ? _biographyController.text
                                        : null,
                              );
                          if (result) {
                            ANDialogHelper.gShowSuccessDialog(
                                context: context,
                                title: S
                                    .of(context)
                                    .kYourProfileHaveBeenUpdatedSuccessfully,
                                primaryButtonLabel: S.of(context).kConfirm);
                          }
                        } catch (e) {
                          ANDialogHelper.gShowConfirmationDialog(
                              context: context,
                              message: e.toString(),
                              type: DialogType.confirm);
                        }
                      },
                      label: Text(
                        S.of(context).kSave,
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      )),
                  gapHXXXL,
                  
                  // Professional Background Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          'Professional Background',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryColor,
                          ),
                        ),
                        gapHL,
                        Text(
                          'Manage your certifications and work experience to enhance your professional profile. These sections are optional.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        gapHL,
                        Row(
                          children: [
                            Expanded(
                              child: ANElevatedButton(
                                onPressed: () async => context.pushNamed(AppRoute.certificatesScreen.name),
                                child: const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.card_membership, color: Colors.white, size: 20),
                                    SizedBox(width: 8),
                                    Text('Certificates', style: TextStyle(color: Colors.white)),
                                  ],
                                ),
                              ),
                            ),
                            gapWL,
                            Expanded(
                              child: ANElevatedButton(
                                onPressed: () async => context.pushNamed(AppRoute.experienceScreen.name),
                                child: const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.work, color: Colors.white, size: 20),
                                    SizedBox(width: 8),
                                    Text('Experience', style: TextStyle(color: Colors.white)),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  gapHXXXL,
                  ANLoadingElevatedButton(
                      onPressed: () async =>
                          context.pushNamed(AppRoute.changePasswordScreen.name),
                      label: Text(
                        S.of(context).kChangePassword,
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      )),
                  gapHXL,

                  // ANLoadingElevatedButton(
                  //   onPressed: () async {
                  //     final bool result = await authController.deleteAccount();
                  //     if (result) {
                  //       // ignore: use_build_context_synchronously
                  //       await ANDialogHelper.gShowSuccessDialog(
                  //         context: context,
                  //         title: "Your account has been deleted successfully",
                  //         // ignore: use_build_context_synchronously
                  //         primaryButtonLabel: S.of(context).kConfirm,
                  //       );
                  //       // ignore: use_build_context_synchronously
                  //       GoRouter.of(context).pushReplacementNamed(
                  //           AppRoute.landingScreen.name,
                  //           queryParams: {
                  //             'sessionExpiration': true.toString()
                  //           });
                  //     }
                  //   },
                  //   label: const Text(
                  //     "Delete Account",
                  //     style: const TextStyle(
                  //       color: Colors.white,
                  //     ),
                  //   ),
                  //   decoration: BoxDecoration(
                  //     color: Colors.red,
                  //     borderRadius: BorderRadius.circular(15.0),
                  //   ),
                  // ),
                  if (enableGap)
                    const SizedBox(
                      height: 270,
                    ),
                ],
              ),
            )
          : const ANGuest(),
    );
  }
}
