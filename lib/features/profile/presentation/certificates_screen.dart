import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/buttons/loading_elevated_button.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/text_fields/text-field.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/certificates/certificates.dart';
import 'package:ajmal_now_doctor/features/profile/data/background_provider.dart';
import 'package:ajmal_now_doctor/helpers/dialog_helper.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class ANCertificatesScreen extends ConsumerStatefulWidget {
  const ANCertificatesScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ANCertificatesScreen> createState() => _ANCertificatesScreenState();
}

class _ANCertificatesScreenState extends ConsumerState<ANCertificatesScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  List<ANCertificatesModel> certificates = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCertificates();
  }

  Future<void> _loadCertificates() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final provider = await ref.read(backgroundRepositoryProvider).getProviderData();
      if (provider != null) {
        setState(() {
          certificates = provider.certificates;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _showCertificateDialog({ANCertificatesModel? certificate}) {
    final TextEditingController nameController = TextEditingController(
      text: certificate?.certificateName ?? '',
    );
    final TextEditingController institutionController = TextEditingController(
      text: certificate?.issuingInstitution ?? '',
    );
    final TextEditingController dateController = TextEditingController(
      text: certificate?.issueDate != null 
          ? DateFormat('yyyy-MM-dd').format(certificate!.issueDate!)
          : '',
    );
    DateTime selectedDate = certificate?.issueDate ?? DateTime.now();

    ANDialogHelper.gShowCustomDialog(
      context: context,
      title: certificate == null ? 'Add Certificate' : 'Edit Certificate',
      body: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ANTextField(
                controller: nameController,
                hint: 'Certificate Name (Optional)',
              ),
              gapHL,
              ANTextField(
                controller: institutionController,
                hint: 'Issuing Institution (Optional)',
              ),
              gapHL,
              ANTextField(
                controller: dateController,
                hint: 'Issue Date (Optional)',
                readOnly: true,
                onTap: () async {
                  final DateTime? picked = await showDatePicker(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: Theme.of(context).colorScheme.copyWith(
                            primary: AppColors.primaryColor,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  );
                  if (picked != null) {
                    setDialogState(() {
                      selectedDate = picked;
                      dateController.text = DateFormat('yyyy-MM-dd').format(picked);
                    });
                  }
                },
              ),
            ],
          );
        },
      ),
      primaryButtonLabel: certificate == null ? 'Add Certificate' : 'Update Certificate',
      primaryButtonCallBack: () async {
        // Allow saving even with empty fields
        final newCertificate = ANCertificatesModel(
          id: certificate?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
          certificateName: nameController.text.trim().isEmpty ? null : nameController.text,
          issuingInstitution: institutionController.text.trim().isEmpty ? null : institutionController.text,
          issueDate: dateController.text.isEmpty ? null : selectedDate,
        );

        bool success;
        if (certificate == null) {
          success = await ref.read(backgroundRepositoryProvider).addCertificate(newCertificate);
        } else {
          success = await ref.read(backgroundRepositoryProvider).updateCertificate(newCertificate);
        }

        Navigator.of(context).pop();
        if (success) {
          _loadCertificates();
          ANDialogHelper.gShowSuccessDialog(
            context: context,
            title: certificate == null 
                ? 'Certificate added successfully!'
                : 'Certificate updated successfully!',
            primaryButtonLabel: 'OK',
          );
        } else {
          ANDialogHelper.gShowConfirmationDialog(
            context: context,
            message: 'Failed to save certificate. Please try again.',
            type: DialogType.confirm,
          );
        }
      },
      secondaryButtonLabel: 'Cancel',
      secondaryButtonCallBack: () => Navigator.of(context).pop(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableBottomNavBar: false,
        disableAppbar: true,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Custom header with back button and title
          Padding(
            padding: const EdgeInsets.only(top: 50, left: 16, right: 16, bottom: 20),
            child: Row(
              children: [
                const ANBackButton(),
                gapWL,
                Expanded(
                  child: Text(
                    'Certificates',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                  ))
                : Column(
                    children: [
                      Expanded(
                        child: certificates.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.card_membership_outlined,
                                      size: 80,
                                      color: Colors.grey[400],
                                    ),
                                    gapHL,
                                    Text(
                                      'No certificates added yet',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    gapHS,
                                    Text(
                                      'Add your professional certificates to\nenhance your profile (Optional)',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: certificates.length,
                                itemBuilder: (context, index) {
                                  final certificate = certificates[index];
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primaryColor.withOpacity(0.1),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: AppColors.primaryColor.withOpacity(0.2),
                                        width: 1,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Container(
                                                padding: const EdgeInsets.all(8),
                                                decoration: BoxDecoration(
                                                  color: AppColors.primaryColor.withOpacity(0.1),
                                                  borderRadius: BorderRadius.circular(8),
                                                ),
                                                child: Icon(
                                                  Icons.card_membership,
                                                  color: AppColors.primaryColor,
                                                  size: 20,
                                                ),
                                              ),
                                              gapWL,
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      certificate.certificateName ?? 'Certificate',
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                        fontWeight: FontWeight.w600,
                                                        color: Colors.black87,
                                                      ),
                                                    ),
                                                    gapHXXS,
                                                    Text(
                                                      certificate.issuingInstitution ?? 'Institution',
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        color: Colors.grey[600],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.edit,
                                                      color: AppColors.primaryColor,
                                                      size: 20,
                                                    ),
                                                    onPressed: () => _showCertificateDialog(certificate: certificate),
                                                    padding: const EdgeInsets.all(4),
                                                    constraints: const BoxConstraints(
                                                      minWidth: 32,
                                                      minHeight: 32,
                                                    ),
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.delete,
                                                      color: Colors.red,
                                                      size: 20,
                                                    ),
                                                    onPressed: () {
                                                      ANDialogHelper.gShowCustomDialog(
                                                        context: context,
                                                        title: 'Delete Certificate',
                                                        body: const Text('Are you sure you want to delete this certificate?'),
                                                        primaryButtonLabel: 'Delete',
                                                        primaryButtonCallBack: () async {
                                                          final success = await ref
                                                              .read(backgroundRepositoryProvider)
                                                              .deleteCertificate(certificate.id);
                                                          Navigator.of(context).pop();
                                                          if (success) {
                                                            _loadCertificates();
                                                            ANDialogHelper.gShowSuccessDialog(
                                                              context: context,
                                                              title: 'Certificate deleted successfully!',
                                                              primaryButtonLabel: 'OK',
                                                            );
                                                          }
                                                        },
                                                        secondaryButtonLabel: 'Cancel',
                                                        secondaryButtonCallBack: () => Navigator.of(context).pop(),
                                                      );
                                                    },
                                                    padding: const EdgeInsets.all(4),
                                                    constraints: const BoxConstraints(
                                                      minWidth: 32,
                                                      minHeight: 32,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          gapHS,
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.calendar_today,
                                                size: 14,
                                                color: Colors.grey[500],
                                              ),
                                              gapWXS,
                                              Text(
                                                certificate.issueDate != null
                                                    ? 'Issued: ${DateFormat('MMM dd, yyyy').format(certificate.issueDate!)}'
                                                    : 'Issue date not specified',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey[500],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                      
                      // Add button
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ANElevatedButton(
                          onPressed: () => _showCertificateDialog(),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.add, color: Colors.white),
                              SizedBox(width: 8),
                              Text('Add Certificate', style: TextStyle(color: Colors.white)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
} 