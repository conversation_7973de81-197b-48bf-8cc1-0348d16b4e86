import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/back_button.dart';
import 'package:ajmal_now_doctor/common/text_fields/password_text_field.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/profile/data/profile_provider.dart';
import 'package:ajmal_now_doctor/helpers/validation_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../common/buttons/loading_elevated_button.dart';
import '../../../generated/l10n.dart';

class ANChangePasswordScreen extends ConsumerWidget {
  ANChangePasswordScreen({Key? key}) : super(key: key);
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final TextEditingController _currentPasswordController =
          TextEditingController(),
      _newPasswordController = TextEditingController(),
      _confirmPasswordController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ANScaffold(
      scaffoldOptions: const ANScaffoldOptions(
        disableDrawer: true,
        disableAppbar: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ANBackButton(),
          gapHXXXL,
          Text(
            S.of(context).kChangePassword,
            style: GoogleFonts.philosopher(textStyle: TextSize.xxl.boldStyle),
          ),
          gapHR,
          Text(
            S.of(context).kYourNewPasswordMustBeUniqueFromThosePreviouslyUsed,
            style:
                TextSize.r.mediumStyle.copyWith(color: const Color(0xFF8391A1)),
          ),
          gapHXXXL,
          ANPasswordTextField(
            controller: _currentPasswordController,
            hint: S.of(context).kCurrentPassword,
            validator: (value) {
              return ANValidationsHelper.getValidators(validation: {
                'is_required': true,
              }, value: value);
            },
          ),
          gapHL,
          ANPasswordTextField(
            controller: _newPasswordController,
            hint: S.of(context).kNewPassword,
            validator: (value) {
              return ANValidationsHelper.getValidators(validation: {
                'is_required': true,
              }, value: value);
            },
          ),
          gapHL,
          ANPasswordTextField(
            controller: _confirmPasswordController,
            hint: S.of(context).kConfirmPassword,
            validator: (value) {
              return ANValidationsHelper.getValidators(
                validation: {
                  'is_required': true,
                  'validation_type': 'confirmPassword',
                },
                value: value,
                confirmPassword: _newPasswordController.text,
              );
            },
          ),
          const SizedBox(height: 20),
          Center(
            child: ANLoadingElevatedButton(
              onPressed: () async {
                try {
                  await ref
                      .read(profileRepositoryProvider)
                      .updatePassword(
                          currentPassword: _currentPasswordController.text,
                          newPassword: _newPasswordController.text,
                          confirmNewPassword: _confirmPasswordController.text)
                      .then((value) {
                    if (value) {
                      context.pop();
                      context.pushReplacementNamed(
                        AppRoute.profileScreen.name,
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text("Password updated successfully"),
                        ),
                      );
                    } else {
                      context.pop();
                      context.pushReplacementNamed(
                        AppRoute.profileScreen.name,
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text("Failed to update password"),
                        ),
                      );
                    }
                  });
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text("Failed to update password"),
                    ),
                  );
                }
              },
              label: Text(
                S.of(context).kChangePassword,
                style: TextSize.l.mediumStyle.copyWith(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
