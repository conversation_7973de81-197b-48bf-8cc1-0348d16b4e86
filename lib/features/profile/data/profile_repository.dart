import 'dart:async';
import 'package:ajmal_now_doctor/features/profile/data/profile_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANProfileRepository {
  final Ref ref;

  ANProfileRepository({required this.ref});

  Future<String> getBiography() async {
    try {
      return await ref.read(httpProfileRepositoryProvider).getBiography();
    } catch (e) {
      return "";
    }
  }

  Future<bool> updatePassword(
      {required String currentPassword,
      required String newPassword,
      required String confirmNewPassword}) async {
    try {
      return await ref.read(httpProfileRepositoryProvider).updatePassword(
            currentPassword: currentPassword,
            newPassword: newPassword,
            confirmNewPassword: confirmNewPassword,
          );
    } catch (e) {
      return false;
    }
  }

  Future<bool> updateProviderData(
      {String? firstName,
      String? middleName,
      String? lastName,
      String? fullName,
      String? phoneNumber,
      String? gender,
      String? biography,
      DateTime? birthDate}) async {
    try {
      return await ref.read(httpProfileRepositoryProvider).updateProviderData(
            firstName: firstName,
            middleName: middleName,
            lastName: lastName,
            fullName: fullName,
            phoneNumber: phoneNumber,
            gender: gender,
            birthDate: birthDate,
            biography: biography,
          );
    } catch (e) {
      return false;
    }
  }
}
