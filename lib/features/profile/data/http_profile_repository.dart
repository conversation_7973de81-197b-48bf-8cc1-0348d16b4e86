import 'dart:convert';
import 'package:ajmal_now_doctor/common/domain/image/image.dart';
import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:http/http.dart' as http;
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../config.dart';

class ANHttpProfileRepository {
  final Ref ref;
  String? providerId;

  ANHttpProfileRepository({required this.ref});

  Future<String> getBiography() async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      final url = Uri.parse(
          '${Config.baseUrl}providers?where[user][equals]=${ref.read(authRepositoryProvider).currentUser!.id}&locale=$locale');
      final response = await http.get(url, headers: {
        'Content-Type': 'application/json',
      });
      Map<String, dynamic> provider = jsonDecode(response.body)['docs'][0];
      print(provider);
      providerId = provider['id'];
      return provider['biography'];
    } catch (e) {
      return "";
    }
  }

  Future<bool> updateBiography(String? biograpy) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      final url = Uri.parse('${Config.baseUrl}providers/$providerId?locale=$locale');
      print(url);
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'biography': biograpy,
        }),
      );
      print("response: ${response.body}");
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<bool> updatePassword(
      {required String currentPassword,
      required String newPassword,
      required String confirmNewPassword}) async {
    ANUserModel user = ref.read(authRepositoryProvider).currentUser!;
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      final url = Uri.parse('${Config.baseUrl}users/login?locale=$locale');
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': user.email,
          'password': currentPassword,
        }),
      );

      if (response.statusCode == 200) {
        if (newPassword != confirmNewPassword) {
          return false;
        }
        final url = Uri.parse('${Config.baseUrl}users/${user.id}?locale=$locale');
        final response = await http.patch(
          url,
          headers: {
            'Content-Type': 'application/json',
          },
          body: jsonEncode({
            'password': newPassword,
          }),
        );
        return response.statusCode == 200;
      }
    } catch (e) {
      return false;
    }
    return false;
  }

  Future<bool> updateProviderData(
      {String? firstName,
      String? middleName,
      String? lastName,
      String? fullName,
      String? phoneNumber,
      String? gender,
      String? biography,
      DateTime? birthDate}) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      final url = Uri.parse(
          '${Config.baseUrl}users/${ref.read(authRepositoryProvider).currentUser!.id}?locale=$locale');
      final response = await http.patch(url,
          headers: {
            'Content-Type': 'application/json',
          },
          body: jsonEncode({
            'first_name': firstName,
            'middle_name': middleName,
            'last_name': lastName,
            'full_name': fullName,
            'phone': phoneNumber,
            'gender': gender?.split(".")[0] == "male" ? "male" : "female",
            'birth_date': birthDate?.toIso8601String(),
          }));
      Map<String, dynamic> userMap = jsonDecode(response.body)["doc"];
      ANUserModel user = ANUserModel(
        id: userMap["id"],
        firstName: userMap["first_name"],
        fullName: userMap["full_name"],
        lastName: userMap["last_name"],
        email: userMap["email"],
        phoneNumber: userMap["phone"],
        gender: (userMap["gender"] == "male")
            ? Gender.male
            : userMap["gender"] == "female"
                ? Gender.female
                : Gender.other,
        role: userMap["roles"],
        middleName: userMap["middle_name"],
        profilePicture: userMap["profile_pic"] != null
            ? ANImageModel.fromJson(userMap["profile_pic"])
            : null,
        birthdate: userMap["birth_date"] != null
            ? DateTime.parse(userMap["birth_date"])
            : null,
      );
      bool success = response.statusCode == 200;
      if (biography != null) {
        success &= await updateBiography(biography);
      }
      ref.read(authRepositoryProvider).setUser(user);
      return success;
    } catch (e) {
      return false;
    }
  }
}
