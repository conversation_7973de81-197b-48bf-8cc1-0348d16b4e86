import 'dart:convert';
import 'package:ajmal_now_doctor/features/statistics/domain/certificates/certificates.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/experience/experience.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/providers/providers.dart';
import 'package:http/http.dart' as http;
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../config.dart';

class ANBackgroundRepository {
  final Ref ref;
  String? providerId;

  ANBackgroundRepository({required this.ref});

  Future<ANProviderModel?> getProviderData() async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      final url = Uri.parse(
          '${Config.baseUrl}providers?where[user][equals]=${ref.read(authRepositoryProvider).currentUser!.id}&locale=$locale');
      final response = await http.get(url, headers: {
        'Content-Type': 'application/json',
      });
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['docs'] != null && data['docs'].isNotEmpty) {
          Map<String, dynamic> provider = data['docs'][0];
          providerId = provider['id'];
          return ANProviderModel.fromJson(provider);
        }
      }
      return null;
    } catch (e) {
      print('Error getting provider data: $e');
      return null;
    }
  }

  Future<bool> addCertificate(ANCertificatesModel certificate) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      // Get current provider data
      final provider = await getProviderData();
      if (provider == null) return false;
      
      // Create new certificate data
      final newCertificate = {
        'certificateName': certificate.certificateName,
        'issuingInstitution': certificate.issuingInstitution,
        'issueDate': certificate.issueDate?.toIso8601String(),
      };
      
      // Add to existing certificates array
      final updatedCertificates = [...provider.certificates.map((cert) => {
        'certificateName': cert.certificateName,
        'issuingInstitution': cert.issuingInstitution,
        'issueDate': cert.issueDate?.toIso8601String(),
      }), newCertificate];
      
      // Update provider with new certificates array
      final url = Uri.parse('${Config.baseUrl}providers/${provider.id}?locale=$locale');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'certificates': updatedCertificates,
        }),
      );
      
      print('Add certificate response: ${response.statusCode}');
      print('Add certificate body: ${response.body}');
      return response.statusCode == 200;
    } catch (e) {
      print('Error adding certificate: $e');
      return false;
    }
  }

  Future<bool> updateCertificate(ANCertificatesModel certificate) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      // Get current provider data
      final provider = await getProviderData();
      if (provider == null) return false;
      
      // Find and update the certificate in the array
      final updatedCertificates = provider.certificates.map((cert) {
        if (cert.id == certificate.id) {
          return {
            'certificateName': certificate.certificateName,
            'issuingInstitution': certificate.issuingInstitution,
            'issueDate': certificate.issueDate?.toIso8601String(),
          };
        }
        return {
          'certificateName': cert.certificateName,
          'issuingInstitution': cert.issuingInstitution,
          'issueDate': cert.issueDate?.toIso8601String(),
        };
      }).toList();
      
      // Update provider
      final url = Uri.parse('${Config.baseUrl}providers/${provider.id}?locale=$locale');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'certificates': updatedCertificates,
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error updating certificate: $e');
      return false;
    }
  }

  Future<bool> deleteCertificate(String certificateId) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      // Get current provider data
      final provider = await getProviderData();
      if (provider == null) return false;
      
      // Remove certificate from array
      final updatedCertificates = provider.certificates
          .where((cert) => cert.id != certificateId)
          .map((cert) => {
                'certificateName': cert.certificateName,
                'issuingInstitution': cert.issuingInstitution,
                'issueDate': cert.issueDate?.toIso8601String(),
              })
          .toList();
      
      // Update provider
      final url = Uri.parse('${Config.baseUrl}providers/${provider.id}?locale=$locale');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'certificates': updatedCertificates,
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting certificate: $e');
      return false;
    }
  }

  Future<bool> addExperience(ANExperienceModel experience) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      // Get current provider data
      final provider = await getProviderData();
      if (provider == null) return false;
      
      // Create new experience data
      final newExperience = {
        'jobPosition': experience.jobPosition,
        'institution': experience.institution,
        'startDate': experience.startDate?.toIso8601String(),
        'currentlyWorking': experience.currentlyWorking ?? false,
        'endDate': experience.endDate?.toIso8601String(),
      };
      
      // Add to existing experience array
      final updatedExperience = [...provider.experience.map((exp) => {
        'jobPosition': exp.jobPosition,
        'institution': exp.institution,
        'startDate': exp.startDate?.toIso8601String(),
        'currentlyWorking': exp.currentlyWorking ?? false,
        'endDate': exp.endDate?.toIso8601String(),
      }), newExperience];
      
      // Update provider with new experience array
      final url = Uri.parse('${Config.baseUrl}providers/${provider.id}?locale=$locale');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'experience': updatedExperience,
        }),
      );
      
      print('Add experience response: ${response.statusCode}');
      print('Add experience body: ${response.body}');
      return response.statusCode == 200;
    } catch (e) {
      print('Error adding experience: $e');
      return false;
    }
  }

  Future<bool> updateExperience(ANExperienceModel experience) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      // Get current provider data
      final provider = await getProviderData();
      if (provider == null) return false;
      
      // Find and update the experience in the array
      final updatedExperience = provider.experience.map((exp) {
        if (exp.id == experience.id) {
          return {
            'jobPosition': experience.jobPosition,
            'institution': experience.institution,
            'startDate': experience.startDate?.toIso8601String(),
            'currentlyWorking': experience.currentlyWorking ?? false,
            'endDate': experience.endDate?.toIso8601String(),
          };
        }
        return {
          'jobPosition': exp.jobPosition,
          'institution': exp.institution,
          'startDate': exp.startDate?.toIso8601String(),
          'currentlyWorking': exp.currentlyWorking ?? false,
          'endDate': exp.endDate?.toIso8601String(),
        };
      }).toList();
      
      // Update provider
      final url = Uri.parse('${Config.baseUrl}providers/${provider.id}?locale=$locale');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'experience': updatedExperience,
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error updating experience: $e');
      return false;
    }
  }

  Future<bool> deleteExperience(String experienceId) async {
    try {
      final languageController = ref.read(languageProvider);
      final locale = languageController.lang?.languageCode ?? 'en';
      
      // Get current provider data
      final provider = await getProviderData();
      if (provider == null) return false;
      
      // Remove experience from array
      final updatedExperience = provider.experience
          .where((exp) => exp.id != experienceId)
          .map((exp) => {
                'jobPosition': exp.jobPosition,
                'institution': exp.institution,
                'startDate': exp.startDate?.toIso8601String(),
                'currentlyWorking': exp.currentlyWorking ?? false,
                'endDate': exp.endDate?.toIso8601String(),
              })
          .toList();
      
      // Update provider
      final url = Uri.parse('${Config.baseUrl}providers/${provider.id}?locale=$locale');
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'experience': updatedExperience,
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting experience: $e');
      return false;
    }
  }
} 