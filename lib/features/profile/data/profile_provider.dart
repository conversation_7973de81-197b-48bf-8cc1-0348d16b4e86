import 'package:ajmal_now_doctor/features/profile/data/profile_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'http_profile_repository.dart';

final httpProfileRepositoryProvider = Provider<ANHttpProfileRepository>(
    (ref) => ANHttpProfileRepository(ref: ref));

final profileRepositoryProvider = Provider<ANProfileRepository>((ref) {
  final profile = ANProfileRepository(ref: ref);
  return profile;
});
