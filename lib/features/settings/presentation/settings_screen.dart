import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/settings/presentation/language_card.dart';
import 'package:ajmal_now_doctor/features/settings/presentation/notification_switch.dart';
import 'package:ajmal_now_doctor/features/settings/presentation/settings_expansion_card.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ANSettingsScreen extends ConsumerStatefulWidget {
  const ANSettingsScreen({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _ANSettingsScreenState();
}

class _ANSettingsScreenState extends ConsumerState<ANSettingsScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final languageController = ref.watch(languageProvider);

    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableBottomNavBar: false,
        disableAppbar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      appbar: ANMainScreenAppbar(
        scaffoldKey: _scaffoldKey,
        centerTitle: true,
        title: Text(
          S.of(context).kSettings,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            gapHXXXL,
            // ANSettingsExpansionTile(
            //   label: S.of(context).kNotificationSettings,
            //   children: [
            //     _buildAppointmentNotification(),
            //     gapHXXL,
            //     Divider(
            //       thickness: 1.0,
            //       color: Colors.grey.shade300,
            //     ),
            //     gapHXXL,
            //     _buildAppointmentCancellation(),
            //   ],
            // ),
            // gapHXXL,
            // ANSettingsExpansionTile(
            //   label: 'Change Password'.hardcoded,
            //   children: [],
            // ),
            // gapHXXL,
            ANSettingsExpansionTile(
              label: S.of(context).kAppLanguage,
              children: [
                Row(
                  children: [
                    ANLanguageCard(
                        language: 'عربي'.hardcoded,
                        onTap: () async =>
                            await languageController.switchLang(langCode: 'ar'),
                        isSelected:
                            languageController.lang!.languageCode == 'ar'),
                    gapWXL,
                    ANLanguageCard(
                        language: 'English'.hardcoded,
                        onTap: () async =>
                            await languageController.switchLang(langCode: 'en'),
                        isSelected:
                            languageController.lang!.languageCode == 'en'),
                  ],
                )
              ],
            ),
            // gapHXXL,
            // ANSettingsExpansionTile(
            //   label: S.of(context).kRateTheApp,
            //   children: const [],
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentNotification() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context).kAppointmentNotifications,
          style: TextSize.xs.mediumStyle,
        ),
        gapHS,
        ANNotificationSwitch(
          label: S.of(context).k1HourBefore,
          onChanged: (value) {},
          value: false,
        ),
        gapHR,
        ANNotificationSwitch(
          label: S.of(context).k6HoursBefore,
          onChanged: (value) {},
          value: true,
        ),
        gapHR,
        ANNotificationSwitch(
          label: S.of(context).k1DayBefore,
          onChanged: (value) {},
          value: false,
        ),
      ],
    );
  }

  Widget _buildAppointmentCancellation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context).kAppointmentCancellation,
          style: TextSize.xs.mediumStyle,
        ),
        gapHS,
        ANNotificationSwitch(
          label: S.of(context).kSendAppNotification,
          onChanged: (value) {},
          value: true,
        ),
        gapHR,
        ANNotificationSwitch(
          label: S.of(context).kSendAnSms,
          onChanged: (value) {},
          value: false,
        ),
        gapHR,
        ANNotificationSwitch(
          label: S.of(context).kSendAnEmail,
          onChanged: (value) {},
          value: false,
        ),
      ],
    );
  }
}
