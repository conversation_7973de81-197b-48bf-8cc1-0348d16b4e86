import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class ANLanguageCard extends StatelessWidget {
  final String language;
  bool isSelected;
  final Function() onTap;

  ANLanguageCard(
      {Key? key,
      required this.language,
      required this.onTap,
      required this.isSelected})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: ANElevatedButton(
        onPressed: () => onTap.call(),
        backgroundColor: isSelected ? AppColors.primaryColor : Colors.white,
        child: Text(
          language,
          style: TextSize.xs.mediumStyle.copyWith(
              color: isSelected ? Colors.white : AppColors.primaryColor),
        ),
      ),
    );
  }
}
