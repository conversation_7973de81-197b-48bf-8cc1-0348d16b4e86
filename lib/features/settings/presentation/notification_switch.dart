import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:flutter/cupertino.dart';

// ignore: must_be_immutable
class ANNotificationSwitch extends StatelessWidget {
  final String label;
  final Function(bool) onChanged;
  bool value;
  ANNotificationSwitch(
      {Key? key,
      required this.label,
      required this.onChanged,
      required this.value})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 14.0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Transform.scale(
              scale: 0.65,
              child: CupertinoSwitch(
                value: value,
                onChanged: onChanged,
                activeColor: AppColors.primaryColor,
              )),
        ],
      ),
    );
  }
}
