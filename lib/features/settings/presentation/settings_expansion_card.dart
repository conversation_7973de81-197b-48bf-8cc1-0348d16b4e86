import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:flutter/material.dart';

class ANSettingsExpansionTile extends StatefulWidget {
  final String label;
  final List<Widget> children;
  const ANSettingsExpansionTile(
      {Key? key, required this.label, required this.children})
      : super(key: key);

  @override
  State<ANSettingsExpansionTile> createState() =>
      _ANSettingsExpansionTileState();
}

class _ANSettingsExpansionTileState extends State<ANSettingsExpansionTile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration:
          BoxDecoration(color: Colors.white, borderRadius: circularBorderXXS),
      child: ClipRRect(
        borderRadius: circularBorderXXS,
        child: Theme(
          data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
          child: ExpansionTile(
            collapsedBackgroundColor: Colors.white,
            backgroundColor: Colors.white,
            iconColor: AppColors.primaryColor,
            collapsedIconColor: AppColors.primaryColor,
            title: Text(
              widget.label,
              style: TextSize.s.semiBoldStyle
                  .copyWith(color: AppColors.primaryColor),
            ),
            expandedCrossAxisAlignment: CrossAxisAlignment.start,
            // expandedAlignment: Alignment.centerLeft,
            childrenPadding: const EdgeInsets.symmetric(horizontal: 22.0),
            children: [
              Divider(
                thickness: 1.0,
                color: Colors.grey.shade300,
              ),
              gapHL,
              ...widget.children,
              gapHXXL
            ],
          ),
        ),
      ),
    );
  }
}
