{"@@locale": "en", "kUnknown": "Unknown", "UnknownError": "Unknown Error Occurred", "kConfirm": "Confirm", "kCancel": "Cancel", "kYes": "Yes", "kNo": "No", "kOk": "Ok", "kAgree": "I Agree", "kDisagree": "I Disagree", "kUpgradeNow": "Upgrade Now", "kUnsupportedVersion": "Unsupported App Version", "kUpgradeRequiredMessage": "Please download the latest version to continue using the application", "kConfirmPasteDetails": "Are you sure you want to paste ", "kConfirmPaste": "Confirm Paste", "kAllRightsReserved": "All rights reserved", "kVersion": "version", "kSupport": "Support", "kPrivacyPolicy": "Privacy policy", "kHome": "Home", "kAccount": "Account", "kCalender": "<PERSON><PERSON>", "kAppointments": "Appointments", "kSettings": "Settings", "kContactSupport": "Contact Support", "kAbout": "About", "kLogout": "Logout", "kYouWillExitTheApp": "You will exit the app", "kProfile": "Profile", "kThereIsSomethingWrong": "There's something wrong", "kHelloThere": "Hello there", "kYouAreUsingTheAppAsAGuestPleaseRegisterOrLoginToContinue": "You're using the app as a guest,\nPlease register or login to continue.", "kLogin": "<PERSON><PERSON>", "kOrLoginWith": "Or Login with", "kRegister": "Register", "kOrRegisterWith": "Or Register with", "kNoDataToDisplay": "No Data to display", "kMale": "Male", "kFemale": "Female", "kForgetPassword": "Forgot Password?", "kEnterYourEmail": "Enter your email", "kEnterYourPassword": "Enter your password", "kSendCode": "Send Code", "kRememberPassword": "Remember password", "kYourSessionHasExpiredPleaseLoginAgain": "Your session has expired please login again", "kSkip": "<PERSON><PERSON>", "kWelcomeBackGladToSeeYouAgain": "Welcome back! Glad to see you again", "kEnterYourPhoneNumber": "Enter your phone number", "kDoNotHaveAnAccount": "Don't have an account?", "kRegisterNow": "Register Now", "kWelcomeToAjmalNowRegisterToGetStarted": "Welcome to AjmalNow! Register to get started", "kFirstName": "First name", "kMiddleName": "Middle name", "kLastName": "Last name", "kPhoneNumber": "Phone number", "kBirthDate": "Birth date", "kEmail": "Email", "kAlreadyHaveAnAccount": "Already have an account?", "kLoginNow": "Login Now", "kCreateNewPassword": "Create new password", "kYourNewPasswordMustBeUniqueFromThosePreviouslyUsed": "Your new password must be unique from those previously used", "kNewPassword": "New password", "kConfirmPassword": "Confirm password", "kResetPassword": "Reset Password", "kPasswordChanged": "Password changed", "kYourPasswordHasBeenChangedSuccessfully": "Your password has been\nchanged successfully", "kBackToLogin": "Back to Login", "kWelcomeToAjmalNowCompleteYourProfileToGetStarted": "Welcome to AjmalNow! Complete your profile to get started.", "kCompleteProfile": "Complete Profile", "kOtpVerification": "OTP Verification", "kEnterTheVerificationCodeWeJustSentOnYourPhone": "Enter the verification code we just sent on your phone.", "kVerify": "Verify", "kDidNotReceiveCode": "Didn't receive code?", "kResendAgain": "Resend again", "kUpcoming": "Upcoming", "kComplete": "Complete", "kCancelled": "Cancelled", "kBlocked": "Blocked", "kOnlinePayment": "Online Payment", "kAppointmentReservedSuccessfully": "Appointment reserved successfully", "kSomethingWentWrong": "Something went wrong", "kHowCanWeHelp": "How can we help?", "kPleaseFillInTheFormBelowOurCustomerServiceRepresentativesWillContactYouShortly": "Please fill in the form below. Our customer service representatives will contact you shortly.", "kMessage": "Message", "kTypeYourMessage": "Type your message...", "kFeelFreeToRateUsOnTheStoreAndLeaveACommentThere": "Feel free to rate us on the store\nand leave a comment there", "kSubmit": "Submit", "kYourInquiryHasBeenSubmittedSuccessfully": "Your inquiry has been submitted successfully", "kCreatedAt": "Created at: ", "kUpdatedAt": "Updated at: ", "kSupportAgent": "Support Agent", "kPaymentFailed": "Payment\nFailed", "kNext": "Next", "kGetStarted": "Get started", "kAgeGroup": "Age group", "kGender": "Gender", "kAccountDetails": "Account Details", "kPickProfileImage": "Pick profile image", "kPickFromGallery": "Pick from gallery", "kCaptureFromCamera": "Capture from camera", "kYourProfileHaveBeenUpdatedSuccessfully": "Your profile have been updated successfully", "kSave": "Save", "kAll": "All", "kSapsAndWellnessCenters": "Spas and Wellness Centers", "kMakeupProfessionals": "Makeup Professionals", "kDermatologists": "Dermatologists", "kPlasticSurgeons": "Plastic Surgeons", "kMore": "More...", "kWelcomeBack": "Welcome back", "kTypeHereToSearch": "Type here to search...", "kPleaseTypeInSomethingToSearch": "Please type in something to search", "kWhatAreYouLookingFor": "What are you looking for?", "kFilter": "Filter", "kSortBy": "Sort by", "kHint": "Hint", "kNearest": "Nearest", "kCheapest": "Cheapest", "kFarthest": "Farthest", "kSearchResults": "Search results", "kDisclaimer": "Disclaimer", "kAValueOfEgp10WillBeDeductedAsReservationFees": "A value of EGP 10 will be deducted as reservation fees.", "kReserve": "Reserve", "kAppointmentDetails": "Appointment Details", "kFacilityType": "Facility type", "kFacilityAddress": "Facility address", "kBookingId": "Booking ID", "kDate": "Date", "kTime": "Time", "kStatus": "Status", "kClientArrivedAt": "Client arrived at", "kClientCheckedInSuccessfully": "Client checked in successfully", "kCheckInClient": "Check in client", "kNoQrCodeDetected": "No Qr code detected", "kPleaseMakeSureTheQrCodeIsVisibleAndTryAgain": "Please make sure the Qr code is visible and try again", "kQrCodeScannedSuccessfully": "Qr code scanned successfully", "kYouWillBeRedirectedToThePreviousScreen": "You will be redirected to the previous screen", "kGallery": "Gallery", "kServiceAndPackages": "Service and Packages", "kAvailability": "Availability", "kEGP": "EGP", "kNotificationSettings": "Notification Settings", "kAppLanguage": "App Language", "kArabic": "عربي", "kEnglish": "English", "kRateTheApp": "Rate the App", "kAppointmentNotifications": "Appointment Notifications", "k1HourBefore": "1 hour before", "k6HoursBefore": "6 hours before", "k1DayBefore": "1 day before", "kAppointmentCancellation": "Appointment Cancellation", "kSendAppNotification": "Send app notification", "kSendAnSms": "Send an SMS", "kSendAnEmail": "Send an e-mail", "kNoWorriesPleaseEnterTheEmailAddressLinkedWithYourAccount": "No worries! Please enter the email address linked with your account", "kMyServices": "My services", "kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices": "You don't own any branches right now.\nPlease contact us to submit your branches.", "kContactUs": "Contact Us", "kSelectAServiceToViewItsDetails": "Select a branch to view its details", "kTimeSlotIsNowAvailable": "Time slot is now available", "kTimeSlotBlockedSuccessfully": "Time slot blocked successfully", "kTotalBookings": "Total bookings", "kBookingsLastWeek": "Bookings last week", "kBookingsThisWeek": "Bookings this week", "kCreateNewService": "Create New Service", "kServiceName": "Service Name", "kDescription": "Description", "kOffers": "Special Offers", "kSpecialOffers": "Special Offers", "kType": "Type", "kAvailableLocations": "Available Locations", "kOriginalPriceEGP": "Original Price (EGP)", "kMinPriceEGP": "<PERSON> (EGP)", "kMaxPriceEGP": "<PERSON> (EGP)", "kDiscountPercentage": "Discount Percentage", "kDiscountActive": "Discount Active", "kScheduleDay": "Schedule Day", "kSelectAtLeastOneDay": "Select at least one day", "kStartTime": "Start Time", "kEndTime": "End Time", "kDayBasedAvailability": "Day-Based Availability", "kSetAvailabilityForEachDay": "Set availability for each day of the week", "kService": "Service", "kConfigured": "Configured", "kDone": "Done", "kAvailable": "Available", "kUnavailable": "Unavailable", "kAddTimeSlot": "Add Time Slot", "kTimeSlots": "Time Slots", "kSetAvailabilityFor": "Set availability for {date}", "kMarkUnavailableFor": "Mark unavailable for {date}", "kPleaseAddAtLeastOneTimeSlot": "Please add at least one time slot", "kPleaseSelectAValidTimeSlot": "Please select a valid time slot", "kCreate": "Create", "kEditService": "Edit Service", "kSat": "Sat", "kSun": "Sun", "kMon": "Mon", "kTue": "<PERSON><PERSON>", "kWed": "Wed", "kThu": "<PERSON>hu", "kFri": "<PERSON><PERSON>", "kServiceCreatedSuccessfully": "Service created successfully", "kServiceUpdatedSuccessfully": "Service updated successfully", "kServiceDeletedSuccessfully": "Service deleted successfully", "kOwners": "Owners", "kServices": "Services", "kBranches": "Branches", "kCreateNewBranch": "Create New Branch", "kYouMustAddALogo": "You must add a logo", "kAtHomeVisitsAvailable": "At home visits available", "kYouMustAddAtLeastOneImage": "You must add at least one image", "kBranchName": "Clinic Name", "kBranchCreatedSuccessfully": "Branch created successfully", "kAddImage": "Add Image", "kIsThisAPackage": "Is this a package?", "kEditBranch": "Edit Branch", "kBranchUpdatedSuccessfully": "Branch updated successfully", "kBranchDeletedSuccessfully": "Branch deleted successfully", "kDeleteBranch": "Delete Branch", "kDeleteService": "Delete Service", "kPickALocation": "Pick a location", "kClinicName": "Clinic Name", "kClinicHotline": "Clinic Phone Number", "kClinicPhone": "Clinic Secondary Phone Number", "kCity": "City", "kDistrict": "District", "kYouMustPickALocation": "You must pick a location", "kCouldNotGetLocation": "Could not get location", "kMyBranches": "My Branches", "kNoBranches": "You don't have any branches.", "kThatsNotTheRightCode": "That's not the right code", "kThisFieldCantBeEmpty": "This field can't be empty", "kCurrentPassword": "Current Password", "kChangePassword": "Change Password", "kNoUpcomingAppointments": "No upcoming appointments", "kNoPastAppointments": "No past appointments", "kPast": "Past", "kSchedule": "Schedule", "kWeeklyHours": "Weekly hours", "kDateSpecificHours": "Date-specific hours", "kUseSameHoursForAllDays": "Use same hours for all days", "kSetAvailable": "Set as Available", "kSetUnavailable": "Set as Unavailable", "kAreYouSureAboutDeletingThisService": "Are you sure about deleting this service?", "kUpdateService": "Update Service", "kError": "Error", "kServiceUploadImageValidation": "Please upload an image for the service", "kPleaseEnterAtLeastOnePrice": "Please enter at least one price", "kMaxPriceMustBeGreaterThanMinPrice": "Max price must be greater than min price", "kCalendarScheduleInfo": "Date-Specific Scheduling", "kSelectDayToSetAvailabilityDescription": "Select a date from the calendar to set or modify availability and time slots. You can mark days as available with time slots or unavailable.", "kAvailableDays": "Available", "kUnavailableDays": "Unavailable", "kNotSetDays": "Not Set", "kPleaseSelectDateFirst": "Please select a date first", "kEditAvailability": "Edit Availability", "kSetAsAvailable": "Set as Available", "kEditUnavailability": "Edit Unavailability", "kSetAsUnavailable": "Set as Unavailable", "kAppointmentTime": "Appointment Time", "kSlotUpdateNote": "Note: After updating the Freezed models, run 'flutter pub run build_runner build --delete-conflicting-outputs' in both projects to update the generated code.", "kOpenMap": "Open Map", "kErrorOpeningMap": "Error opening map", "kSearchForLocation": "Search for location", "kNoLocationsFound": "No locations found", "kOpenWithMap": "Open with Map", "kLocationNotAvailable": "Location not available", "kContactOptions": "Contact Options", "kCallPhoneNumber": "Call", "kCopyPhoneNumber": "Copy Number", "kPhoneNumberCopied": "Phone number copied to clipboard", "kCouldNotCopyPhoneNumber": "Could not copy phone number", "kCouldNotMakePhoneCall": "Could not make phone call", "kShowingOptions": "Could not initiate call directly. Showing options...", "kWouldYouLikeToCallOrCopy": "Would you like to call or copy this number?"}