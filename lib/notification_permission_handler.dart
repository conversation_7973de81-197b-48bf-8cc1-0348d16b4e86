import 'dart:io';

import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class NotificationPermissionHandler {
  static Future<void> requestNotificationPermission() async {
    // For iOS
    final messaging = FirebaseMessaging.instance;

    try {
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false, // Set to true for quiet permissions on iOS
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted permission');
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        print('User granted provisional permission');
      } else {
        print('User declined permission');
      }

      // For Android
      if (Platform.isAndroid) {
        // Check if permission is already granted
        var status = await Permission.notification.status;
        if (status.isDenied) {
          // Request permission
          await Permission.notification.request();
        }

        // For Android 13 (API level 33) and above
        if (await Permission.notification.isDenied) {
          // Open app settings if permission is denied
          openAppSettings();
        }
      }
    } catch (e) {
      print('Error requesting notification permission: $e');
    }
  }
}
