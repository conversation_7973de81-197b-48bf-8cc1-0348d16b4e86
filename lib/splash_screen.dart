import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/constants/images.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/helpers/shared_preferences_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANSplashScreen extends ConsumerStatefulWidget {
  const ANSplashScreen({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _ANSplashScreenState();
}

class _ANSplashScreenState extends ConsumerState<ANSplashScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      Future.delayed(const Duration(microseconds: 200), () {
        final authController = ref.watch(authStateChangesProvider.stream);

        authController.listen((currentUser) {
          final isLoggedIn = currentUser != null;
          if (isLoggedIn) {
            GoRouter.of(context).pushReplacementNamed(AppRoute.homeScreen.name);
            // ref.read(authRepositoryProvider).validateUserToken().then((value) {
            //   if (value) {
            //     GoRouter.of(context)
            //         .pushReplacementNamed(AppRoute.homeScreen.name);
            //   } else {
            //     // Failed to re-login the user with new token, redirect to landing screen with a dialog stating that session has expired
            //     GoRouter.of(context).pushReplacementNamed(
            //         AppRoute.landingScreen.name,
            //         queryParams: {'sessionExpiration': true.toString()});
            //   }
            // });
          } else {
            final ANSharedPreferencesHelper sharedPreferences =
                ANSharedPreferencesHelper();
            sharedPreferences
                .get(
                    key: 'FIRST_TIME',
                    decoder: (value) {
                      return value as bool?;
                    })
                .then((value) {
              final bool? isFirstTime = value as bool?;
              if (isFirstTime != null && isFirstTime) {
                GoRouter.of(context).pushReplacementNamed(
                    AppRoute.landingScreen.name,
                    queryParameters: {'sessionExpiration': false.toString()});
              } else {
                sharedPreferences.storeData(
                  key: 'FIRST_TIME',
                  value: true,
                  encoder: (value) {
                    return value as bool;
                  },
                );
                if (GoRouter.of(context).routerDelegate.currentConfiguration.fullPath == '/') {
                  GoRouter.of(context)
                      .pushReplacementNamed(AppRoute.onboardingScreen.name);
                }
              }
            });
          }
        });

        // authController.whenData((value){
        //   print('Inside $value');
        //   final isLoggedIn = value != null;
        //   if (isLoggedIn) {
        //     GoRouter.of(context).replaceNamed(AppRoute.homeScreen.name);
        //   } else {
        //     if (GoRouter.of(context).location == '/') {
        //       GoRouter.of(context).replaceNamed(AppRoute.onboardingScreen.name);
        //     }
        //   }
        // });

        // final authRepository = ref.watch(authRepositoryProvider);
        //
        // final isLoggedIn = authRepository.currentUser != null;
        // if (isLoggedIn) {
        //   GoRouter.of(context).replaceNamed(AppRoute.homeScreen.name);
        // } else {
        //   if (GoRouter.of(context).location == '/') {
        //     GoRouter.of(context).replaceNamed(AppRoute.onboardingScreen.name);
        //   }
        // }
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 200.0,
              height: 200.0,
              child: Image.asset(Images.logoImage),
            ),
            gapHL,
            Text(
              'Ajmal Now Doctor'.hardcoded,
              style: GoogleFonts.philosopher(
                  textStyle: const TextStyle(
                      fontSize: 42.0, fontWeight: FontWeight.bold),
                  color: AppColors.primaryColor),
            )
          ],
        ),
      ),
    );
  }
}
