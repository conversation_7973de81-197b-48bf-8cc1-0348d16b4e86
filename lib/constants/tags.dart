class Tags {
  /// Model tags

  /// Network Codes
  // Codes
  static const int kSuccessCode = 200;
  static const int kSuccessCreationCode = 201;
  static const int kPendingCode = 202;
  static const int kDelayedSuccessCode = 203;
  static const int kErrorCode = 401;
  static const int kTransErrorCode = 422;
  static const int kNotFoundErrorCode = 404;
  static const int kServerErrorErrorCode = 500;

  /// API tags

  /// JSON tags
  // General tags
  static const String kStatusTag = 'status';
  static const String kErrorMessageTag = 'errors';
  static const String kMessageTag = 'message';
  static const String kDataTag = 'data';
  static const String kIdTag = 'id';
  static const String kAmountTag = 'amount';
  static const String kCreatedAtTag = 'created_at';

  // Header tags
  static const String kAuthTag = 'Authorization';
  static const String kLangTag = 'X-localization';

  // Check server tags
  static const String kVersionTag = 'version';
  static const String kPlatformTag = 'platform';
  static const String kUpgradeTag = 'upgrade';

  // Notifications tags
  static const String kNotificationTag = 'notification';
  static const String kNotificationIdTag = 'id';
  static const String kNotificationDataTag = 'data';
  static const String kNotificationTitleTag = 'title';
  static const String kNotificationBodyTag = 'body';
  static const String kNotificationMessageTag = 'message';
  static const String kNotificationDataTypeTag = 'n_type';
  static const String kNotificationCustomDataTag = 'n_data';
  static const String kNotificationSeenTag = 'isSeen';
  static const String kNotificationDateTag = 'date';
  static const String kNotificationCount = 'count';

  /// Shared preferences tags
  static const String kFirstTimeLogin = 'first_time';
}
