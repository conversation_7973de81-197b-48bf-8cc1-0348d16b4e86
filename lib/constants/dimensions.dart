import 'package:flutter/material.dart';

class Paddings {
  static const double xxs = 4.0;
  static const double xs = 6.0;
  static const double s = 8.0;
  static const double r = 10.0;
  static const double l = 12.0;
  static const double xl = 14.0;
  static const double xxl = 20.0;
  static const double xxxl = 30.0;

  static const double screenPaddingHorizontally = 20.0;
  static const double screenPaddingVertically = 20.0;
}

const EdgeInsets screenPadding = EdgeInsets.symmetric(
    horizontal: Paddings.screenPaddingHorizontally,
    vertical: Paddings.screenPaddingVertically);

const EdgeInsets allRoundPaddingXXS = EdgeInsets.all(Paddings.xxs);
const EdgeInsets allRoundPaddingXS = EdgeInsets.all(Paddings.xs);
const EdgeInsets allRoundPaddingS = EdgeInsets.all(Paddings.s);
const EdgeInsets allRoundPaddingR = EdgeInsets.all(Paddings.r);
const EdgeInsets allRoundPaddingL = EdgeInsets.all(Paddings.l);
const EdgeInsets allRoundPaddingXL = EdgeInsets.all(Paddings.xl);
const EdgeInsets allRoundPaddingXXL = EdgeInsets.all(Paddings.xxl);
const EdgeInsets allRoundPaddingXXXL = EdgeInsets.all(Paddings.xxxl);

const gapWXXS = SizedBox(width: Paddings.xxs);
const gapWXS = SizedBox(width: Paddings.xs);
const gapWS = SizedBox(width: Paddings.s);
const gapWR = SizedBox(width: Paddings.r);
const gapWL = SizedBox(width: Paddings.l);
const gapWM = SizedBox(width: 16.0);
const gapWXL = SizedBox(width: Paddings.xl);
const gapWXXL = SizedBox(width: Paddings.xxl);
const gapWXXXL = SizedBox(width: Paddings.xxxl);

const gapHXXS = SizedBox(height: Paddings.xxs);
const gapHXS = SizedBox(height: Paddings.xs);
const gapHS = SizedBox(height: Paddings.s);
const gapHR = SizedBox(height: Paddings.r);
const gapHL = SizedBox(height: Paddings.l);
const gapHXL = SizedBox(height: Paddings.xl);
const gapHXXL = SizedBox(height: Paddings.xxl);
const gapHXXXL = SizedBox(height: Paddings.xxxl);

class Dimensions {
  static const double buttonHeight = 50.0;
  static const double buttonWidth = 320.0;

  static const double textFieldHeight = 50.0;
  static const double textFieldWidth = 320.0;
}

const elevatedButtonFixedSize =
    Size(Dimensions.buttonWidth, Dimensions.buttonHeight);

enum TextSize { xxs, xs, s, r, l, xl, xxr, xxl }

extension TextSizeOp on TextSize {
  double get size {
    switch (this) {
      case TextSize.xxs:
        return 10.0;
      case TextSize.xs:
        return 12.0;
      case TextSize.s:
        return 14.0;
      case TextSize.r:
        return 16.0;
      case TextSize.l:
        return 18.0;
      case TextSize.xl:
        return 22.0;
      case TextSize.xxr:
        return 28.0;
      case TextSize.xxl:
        return 30.0;
    }
  }

  TextStyle get thinStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w100);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w100);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w100);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w100);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w100);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w100);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w100);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w100);
    }
  }

  TextStyle get extraLightStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w200);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w200);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w200);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w200);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w200);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w200);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w200);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w200);
    }
  }

  TextStyle get lightStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w300);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w300);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w300);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w300);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w300);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w300);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w300);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w300);
    }
  }

  TextStyle get regularStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w400);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w400);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w400);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w400);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w400);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w400);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w400);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w400);
    }
  }

  TextStyle get mediumStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w500);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w500);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w500);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w500);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w500);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w500);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w500);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w500);
    }
  }

  TextStyle get semiBoldStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w600);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w600);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w600);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w600);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w600);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w600);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w600);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w600);
    }
  }

  TextStyle get boldStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w700);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w700);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w700);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w700);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w700);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w700);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w700);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w700);
    }
  }

  TextStyle get extraBoldStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w800);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w800);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w800);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w800);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w800);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w800);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w800);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w800);
    }
  }

  TextStyle get thickStyle {
    switch (this) {
      case TextSize.xxs:
        return TextStyle(
            fontSize: TextSize.xxs.size, fontWeight: FontWeight.w900);
      case TextSize.xs:
        return TextStyle(
            fontSize: TextSize.xs.size, fontWeight: FontWeight.w900);
      case TextSize.s:
        return TextStyle(
            fontSize: TextSize.s.size, fontWeight: FontWeight.w900);
      case TextSize.r:
        return TextStyle(
            fontSize: TextSize.r.size, fontWeight: FontWeight.w900);
      case TextSize.l:
        return TextStyle(
            fontSize: TextSize.l.size, fontWeight: FontWeight.w900);
      case TextSize.xl:
        return TextStyle(
            fontSize: TextSize.xl.size, fontWeight: FontWeight.w900);
      case TextSize.xxl:
        return TextStyle(
            fontSize: TextSize.xxl.size, fontWeight: FontWeight.w900);
      case TextSize.xxr:
        return TextStyle(
            fontSize: TextSize.xxr.size, fontWeight: FontWeight.w900);
    }
  }
}

class CornerRadius {
  static const double xxs = 10.0;
  static const double xs = 12.0;
  static const double s = 14.0;
  static const double r = 16.0;
  static const double l = 18.0;
  static const double xl = 22.0;
  static const double xxl = 32.0;
}

final BorderRadius circularBorderXXS = BorderRadius.circular(CornerRadius.xxs);
final BorderRadius circularBorderXS = BorderRadius.circular(CornerRadius.xs);
final BorderRadius circularBorderS = BorderRadius.circular(CornerRadius.s);
final BorderRadius circularBorderR = BorderRadius.circular(CornerRadius.r);
final BorderRadius circularBorderL = BorderRadius.circular(CornerRadius.l);
final BorderRadius circularBorderXL = BorderRadius.circular(CornerRadius.xl);
final BorderRadius circularBorderXXL = BorderRadius.circular(CornerRadius.xxl);

final BorderRadius buttonBorderRadius = BorderRadius.circular(CornerRadius.xs);
final BorderRadius textFieldBorderRadius =
    BorderRadius.circular(CornerRadius.xxs);
final BorderRadius socialMediaButtonBorderRadius = BorderRadius.circular(8.0);
final BorderRadius otpFieldBorderRadius = BorderRadius.circular(8.0);
