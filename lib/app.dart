import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/theme.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/helpers/language_helper.dart';
import 'package:ajmal_now_doctor/helpers/notification/notifications_helper.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_fgbg/flutter_fgbg.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MyApp extends ConsumerStatefulWidget {
  const MyApp({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  var subscription;

  @override
  void initState() {
    ref.read(notificationProvider).init();
    // When app comes in the foreground, reinitialize
    // the notifications from the notifications database
    subscription = FGBGEvents.instance.stream.listen((event) async {
      if (event == FGBGType.foreground) {
        await ref.read(notificationProvider).initNotifications();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final languageController = ref.watch(languageProvider);
    final goRouter = ref.watch(goRouterProvider);

    return Container(
      color: AppColors.backgroundColor,
      child: SafeArea(
        child: MediaQuery(
          data: const MediaQueryData(textScaleFactor: 1.0),
          child: MaterialApp.router(
            routerConfig: goRouter,
            restorationScopeId: 'app',
            debugShowCheckedModeBanner: false,
            onGenerateTitle: (BuildContext context) =>
                'Ajmal Now Doctor'.hardcoded,
            title: 'Ajmal Now Doctor'.hardcoded,
            locale: languageController.lang,
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              DefaultCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            theme: kLightTheme,
          ),
        ),
      ),
    );
  }
}
