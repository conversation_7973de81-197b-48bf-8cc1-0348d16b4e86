// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(date) => "Mark unavailable for ${date}";

  static String m1(date) => "Set availability for ${date}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "UnknownError":
            MessageLookupByLibrary.simpleMessage("Unknown Error Occurred"),
        "k1DayBefore": MessageLookupByLibrary.simpleMessage("1 day before"),
        "k1HourBefore": MessageLookupByLibrary.simpleMessage("1 hour before"),
        "k6HoursBefore": MessageLookupByLibrary.simpleMessage("6 hours before"),
        "kAValueOfEgp10WillBeDeductedAsReservationFees":
            MessageLookupByLibrary.simpleMessage(
                "A value of EGP 10 will be deducted as reservation fees."),
        "kAbout": MessageLookupByLibrary.simpleMessage("About"),
        "kAccount": MessageLookupByLibrary.simpleMessage("Account"),
        "kAccountDetails":
            MessageLookupByLibrary.simpleMessage("Account Details"),
        "kAddImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "kAddTimeSlot": MessageLookupByLibrary.simpleMessage("Add Time Slot"),
        "kAgeGroup": MessageLookupByLibrary.simpleMessage("Age group"),
        "kAgree": MessageLookupByLibrary.simpleMessage("I Agree"),
        "kAll": MessageLookupByLibrary.simpleMessage("All"),
        "kAllRightsReserved":
            MessageLookupByLibrary.simpleMessage("All rights reserved"),
        "kAlreadyHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Already have an account?"),
        "kAppLanguage": MessageLookupByLibrary.simpleMessage("App Language"),
        "kAppointmentCancellation":
            MessageLookupByLibrary.simpleMessage("Appointment Cancellation"),
        "kAppointmentDetails":
            MessageLookupByLibrary.simpleMessage("Appointment Details"),
        "kAppointmentNotifications":
            MessageLookupByLibrary.simpleMessage("Appointment Notifications"),
        "kAppointmentReservedSuccessfully":
            MessageLookupByLibrary.simpleMessage(
                "Appointment reserved successfully"),
        "kAppointmentTime":
            MessageLookupByLibrary.simpleMessage("Appointment Time"),
        "kAppointments": MessageLookupByLibrary.simpleMessage("Appointments"),
        "kArabic": MessageLookupByLibrary.simpleMessage("عربي"),
        "kAreYouSureAboutDeletingThisService":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure about deleting this service?"),
        "kAtHomeVisitsAvailable":
            MessageLookupByLibrary.simpleMessage("At home visits available"),
        "kAvailability": MessageLookupByLibrary.simpleMessage("Availability"),
        "kAvailable": MessageLookupByLibrary.simpleMessage("Available"),
        "kAvailableDays": MessageLookupByLibrary.simpleMessage("Available"),
        "kAvailableLocations":
            MessageLookupByLibrary.simpleMessage("Available Locations"),
        "kBackToLogin": MessageLookupByLibrary.simpleMessage("Back to Login"),
        "kBirthDate": MessageLookupByLibrary.simpleMessage("Birth date"),
        "kBlocked": MessageLookupByLibrary.simpleMessage("Blocked"),
        "kBookingId": MessageLookupByLibrary.simpleMessage("Booking ID"),
        "kBookingsLastWeek":
            MessageLookupByLibrary.simpleMessage("Bookings last week"),
        "kBookingsThisWeek":
            MessageLookupByLibrary.simpleMessage("Bookings this week"),
        "kBranchCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Branch created successfully"),
        "kBranchDeletedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Branch deleted successfully"),
        "kBranchName": MessageLookupByLibrary.simpleMessage("Clinic Name"),
        "kBranchUpdatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("Branch updated successfully"),
        "kBranches": MessageLookupByLibrary.simpleMessage("Branches"),
        "kCalendarScheduleInfo":
            MessageLookupByLibrary.simpleMessage("Date-Specific Scheduling"),
        "kCalender": MessageLookupByLibrary.simpleMessage("Calender"),
        "kCallPhoneNumber": MessageLookupByLibrary.simpleMessage("Call"),
        "kCancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "kCancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
        "kCaptureFromCamera":
            MessageLookupByLibrary.simpleMessage("Capture from camera"),
        "kChangePassword":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "kCheapest": MessageLookupByLibrary.simpleMessage("Cheapest"),
        "kCheckInClient":
            MessageLookupByLibrary.simpleMessage("Check in client"),
        "kCity": MessageLookupByLibrary.simpleMessage("City"),
        "kClientArrivedAt":
            MessageLookupByLibrary.simpleMessage("Client arrived at"),
        "kClientCheckedInSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Client checked in successfully"),
        "kClinicHotline":
            MessageLookupByLibrary.simpleMessage("Clinic Phone Number"),
        "kClinicName": MessageLookupByLibrary.simpleMessage("Clinic Name"),
        "kClinicPhone": MessageLookupByLibrary.simpleMessage(
            "Clinic Secondary Phone Number"),
        "kComplete": MessageLookupByLibrary.simpleMessage("Complete"),
        "kCompleteProfile":
            MessageLookupByLibrary.simpleMessage("Complete Profile"),
        "kConfigured": MessageLookupByLibrary.simpleMessage("Configured"),
        "kConfirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "kConfirmPassword":
            MessageLookupByLibrary.simpleMessage("Confirm password"),
        "kConfirmPaste": MessageLookupByLibrary.simpleMessage("Confirm Paste"),
        "kConfirmPasteDetails": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to paste "),
        "kContactOptions":
            MessageLookupByLibrary.simpleMessage("Contact Options"),
        "kContactSupport":
            MessageLookupByLibrary.simpleMessage("Contact Support"),
        "kContactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
        "kCopyPhoneNumber": MessageLookupByLibrary.simpleMessage("Copy Number"),
        "kCouldNotCopyPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Could not copy phone number"),
        "kCouldNotGetLocation":
            MessageLookupByLibrary.simpleMessage("Could not get location"),
        "kCouldNotMakePhoneCall":
            MessageLookupByLibrary.simpleMessage("Could not make phone call"),
        "kCreate": MessageLookupByLibrary.simpleMessage("Create"),
        "kCreateNewBranch":
            MessageLookupByLibrary.simpleMessage("Create New Branch"),
        "kCreateNewPassword":
            MessageLookupByLibrary.simpleMessage("Create new password"),
        "kCreateNewService":
            MessageLookupByLibrary.simpleMessage("Create New Service"),
        "kCreatedAt": MessageLookupByLibrary.simpleMessage("Created at: "),
        "kCurrentPassword":
            MessageLookupByLibrary.simpleMessage("Current Password"),
        "kDate": MessageLookupByLibrary.simpleMessage("Date"),
        "kDateSpecificHours":
            MessageLookupByLibrary.simpleMessage("Date-specific hours"),
        "kDayBasedAvailability":
            MessageLookupByLibrary.simpleMessage("Day-Based Availability"),
        "kDeleteBranch": MessageLookupByLibrary.simpleMessage("Delete Branch"),
        "kDeleteService":
            MessageLookupByLibrary.simpleMessage("Delete Service"),
        "kDermatologists":
            MessageLookupByLibrary.simpleMessage("Dermatologists"),
        "kDescription": MessageLookupByLibrary.simpleMessage("Description"),
        "kDidNotReceiveCode":
            MessageLookupByLibrary.simpleMessage("Didn\'t receive code?"),
        "kDisagree": MessageLookupByLibrary.simpleMessage("I Disagree"),
        "kDisclaimer": MessageLookupByLibrary.simpleMessage("Disclaimer"),
        "kDiscountActive":
            MessageLookupByLibrary.simpleMessage("Discount Active"),
        "kDiscountPercentage":
            MessageLookupByLibrary.simpleMessage("Discount Percentage"),
        "kDistrict": MessageLookupByLibrary.simpleMessage("District"),
        "kDoNotHaveAnAccount":
            MessageLookupByLibrary.simpleMessage("Don\'t have an account?"),
        "kDone": MessageLookupByLibrary.simpleMessage("Done"),
        "kEGP": MessageLookupByLibrary.simpleMessage("EGP"),
        "kEditAvailability":
            MessageLookupByLibrary.simpleMessage("Edit Availability"),
        "kEditBranch": MessageLookupByLibrary.simpleMessage("Edit Branch"),
        "kEditService": MessageLookupByLibrary.simpleMessage("Edit Service"),
        "kEditUnavailability":
            MessageLookupByLibrary.simpleMessage("Edit Unavailability"),
        "kEmail": MessageLookupByLibrary.simpleMessage("Email"),
        "kEndTime": MessageLookupByLibrary.simpleMessage("End Time"),
        "kEnglish": MessageLookupByLibrary.simpleMessage("English"),
        "kEnterTheVerificationCodeWeJustSentOnYourPhone":
            MessageLookupByLibrary.simpleMessage(
                "Enter the verification code we just sent on your phone."),
        "kEnterYourEmail":
            MessageLookupByLibrary.simpleMessage("Enter your email"),
        "kEnterYourPassword":
            MessageLookupByLibrary.simpleMessage("Enter your password"),
        "kEnterYourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter your phone number"),
        "kError": MessageLookupByLibrary.simpleMessage("Error"),
        "kErrorOpeningMap":
            MessageLookupByLibrary.simpleMessage("Error opening map"),
        "kFacilityAddress":
            MessageLookupByLibrary.simpleMessage("Facility address"),
        "kFacilityType": MessageLookupByLibrary.simpleMessage("Facility type"),
        "kFarthest": MessageLookupByLibrary.simpleMessage("Farthest"),
        "kFeelFreeToRateUsOnTheStoreAndLeaveACommentThere":
            MessageLookupByLibrary.simpleMessage(
                "Feel free to rate us on the store\nand leave a comment there"),
        "kFemale": MessageLookupByLibrary.simpleMessage("Female"),
        "kFilter": MessageLookupByLibrary.simpleMessage("Filter"),
        "kFirstName": MessageLookupByLibrary.simpleMessage("First name"),
        "kForgetPassword":
            MessageLookupByLibrary.simpleMessage("Forgot Password?"),
        "kFri": MessageLookupByLibrary.simpleMessage("Fri"),
        "kGallery": MessageLookupByLibrary.simpleMessage("Gallery"),
        "kGender": MessageLookupByLibrary.simpleMessage("Gender"),
        "kGetStarted": MessageLookupByLibrary.simpleMessage("Get started"),
        "kHelloThere": MessageLookupByLibrary.simpleMessage("Hello there"),
        "kHint": MessageLookupByLibrary.simpleMessage("Hint"),
        "kHome": MessageLookupByLibrary.simpleMessage("Home"),
        "kHowCanWeHelp":
            MessageLookupByLibrary.simpleMessage("How can we help?"),
        "kIsThisAPackage":
            MessageLookupByLibrary.simpleMessage("Is this a package?"),
        "kLastName": MessageLookupByLibrary.simpleMessage("Last name"),
        "kLocationNotAvailable":
            MessageLookupByLibrary.simpleMessage("Location not available"),
        "kLogin": MessageLookupByLibrary.simpleMessage("Login"),
        "kLoginNow": MessageLookupByLibrary.simpleMessage("Login Now"),
        "kLogout": MessageLookupByLibrary.simpleMessage("Logout"),
        "kMakeupProfessionals":
            MessageLookupByLibrary.simpleMessage("Makeup Professionals"),
        "kMale": MessageLookupByLibrary.simpleMessage("Male"),
        "kMarkUnavailableFor": m0,
        "kMaxPriceEGP": MessageLookupByLibrary.simpleMessage("Max Price (EGP)"),
        "kMaxPriceMustBeGreaterThanMinPrice":
            MessageLookupByLibrary.simpleMessage(
                "Max price must be greater than min price"),
        "kMessage": MessageLookupByLibrary.simpleMessage("Message"),
        "kMiddleName": MessageLookupByLibrary.simpleMessage("Middle name"),
        "kMinPriceEGP": MessageLookupByLibrary.simpleMessage("Min Price (EGP)"),
        "kMon": MessageLookupByLibrary.simpleMessage("Mon"),
        "kMore": MessageLookupByLibrary.simpleMessage("More..."),
        "kMyBranches": MessageLookupByLibrary.simpleMessage("My Branches"),
        "kMyServices": MessageLookupByLibrary.simpleMessage("My services"),
        "kNearest": MessageLookupByLibrary.simpleMessage("Nearest"),
        "kNewPassword": MessageLookupByLibrary.simpleMessage("New password"),
        "kNext": MessageLookupByLibrary.simpleMessage("Next"),
        "kNo": MessageLookupByLibrary.simpleMessage("No"),
        "kNoBranches": MessageLookupByLibrary.simpleMessage(
            "You don\'t have any branches."),
        "kNoDataToDisplay":
            MessageLookupByLibrary.simpleMessage("No Data to display"),
        "kNoLocationsFound":
            MessageLookupByLibrary.simpleMessage("No locations found"),
        "kNoPastAppointments":
            MessageLookupByLibrary.simpleMessage("No past appointments"),
        "kNoQrCodeDetected":
            MessageLookupByLibrary.simpleMessage("No Qr code detected"),
        "kNoUpcomingAppointments":
            MessageLookupByLibrary.simpleMessage("No upcoming appointments"),
        "kNoWorriesPleaseEnterTheEmailAddressLinkedWithYourAccount":
            MessageLookupByLibrary.simpleMessage(
                "No worries! Please enter the email address linked with your account"),
        "kNotSetDays": MessageLookupByLibrary.simpleMessage("Not Set"),
        "kNotificationSettings":
            MessageLookupByLibrary.simpleMessage("Notification Settings"),
        "kOffers": MessageLookupByLibrary.simpleMessage("Special Offers"),
        "kOk": MessageLookupByLibrary.simpleMessage("Ok"),
        "kOnlinePayment":
            MessageLookupByLibrary.simpleMessage("Online Payment"),
        "kOpenMap": MessageLookupByLibrary.simpleMessage("Open Map"),
        "kOpenWithMap": MessageLookupByLibrary.simpleMessage("Open with Map"),
        "kOrLoginWith": MessageLookupByLibrary.simpleMessage("Or Login with"),
        "kOrRegisterWith":
            MessageLookupByLibrary.simpleMessage("Or Register with"),
        "kOriginalPriceEGP":
            MessageLookupByLibrary.simpleMessage("Original Price (EGP)"),
        "kOtpVerification":
            MessageLookupByLibrary.simpleMessage("OTP Verification"),
        "kOwners": MessageLookupByLibrary.simpleMessage("Owners"),
        "kPasswordChanged":
            MessageLookupByLibrary.simpleMessage("Password changed"),
        "kPast": MessageLookupByLibrary.simpleMessage("Past"),
        "kPaymentFailed":
            MessageLookupByLibrary.simpleMessage("Payment\nFailed"),
        "kPhoneNumber": MessageLookupByLibrary.simpleMessage("Phone number"),
        "kPhoneNumberCopied": MessageLookupByLibrary.simpleMessage(
            "Phone number copied to clipboard"),
        "kPickALocation":
            MessageLookupByLibrary.simpleMessage("Pick a location"),
        "kPickFromGallery":
            MessageLookupByLibrary.simpleMessage("Pick from gallery"),
        "kPickProfileImage":
            MessageLookupByLibrary.simpleMessage("Pick profile image"),
        "kPlasticSurgeons":
            MessageLookupByLibrary.simpleMessage("Plastic Surgeons"),
        "kPleaseAddAtLeastOneTimeSlot": MessageLookupByLibrary.simpleMessage(
            "Please add at least one time slot"),
        "kPleaseEnterAtLeastOnePrice": MessageLookupByLibrary.simpleMessage(
            "Please enter at least one price"),
        "kPleaseFillInTheFormBelowOurCustomerServiceRepresentativesWillContactYouShortly":
            MessageLookupByLibrary.simpleMessage(
                "Please fill in the form below. Our customer service representatives will contact you shortly."),
        "kPleaseMakeSureTheQrCodeIsVisibleAndTryAgain":
            MessageLookupByLibrary.simpleMessage(
                "Please make sure the Qr code is visible and try again"),
        "kPleaseSelectAValidTimeSlot": MessageLookupByLibrary.simpleMessage(
            "Please select a valid time slot"),
        "kPleaseSelectDateFirst":
            MessageLookupByLibrary.simpleMessage("Please select a date first"),
        "kPleaseTypeInSomethingToSearch": MessageLookupByLibrary.simpleMessage(
            "Please type in something to search"),
        "kPrivacyPolicy":
            MessageLookupByLibrary.simpleMessage("Privacy policy"),
        "kProfile": MessageLookupByLibrary.simpleMessage("Profile"),
        "kQrCodeScannedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Qr code scanned successfully"),
        "kRateTheApp": MessageLookupByLibrary.simpleMessage("Rate the App"),
        "kRegister": MessageLookupByLibrary.simpleMessage("Register"),
        "kRegisterNow": MessageLookupByLibrary.simpleMessage("Register Now"),
        "kRememberPassword":
            MessageLookupByLibrary.simpleMessage("Remember password"),
        "kResendAgain": MessageLookupByLibrary.simpleMessage("Resend again"),
        "kReserve": MessageLookupByLibrary.simpleMessage("Reserve"),
        "kResetPassword":
            MessageLookupByLibrary.simpleMessage("Reset Password"),
        "kSapsAndWellnessCenters":
            MessageLookupByLibrary.simpleMessage("Spas and Wellness Centers"),
        "kSat": MessageLookupByLibrary.simpleMessage("Sat"),
        "kSave": MessageLookupByLibrary.simpleMessage("Save"),
        "kSchedule": MessageLookupByLibrary.simpleMessage("Schedule"),
        "kScheduleDay": MessageLookupByLibrary.simpleMessage("Schedule Day"),
        "kSearchForLocation":
            MessageLookupByLibrary.simpleMessage("Search for location"),
        "kSearchResults":
            MessageLookupByLibrary.simpleMessage("Search results"),
        "kSelectAServiceToViewItsDetails": MessageLookupByLibrary.simpleMessage(
            "Select a branch to view its details"),
        "kSelectAtLeastOneDay":
            MessageLookupByLibrary.simpleMessage("Select at least one day"),
        "kSelectDayToSetAvailabilityDescription":
            MessageLookupByLibrary.simpleMessage(
                "Select a date from the calendar to set or modify availability and time slots. You can mark days as available with time slots or unavailable."),
        "kSendAnEmail": MessageLookupByLibrary.simpleMessage("Send an e-mail"),
        "kSendAnSms": MessageLookupByLibrary.simpleMessage("Send an SMS"),
        "kSendAppNotification":
            MessageLookupByLibrary.simpleMessage("Send app notification"),
        "kSendCode": MessageLookupByLibrary.simpleMessage("Send Code"),
        "kService": MessageLookupByLibrary.simpleMessage("Service"),
        "kServiceAndPackages":
            MessageLookupByLibrary.simpleMessage("Service and Packages"),
        "kServiceCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Service created successfully"),
        "kServiceDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Service deleted successfully"),
        "kServiceName": MessageLookupByLibrary.simpleMessage("Service Name"),
        "kServiceUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Service updated successfully"),
        "kServiceUploadImageValidation": MessageLookupByLibrary.simpleMessage(
            "Please upload an image for the service"),
        "kServices": MessageLookupByLibrary.simpleMessage("Services"),
        "kSetAsAvailable":
            MessageLookupByLibrary.simpleMessage("Set as Available"),
        "kSetAsUnavailable":
            MessageLookupByLibrary.simpleMessage("Set as Unavailable"),
        "kSetAvailabilityFor": m1,
        "kSetAvailabilityForEachDay": MessageLookupByLibrary.simpleMessage(
            "Set availability for each day of the week"),
        "kSetAvailable":
            MessageLookupByLibrary.simpleMessage("Set as Available"),
        "kSetUnavailable":
            MessageLookupByLibrary.simpleMessage("Set as Unavailable"),
        "kSettings": MessageLookupByLibrary.simpleMessage("Settings"),
        "kShowingOptions": MessageLookupByLibrary.simpleMessage(
            "Could not initiate call directly. Showing options..."),
        "kSkip": MessageLookupByLibrary.simpleMessage("Skip"),
        "kSlotUpdateNote": MessageLookupByLibrary.simpleMessage(
            "Note: After updating the Freezed models, run \'flutter pub run build_runner build --delete-conflicting-outputs\' in both projects to update the generated code."),
        "kSomethingWentWrong":
            MessageLookupByLibrary.simpleMessage("Something went wrong"),
        "kSortBy": MessageLookupByLibrary.simpleMessage("Sort by"),
        "kSpecialOffers":
            MessageLookupByLibrary.simpleMessage("Special Offers"),
        "kStartTime": MessageLookupByLibrary.simpleMessage("Start Time"),
        "kStatus": MessageLookupByLibrary.simpleMessage("Status"),
        "kSubmit": MessageLookupByLibrary.simpleMessage("Submit"),
        "kSun": MessageLookupByLibrary.simpleMessage("Sun"),
        "kSupport": MessageLookupByLibrary.simpleMessage("Support"),
        "kSupportAgent": MessageLookupByLibrary.simpleMessage("Support Agent"),
        "kThatsNotTheRightCode":
            MessageLookupByLibrary.simpleMessage("That\'s not the right code"),
        "kThereIsSomethingWrong":
            MessageLookupByLibrary.simpleMessage("There\'s something wrong"),
        "kThisFieldCantBeEmpty":
            MessageLookupByLibrary.simpleMessage("This field can\'t be empty"),
        "kThu": MessageLookupByLibrary.simpleMessage("Thu"),
        "kTime": MessageLookupByLibrary.simpleMessage("Time"),
        "kTimeSlotBlockedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Time slot blocked successfully"),
        "kTimeSlotIsNowAvailable":
            MessageLookupByLibrary.simpleMessage("Time slot is now available"),
        "kTimeSlots": MessageLookupByLibrary.simpleMessage("Time Slots"),
        "kTotalBookings":
            MessageLookupByLibrary.simpleMessage("Total bookings"),
        "kTue": MessageLookupByLibrary.simpleMessage("Tue"),
        "kType": MessageLookupByLibrary.simpleMessage("Type"),
        "kTypeHereToSearch":
            MessageLookupByLibrary.simpleMessage("Type here to search..."),
        "kTypeYourMessage":
            MessageLookupByLibrary.simpleMessage("Type your message..."),
        "kUnavailable": MessageLookupByLibrary.simpleMessage("Unavailable"),
        "kUnavailableDays": MessageLookupByLibrary.simpleMessage("Unavailable"),
        "kUnknown": MessageLookupByLibrary.simpleMessage("Unknown"),
        "kUnsupportedVersion":
            MessageLookupByLibrary.simpleMessage("Unsupported App Version"),
        "kUpcoming": MessageLookupByLibrary.simpleMessage("Upcoming"),
        "kUpdateService":
            MessageLookupByLibrary.simpleMessage("Update Service"),
        "kUpdatedAt": MessageLookupByLibrary.simpleMessage("Updated at: "),
        "kUpgradeNow": MessageLookupByLibrary.simpleMessage("Upgrade Now"),
        "kUpgradeRequiredMessage": MessageLookupByLibrary.simpleMessage(
            "Please download the latest version to continue using the application"),
        "kUseSameHoursForAllDays":
            MessageLookupByLibrary.simpleMessage("Use same hours for all days"),
        "kVerify": MessageLookupByLibrary.simpleMessage("Verify"),
        "kVersion": MessageLookupByLibrary.simpleMessage("version"),
        "kWed": MessageLookupByLibrary.simpleMessage("Wed"),
        "kWeeklyHours": MessageLookupByLibrary.simpleMessage("Weekly hours"),
        "kWelcomeBack": MessageLookupByLibrary.simpleMessage("Welcome back"),
        "kWelcomeBackGladToSeeYouAgain": MessageLookupByLibrary.simpleMessage(
            "Welcome back! Glad to see you again"),
        "kWelcomeToAjmalNowCompleteYourProfileToGetStarted":
            MessageLookupByLibrary.simpleMessage(
                "Welcome to AjmalNow! Complete your profile to get started."),
        "kWelcomeToAjmalNowRegisterToGetStarted":
            MessageLookupByLibrary.simpleMessage(
                "Welcome to AjmalNow! Register to get started"),
        "kWhatAreYouLookingFor":
            MessageLookupByLibrary.simpleMessage("What are you looking for?"),
        "kWouldYouLikeToCallOrCopy": MessageLookupByLibrary.simpleMessage(
            "Would you like to call or copy this number?"),
        "kYes": MessageLookupByLibrary.simpleMessage("Yes"),
        "kYouAreUsingTheAppAsAGuestPleaseRegisterOrLoginToContinue":
            MessageLookupByLibrary.simpleMessage(
                "You\'re using the app as a guest,\nPlease register or login to continue."),
        "kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices":
            MessageLookupByLibrary.simpleMessage(
                "You don\'t own any branches right now.\nPlease contact us to submit your branches."),
        "kYouMustAddALogo":
            MessageLookupByLibrary.simpleMessage("You must add a logo"),
        "kYouMustAddAtLeastOneImage": MessageLookupByLibrary.simpleMessage(
            "You must add at least one image"),
        "kYouMustPickALocation":
            MessageLookupByLibrary.simpleMessage("You must pick a location"),
        "kYouWillBeRedirectedToThePreviousScreen":
            MessageLookupByLibrary.simpleMessage(
                "You will be redirected to the previous screen"),
        "kYouWillExitTheApp":
            MessageLookupByLibrary.simpleMessage("You will exit the app"),
        "kYourInquiryHasBeenSubmittedSuccessfully":
            MessageLookupByLibrary.simpleMessage(
                "Your inquiry has been submitted successfully"),
        "kYourNewPasswordMustBeUniqueFromThosePreviouslyUsed":
            MessageLookupByLibrary.simpleMessage(
                "Your new password must be unique from those previously used"),
        "kYourPasswordHasBeenChangedSuccessfully":
            MessageLookupByLibrary.simpleMessage(
                "Your password has been\nchanged successfully"),
        "kYourProfileHaveBeenUpdatedSuccessfully":
            MessageLookupByLibrary.simpleMessage(
                "Your profile have been updated successfully"),
        "kYourSessionHasExpiredPleaseLoginAgain":
            MessageLookupByLibrary.simpleMessage(
                "Your session has expired please login again")
      };
}
