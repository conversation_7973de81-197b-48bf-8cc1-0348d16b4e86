// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Unknown`
  String get kUnknown {
    return Intl.message(
      'Unknown',
      name: 'kUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Unknown Error Occurred`
  String get UnknownError {
    return Intl.message(
      'Unknown Error Occurred',
      name: 'UnknownError',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get kConfirm {
    return Intl.message(
      'Confirm',
      name: 'kConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get kCancel {
    return Intl.message(
      'Cancel',
      name: 'kCancel',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get kYes {
    return Intl.message(
      'Yes',
      name: 'kYes',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get kNo {
    return Intl.message(
      'No',
      name: 'kNo',
      desc: '',
      args: [],
    );
  }

  /// `Ok`
  String get kOk {
    return Intl.message(
      'Ok',
      name: 'kOk',
      desc: '',
      args: [],
    );
  }

  /// `I Agree`
  String get kAgree {
    return Intl.message(
      'I Agree',
      name: 'kAgree',
      desc: '',
      args: [],
    );
  }

  /// `I Disagree`
  String get kDisagree {
    return Intl.message(
      'I Disagree',
      name: 'kDisagree',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Now`
  String get kUpgradeNow {
    return Intl.message(
      'Upgrade Now',
      name: 'kUpgradeNow',
      desc: '',
      args: [],
    );
  }

  /// `Unsupported App Version`
  String get kUnsupportedVersion {
    return Intl.message(
      'Unsupported App Version',
      name: 'kUnsupportedVersion',
      desc: '',
      args: [],
    );
  }

  /// `Please download the latest version to continue using the application`
  String get kUpgradeRequiredMessage {
    return Intl.message(
      'Please download the latest version to continue using the application',
      name: 'kUpgradeRequiredMessage',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to paste `
  String get kConfirmPasteDetails {
    return Intl.message(
      'Are you sure you want to paste ',
      name: 'kConfirmPasteDetails',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Paste`
  String get kConfirmPaste {
    return Intl.message(
      'Confirm Paste',
      name: 'kConfirmPaste',
      desc: '',
      args: [],
    );
  }

  /// `All rights reserved`
  String get kAllRightsReserved {
    return Intl.message(
      'All rights reserved',
      name: 'kAllRightsReserved',
      desc: '',
      args: [],
    );
  }

  /// `version`
  String get kVersion {
    return Intl.message(
      'version',
      name: 'kVersion',
      desc: '',
      args: [],
    );
  }

  /// `Support`
  String get kSupport {
    return Intl.message(
      'Support',
      name: 'kSupport',
      desc: '',
      args: [],
    );
  }

  /// `Privacy policy`
  String get kPrivacyPolicy {
    return Intl.message(
      'Privacy policy',
      name: 'kPrivacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get kHome {
    return Intl.message(
      'Home',
      name: 'kHome',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get kAccount {
    return Intl.message(
      'Account',
      name: 'kAccount',
      desc: '',
      args: [],
    );
  }

  /// `Calender`
  String get kCalender {
    return Intl.message(
      'Calender',
      name: 'kCalender',
      desc: '',
      args: [],
    );
  }

  /// `Appointments`
  String get kAppointments {
    return Intl.message(
      'Appointments',
      name: 'kAppointments',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get kSettings {
    return Intl.message(
      'Settings',
      name: 'kSettings',
      desc: '',
      args: [],
    );
  }

  /// `Contact Support`
  String get kContactSupport {
    return Intl.message(
      'Contact Support',
      name: 'kContactSupport',
      desc: '',
      args: [],
    );
  }

  /// `About`
  String get kAbout {
    return Intl.message(
      'About',
      name: 'kAbout',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get kLogout {
    return Intl.message(
      'Logout',
      name: 'kLogout',
      desc: '',
      args: [],
    );
  }

  /// `You will exit the app`
  String get kYouWillExitTheApp {
    return Intl.message(
      'You will exit the app',
      name: 'kYouWillExitTheApp',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get kProfile {
    return Intl.message(
      'Profile',
      name: 'kProfile',
      desc: '',
      args: [],
    );
  }

  /// `There's something wrong`
  String get kThereIsSomethingWrong {
    return Intl.message(
      'There\'s something wrong',
      name: 'kThereIsSomethingWrong',
      desc: '',
      args: [],
    );
  }

  /// `Hello there`
  String get kHelloThere {
    return Intl.message(
      'Hello there',
      name: 'kHelloThere',
      desc: '',
      args: [],
    );
  }

  /// `You're using the app as a guest,\nPlease register or login to continue.`
  String get kYouAreUsingTheAppAsAGuestPleaseRegisterOrLoginToContinue {
    return Intl.message(
      'You\'re using the app as a guest,\nPlease register or login to continue.',
      name: 'kYouAreUsingTheAppAsAGuestPleaseRegisterOrLoginToContinue',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get kLogin {
    return Intl.message(
      'Login',
      name: 'kLogin',
      desc: '',
      args: [],
    );
  }

  /// `Or Login with`
  String get kOrLoginWith {
    return Intl.message(
      'Or Login with',
      name: 'kOrLoginWith',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get kRegister {
    return Intl.message(
      'Register',
      name: 'kRegister',
      desc: '',
      args: [],
    );
  }

  /// `Or Register with`
  String get kOrRegisterWith {
    return Intl.message(
      'Or Register with',
      name: 'kOrRegisterWith',
      desc: '',
      args: [],
    );
  }

  /// `No Data to display`
  String get kNoDataToDisplay {
    return Intl.message(
      'No Data to display',
      name: 'kNoDataToDisplay',
      desc: '',
      args: [],
    );
  }

  /// `Male`
  String get kMale {
    return Intl.message(
      'Male',
      name: 'kMale',
      desc: '',
      args: [],
    );
  }

  /// `Female`
  String get kFemale {
    return Intl.message(
      'Female',
      name: 'kFemale',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password?`
  String get kForgetPassword {
    return Intl.message(
      'Forgot Password?',
      name: 'kForgetPassword',
      desc: '',
      args: [],
    );
  }

  /// `Enter your email`
  String get kEnterYourEmail {
    return Intl.message(
      'Enter your email',
      name: 'kEnterYourEmail',
      desc: '',
      args: [],
    );
  }

  /// `Enter your password`
  String get kEnterYourPassword {
    return Intl.message(
      'Enter your password',
      name: 'kEnterYourPassword',
      desc: '',
      args: [],
    );
  }

  /// `Send Code`
  String get kSendCode {
    return Intl.message(
      'Send Code',
      name: 'kSendCode',
      desc: '',
      args: [],
    );
  }

  /// `Remember password`
  String get kRememberPassword {
    return Intl.message(
      'Remember password',
      name: 'kRememberPassword',
      desc: '',
      args: [],
    );
  }

  /// `Your session has expired please login again`
  String get kYourSessionHasExpiredPleaseLoginAgain {
    return Intl.message(
      'Your session has expired please login again',
      name: 'kYourSessionHasExpiredPleaseLoginAgain',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get kSkip {
    return Intl.message(
      'Skip',
      name: 'kSkip',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back! Glad to see you again`
  String get kWelcomeBackGladToSeeYouAgain {
    return Intl.message(
      'Welcome back! Glad to see you again',
      name: 'kWelcomeBackGladToSeeYouAgain',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number`
  String get kEnterYourPhoneNumber {
    return Intl.message(
      'Enter your phone number',
      name: 'kEnterYourPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Don't have an account?`
  String get kDoNotHaveAnAccount {
    return Intl.message(
      'Don\'t have an account?',
      name: 'kDoNotHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Register Now`
  String get kRegisterNow {
    return Intl.message(
      'Register Now',
      name: 'kRegisterNow',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to AjmalNow! Register to get started`
  String get kWelcomeToAjmalNowRegisterToGetStarted {
    return Intl.message(
      'Welcome to AjmalNow! Register to get started',
      name: 'kWelcomeToAjmalNowRegisterToGetStarted',
      desc: '',
      args: [],
    );
  }

  /// `First name`
  String get kFirstName {
    return Intl.message(
      'First name',
      name: 'kFirstName',
      desc: '',
      args: [],
    );
  }

  /// `Middle name`
  String get kMiddleName {
    return Intl.message(
      'Middle name',
      name: 'kMiddleName',
      desc: '',
      args: [],
    );
  }

  /// `Last name`
  String get kLastName {
    return Intl.message(
      'Last name',
      name: 'kLastName',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get kPhoneNumber {
    return Intl.message(
      'Phone number',
      name: 'kPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Birth date`
  String get kBirthDate {
    return Intl.message(
      'Birth date',
      name: 'kBirthDate',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get kEmail {
    return Intl.message(
      'Email',
      name: 'kEmail',
      desc: '',
      args: [],
    );
  }

  /// `Already have an account?`
  String get kAlreadyHaveAnAccount {
    return Intl.message(
      'Already have an account?',
      name: 'kAlreadyHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Login Now`
  String get kLoginNow {
    return Intl.message(
      'Login Now',
      name: 'kLoginNow',
      desc: '',
      args: [],
    );
  }

  /// `Create new password`
  String get kCreateNewPassword {
    return Intl.message(
      'Create new password',
      name: 'kCreateNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `Your new password must be unique from those previously used`
  String get kYourNewPasswordMustBeUniqueFromThosePreviouslyUsed {
    return Intl.message(
      'Your new password must be unique from those previously used',
      name: 'kYourNewPasswordMustBeUniqueFromThosePreviouslyUsed',
      desc: '',
      args: [],
    );
  }

  /// `New password`
  String get kNewPassword {
    return Intl.message(
      'New password',
      name: 'kNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `Confirm password`
  String get kConfirmPassword {
    return Intl.message(
      'Confirm password',
      name: 'kConfirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Reset Password`
  String get kResetPassword {
    return Intl.message(
      'Reset Password',
      name: 'kResetPassword',
      desc: '',
      args: [],
    );
  }

  /// `Password changed`
  String get kPasswordChanged {
    return Intl.message(
      'Password changed',
      name: 'kPasswordChanged',
      desc: '',
      args: [],
    );
  }

  /// `Your password has been\nchanged successfully`
  String get kYourPasswordHasBeenChangedSuccessfully {
    return Intl.message(
      'Your password has been\nchanged successfully',
      name: 'kYourPasswordHasBeenChangedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Back to Login`
  String get kBackToLogin {
    return Intl.message(
      'Back to Login',
      name: 'kBackToLogin',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to AjmalNow! Complete your profile to get started.`
  String get kWelcomeToAjmalNowCompleteYourProfileToGetStarted {
    return Intl.message(
      'Welcome to AjmalNow! Complete your profile to get started.',
      name: 'kWelcomeToAjmalNowCompleteYourProfileToGetStarted',
      desc: '',
      args: [],
    );
  }

  /// `Complete Profile`
  String get kCompleteProfile {
    return Intl.message(
      'Complete Profile',
      name: 'kCompleteProfile',
      desc: '',
      args: [],
    );
  }

  /// `OTP Verification`
  String get kOtpVerification {
    return Intl.message(
      'OTP Verification',
      name: 'kOtpVerification',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code we just sent on your phone.`
  String get kEnterTheVerificationCodeWeJustSentOnYourPhone {
    return Intl.message(
      'Enter the verification code we just sent on your phone.',
      name: 'kEnterTheVerificationCodeWeJustSentOnYourPhone',
      desc: '',
      args: [],
    );
  }

  /// `Verify`
  String get kVerify {
    return Intl.message(
      'Verify',
      name: 'kVerify',
      desc: '',
      args: [],
    );
  }

  /// `Didn't receive code?`
  String get kDidNotReceiveCode {
    return Intl.message(
      'Didn\'t receive code?',
      name: 'kDidNotReceiveCode',
      desc: '',
      args: [],
    );
  }

  /// `Resend again`
  String get kResendAgain {
    return Intl.message(
      'Resend again',
      name: 'kResendAgain',
      desc: '',
      args: [],
    );
  }

  /// `Upcoming`
  String get kUpcoming {
    return Intl.message(
      'Upcoming',
      name: 'kUpcoming',
      desc: '',
      args: [],
    );
  }

  /// `Complete`
  String get kComplete {
    return Intl.message(
      'Complete',
      name: 'kComplete',
      desc: '',
      args: [],
    );
  }

  /// `Cancelled`
  String get kCancelled {
    return Intl.message(
      'Cancelled',
      name: 'kCancelled',
      desc: '',
      args: [],
    );
  }

  /// `Blocked`
  String get kBlocked {
    return Intl.message(
      'Blocked',
      name: 'kBlocked',
      desc: '',
      args: [],
    );
  }

  /// `Online Payment`
  String get kOnlinePayment {
    return Intl.message(
      'Online Payment',
      name: 'kOnlinePayment',
      desc: '',
      args: [],
    );
  }

  /// `Appointment reserved successfully`
  String get kAppointmentReservedSuccessfully {
    return Intl.message(
      'Appointment reserved successfully',
      name: 'kAppointmentReservedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get kSomethingWentWrong {
    return Intl.message(
      'Something went wrong',
      name: 'kSomethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `How can we help?`
  String get kHowCanWeHelp {
    return Intl.message(
      'How can we help?',
      name: 'kHowCanWeHelp',
      desc: '',
      args: [],
    );
  }

  /// `Please fill in the form below. Our customer service representatives will contact you shortly.`
  String
      get kPleaseFillInTheFormBelowOurCustomerServiceRepresentativesWillContactYouShortly {
    return Intl.message(
      'Please fill in the form below. Our customer service representatives will contact you shortly.',
      name:
          'kPleaseFillInTheFormBelowOurCustomerServiceRepresentativesWillContactYouShortly',
      desc: '',
      args: [],
    );
  }

  /// `Message`
  String get kMessage {
    return Intl.message(
      'Message',
      name: 'kMessage',
      desc: '',
      args: [],
    );
  }

  /// `Type your message...`
  String get kTypeYourMessage {
    return Intl.message(
      'Type your message...',
      name: 'kTypeYourMessage',
      desc: '',
      args: [],
    );
  }

  /// `Feel free to rate us on the store\nand leave a comment there`
  String get kFeelFreeToRateUsOnTheStoreAndLeaveACommentThere {
    return Intl.message(
      'Feel free to rate us on the store\nand leave a comment there',
      name: 'kFeelFreeToRateUsOnTheStoreAndLeaveACommentThere',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get kSubmit {
    return Intl.message(
      'Submit',
      name: 'kSubmit',
      desc: '',
      args: [],
    );
  }

  /// `Your inquiry has been submitted successfully`
  String get kYourInquiryHasBeenSubmittedSuccessfully {
    return Intl.message(
      'Your inquiry has been submitted successfully',
      name: 'kYourInquiryHasBeenSubmittedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Created at: `
  String get kCreatedAt {
    return Intl.message(
      'Created at: ',
      name: 'kCreatedAt',
      desc: '',
      args: [],
    );
  }

  /// `Updated at: `
  String get kUpdatedAt {
    return Intl.message(
      'Updated at: ',
      name: 'kUpdatedAt',
      desc: '',
      args: [],
    );
  }

  /// `Support Agent`
  String get kSupportAgent {
    return Intl.message(
      'Support Agent',
      name: 'kSupportAgent',
      desc: '',
      args: [],
    );
  }

  /// `Payment\nFailed`
  String get kPaymentFailed {
    return Intl.message(
      'Payment\nFailed',
      name: 'kPaymentFailed',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get kNext {
    return Intl.message(
      'Next',
      name: 'kNext',
      desc: '',
      args: [],
    );
  }

  /// `Get started`
  String get kGetStarted {
    return Intl.message(
      'Get started',
      name: 'kGetStarted',
      desc: '',
      args: [],
    );
  }

  /// `Age group`
  String get kAgeGroup {
    return Intl.message(
      'Age group',
      name: 'kAgeGroup',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get kGender {
    return Intl.message(
      'Gender',
      name: 'kGender',
      desc: '',
      args: [],
    );
  }

  /// `Account Details`
  String get kAccountDetails {
    return Intl.message(
      'Account Details',
      name: 'kAccountDetails',
      desc: '',
      args: [],
    );
  }

  /// `Pick profile image`
  String get kPickProfileImage {
    return Intl.message(
      'Pick profile image',
      name: 'kPickProfileImage',
      desc: '',
      args: [],
    );
  }

  /// `Pick from gallery`
  String get kPickFromGallery {
    return Intl.message(
      'Pick from gallery',
      name: 'kPickFromGallery',
      desc: '',
      args: [],
    );
  }

  /// `Capture from camera`
  String get kCaptureFromCamera {
    return Intl.message(
      'Capture from camera',
      name: 'kCaptureFromCamera',
      desc: '',
      args: [],
    );
  }

  /// `Your profile have been updated successfully`
  String get kYourProfileHaveBeenUpdatedSuccessfully {
    return Intl.message(
      'Your profile have been updated successfully',
      name: 'kYourProfileHaveBeenUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get kSave {
    return Intl.message(
      'Save',
      name: 'kSave',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get kAll {
    return Intl.message(
      'All',
      name: 'kAll',
      desc: '',
      args: [],
    );
  }

  /// `Spas and Wellness Centers`
  String get kSapsAndWellnessCenters {
    return Intl.message(
      'Spas and Wellness Centers',
      name: 'kSapsAndWellnessCenters',
      desc: '',
      args: [],
    );
  }

  /// `Makeup Professionals`
  String get kMakeupProfessionals {
    return Intl.message(
      'Makeup Professionals',
      name: 'kMakeupProfessionals',
      desc: '',
      args: [],
    );
  }

  /// `Dermatologists`
  String get kDermatologists {
    return Intl.message(
      'Dermatologists',
      name: 'kDermatologists',
      desc: '',
      args: [],
    );
  }

  /// `Plastic Surgeons`
  String get kPlasticSurgeons {
    return Intl.message(
      'Plastic Surgeons',
      name: 'kPlasticSurgeons',
      desc: '',
      args: [],
    );
  }

  /// `More...`
  String get kMore {
    return Intl.message(
      'More...',
      name: 'kMore',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back`
  String get kWelcomeBack {
    return Intl.message(
      'Welcome back',
      name: 'kWelcomeBack',
      desc: '',
      args: [],
    );
  }

  /// `Type here to search...`
  String get kTypeHereToSearch {
    return Intl.message(
      'Type here to search...',
      name: 'kTypeHereToSearch',
      desc: '',
      args: [],
    );
  }

  /// `Please type in something to search`
  String get kPleaseTypeInSomethingToSearch {
    return Intl.message(
      'Please type in something to search',
      name: 'kPleaseTypeInSomethingToSearch',
      desc: '',
      args: [],
    );
  }

  /// `What are you looking for?`
  String get kWhatAreYouLookingFor {
    return Intl.message(
      'What are you looking for?',
      name: 'kWhatAreYouLookingFor',
      desc: '',
      args: [],
    );
  }

  /// `Filter`
  String get kFilter {
    return Intl.message(
      'Filter',
      name: 'kFilter',
      desc: '',
      args: [],
    );
  }

  /// `Sort by`
  String get kSortBy {
    return Intl.message(
      'Sort by',
      name: 'kSortBy',
      desc: '',
      args: [],
    );
  }

  /// `Hint`
  String get kHint {
    return Intl.message(
      'Hint',
      name: 'kHint',
      desc: '',
      args: [],
    );
  }

  /// `Nearest`
  String get kNearest {
    return Intl.message(
      'Nearest',
      name: 'kNearest',
      desc: '',
      args: [],
    );
  }

  /// `Cheapest`
  String get kCheapest {
    return Intl.message(
      'Cheapest',
      name: 'kCheapest',
      desc: '',
      args: [],
    );
  }

  /// `Farthest`
  String get kFarthest {
    return Intl.message(
      'Farthest',
      name: 'kFarthest',
      desc: '',
      args: [],
    );
  }

  /// `Search results`
  String get kSearchResults {
    return Intl.message(
      'Search results',
      name: 'kSearchResults',
      desc: '',
      args: [],
    );
  }

  /// `Disclaimer`
  String get kDisclaimer {
    return Intl.message(
      'Disclaimer',
      name: 'kDisclaimer',
      desc: '',
      args: [],
    );
  }

  /// `A value of EGP 10 will be deducted as reservation fees.`
  String get kAValueOfEgp10WillBeDeductedAsReservationFees {
    return Intl.message(
      'A value of EGP 10 will be deducted as reservation fees.',
      name: 'kAValueOfEgp10WillBeDeductedAsReservationFees',
      desc: '',
      args: [],
    );
  }

  /// `Reserve`
  String get kReserve {
    return Intl.message(
      'Reserve',
      name: 'kReserve',
      desc: '',
      args: [],
    );
  }

  /// `Appointment Details`
  String get kAppointmentDetails {
    return Intl.message(
      'Appointment Details',
      name: 'kAppointmentDetails',
      desc: '',
      args: [],
    );
  }

  /// `Facility type`
  String get kFacilityType {
    return Intl.message(
      'Facility type',
      name: 'kFacilityType',
      desc: '',
      args: [],
    );
  }

  /// `Facility address`
  String get kFacilityAddress {
    return Intl.message(
      'Facility address',
      name: 'kFacilityAddress',
      desc: '',
      args: [],
    );
  }

  /// `Booking ID`
  String get kBookingId {
    return Intl.message(
      'Booking ID',
      name: 'kBookingId',
      desc: '',
      args: [],
    );
  }

  /// `Date`
  String get kDate {
    return Intl.message(
      'Date',
      name: 'kDate',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get kTime {
    return Intl.message(
      'Time',
      name: 'kTime',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get kStatus {
    return Intl.message(
      'Status',
      name: 'kStatus',
      desc: '',
      args: [],
    );
  }

  /// `Client arrived at`
  String get kClientArrivedAt {
    return Intl.message(
      'Client arrived at',
      name: 'kClientArrivedAt',
      desc: '',
      args: [],
    );
  }

  /// `Client checked in successfully`
  String get kClientCheckedInSuccessfully {
    return Intl.message(
      'Client checked in successfully',
      name: 'kClientCheckedInSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Check in client`
  String get kCheckInClient {
    return Intl.message(
      'Check in client',
      name: 'kCheckInClient',
      desc: '',
      args: [],
    );
  }

  /// `No Qr code detected`
  String get kNoQrCodeDetected {
    return Intl.message(
      'No Qr code detected',
      name: 'kNoQrCodeDetected',
      desc: '',
      args: [],
    );
  }

  /// `Please make sure the Qr code is visible and try again`
  String get kPleaseMakeSureTheQrCodeIsVisibleAndTryAgain {
    return Intl.message(
      'Please make sure the Qr code is visible and try again',
      name: 'kPleaseMakeSureTheQrCodeIsVisibleAndTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Qr code scanned successfully`
  String get kQrCodeScannedSuccessfully {
    return Intl.message(
      'Qr code scanned successfully',
      name: 'kQrCodeScannedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `You will be redirected to the previous screen`
  String get kYouWillBeRedirectedToThePreviousScreen {
    return Intl.message(
      'You will be redirected to the previous screen',
      name: 'kYouWillBeRedirectedToThePreviousScreen',
      desc: '',
      args: [],
    );
  }

  /// `Gallery`
  String get kGallery {
    return Intl.message(
      'Gallery',
      name: 'kGallery',
      desc: '',
      args: [],
    );
  }

  /// `Service and Packages`
  String get kServiceAndPackages {
    return Intl.message(
      'Service and Packages',
      name: 'kServiceAndPackages',
      desc: '',
      args: [],
    );
  }

  /// `Availability`
  String get kAvailability {
    return Intl.message(
      'Availability',
      name: 'kAvailability',
      desc: '',
      args: [],
    );
  }

  /// `EGP`
  String get kEGP {
    return Intl.message(
      'EGP',
      name: 'kEGP',
      desc: '',
      args: [],
    );
  }

  /// `Notification Settings`
  String get kNotificationSettings {
    return Intl.message(
      'Notification Settings',
      name: 'kNotificationSettings',
      desc: '',
      args: [],
    );
  }

  /// `App Language`
  String get kAppLanguage {
    return Intl.message(
      'App Language',
      name: 'kAppLanguage',
      desc: '',
      args: [],
    );
  }

  /// `عربي`
  String get kArabic {
    return Intl.message(
      'عربي',
      name: 'kArabic',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get kEnglish {
    return Intl.message(
      'English',
      name: 'kEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Rate the App`
  String get kRateTheApp {
    return Intl.message(
      'Rate the App',
      name: 'kRateTheApp',
      desc: '',
      args: [],
    );
  }

  /// `Appointment Notifications`
  String get kAppointmentNotifications {
    return Intl.message(
      'Appointment Notifications',
      name: 'kAppointmentNotifications',
      desc: '',
      args: [],
    );
  }

  /// `1 hour before`
  String get k1HourBefore {
    return Intl.message(
      '1 hour before',
      name: 'k1HourBefore',
      desc: '',
      args: [],
    );
  }

  /// `6 hours before`
  String get k6HoursBefore {
    return Intl.message(
      '6 hours before',
      name: 'k6HoursBefore',
      desc: '',
      args: [],
    );
  }

  /// `1 day before`
  String get k1DayBefore {
    return Intl.message(
      '1 day before',
      name: 'k1DayBefore',
      desc: '',
      args: [],
    );
  }

  /// `Appointment Cancellation`
  String get kAppointmentCancellation {
    return Intl.message(
      'Appointment Cancellation',
      name: 'kAppointmentCancellation',
      desc: '',
      args: [],
    );
  }

  /// `Send app notification`
  String get kSendAppNotification {
    return Intl.message(
      'Send app notification',
      name: 'kSendAppNotification',
      desc: '',
      args: [],
    );
  }

  /// `Send an SMS`
  String get kSendAnSms {
    return Intl.message(
      'Send an SMS',
      name: 'kSendAnSms',
      desc: '',
      args: [],
    );
  }

  /// `Send an e-mail`
  String get kSendAnEmail {
    return Intl.message(
      'Send an e-mail',
      name: 'kSendAnEmail',
      desc: '',
      args: [],
    );
  }

  /// `No worries! Please enter the email address linked with your account`
  String get kNoWorriesPleaseEnterTheEmailAddressLinkedWithYourAccount {
    return Intl.message(
      'No worries! Please enter the email address linked with your account',
      name: 'kNoWorriesPleaseEnterTheEmailAddressLinkedWithYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `My services`
  String get kMyServices {
    return Intl.message(
      'My services',
      name: 'kMyServices',
      desc: '',
      args: [],
    );
  }

  /// `You don't own any branches right now.\nPlease contact us to submit your branches.`
  String
      get kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices {
    return Intl.message(
      'You don\'t own any branches right now.\nPlease contact us to submit your branches.',
      name:
          'kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices',
      desc: '',
      args: [],
    );
  }

  /// `Contact Us`
  String get kContactUs {
    return Intl.message(
      'Contact Us',
      name: 'kContactUs',
      desc: '',
      args: [],
    );
  }

  /// `Select a branch to view its details`
  String get kSelectAServiceToViewItsDetails {
    return Intl.message(
      'Select a branch to view its details',
      name: 'kSelectAServiceToViewItsDetails',
      desc: '',
      args: [],
    );
  }

  /// `Time slot is now available`
  String get kTimeSlotIsNowAvailable {
    return Intl.message(
      'Time slot is now available',
      name: 'kTimeSlotIsNowAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Time slot blocked successfully`
  String get kTimeSlotBlockedSuccessfully {
    return Intl.message(
      'Time slot blocked successfully',
      name: 'kTimeSlotBlockedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Total bookings`
  String get kTotalBookings {
    return Intl.message(
      'Total bookings',
      name: 'kTotalBookings',
      desc: '',
      args: [],
    );
  }

  /// `Bookings last week`
  String get kBookingsLastWeek {
    return Intl.message(
      'Bookings last week',
      name: 'kBookingsLastWeek',
      desc: '',
      args: [],
    );
  }

  /// `Bookings this week`
  String get kBookingsThisWeek {
    return Intl.message(
      'Bookings this week',
      name: 'kBookingsThisWeek',
      desc: '',
      args: [],
    );
  }

  /// `Create New Service`
  String get kCreateNewService {
    return Intl.message(
      'Create New Service',
      name: 'kCreateNewService',
      desc: '',
      args: [],
    );
  }

  /// `Service Name`
  String get kServiceName {
    return Intl.message(
      'Service Name',
      name: 'kServiceName',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get kDescription {
    return Intl.message(
      'Description',
      name: 'kDescription',
      desc: '',
      args: [],
    );
  }

  /// `Special Offers`
  String get kOffers {
    return Intl.message(
      'Special Offers',
      name: 'kOffers',
      desc: '',
      args: [],
    );
  }

  /// `Special Offers`
  String get kSpecialOffers {
    return Intl.message(
      'Special Offers',
      name: 'kSpecialOffers',
      desc: '',
      args: [],
    );
  }

  /// `Type`
  String get kType {
    return Intl.message(
      'Type',
      name: 'kType',
      desc: '',
      args: [],
    );
  }

  /// `Available Locations`
  String get kAvailableLocations {
    return Intl.message(
      'Available Locations',
      name: 'kAvailableLocations',
      desc: '',
      args: [],
    );
  }

  /// `Original Price (EGP)`
  String get kOriginalPriceEGP {
    return Intl.message(
      'Original Price (EGP)',
      name: 'kOriginalPriceEGP',
      desc: '',
      args: [],
    );
  }

  /// `Min Price (EGP)`
  String get kMinPriceEGP {
    return Intl.message(
      'Min Price (EGP)',
      name: 'kMinPriceEGP',
      desc: '',
      args: [],
    );
  }

  /// `Max Price (EGP)`
  String get kMaxPriceEGP {
    return Intl.message(
      'Max Price (EGP)',
      name: 'kMaxPriceEGP',
      desc: '',
      args: [],
    );
  }

  /// `Discount Percentage`
  String get kDiscountPercentage {
    return Intl.message(
      'Discount Percentage',
      name: 'kDiscountPercentage',
      desc: '',
      args: [],
    );
  }

  /// `Discount Active`
  String get kDiscountActive {
    return Intl.message(
      'Discount Active',
      name: 'kDiscountActive',
      desc: '',
      args: [],
    );
  }

  /// `Schedule Day`
  String get kScheduleDay {
    return Intl.message(
      'Schedule Day',
      name: 'kScheduleDay',
      desc: '',
      args: [],
    );
  }

  /// `Select at least one day`
  String get kSelectAtLeastOneDay {
    return Intl.message(
      'Select at least one day',
      name: 'kSelectAtLeastOneDay',
      desc: '',
      args: [],
    );
  }

  /// `Start Time`
  String get kStartTime {
    return Intl.message(
      'Start Time',
      name: 'kStartTime',
      desc: '',
      args: [],
    );
  }

  /// `End Time`
  String get kEndTime {
    return Intl.message(
      'End Time',
      name: 'kEndTime',
      desc: '',
      args: [],
    );
  }

  /// `Day-Based Availability`
  String get kDayBasedAvailability {
    return Intl.message(
      'Day-Based Availability',
      name: 'kDayBasedAvailability',
      desc: '',
      args: [],
    );
  }

  /// `Set availability for each day of the week`
  String get kSetAvailabilityForEachDay {
    return Intl.message(
      'Set availability for each day of the week',
      name: 'kSetAvailabilityForEachDay',
      desc: '',
      args: [],
    );
  }

  /// `Service`
  String get kService {
    return Intl.message(
      'Service',
      name: 'kService',
      desc: '',
      args: [],
    );
  }

  /// `Configured`
  String get kConfigured {
    return Intl.message(
      'Configured',
      name: 'kConfigured',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get kDone {
    return Intl.message(
      'Done',
      name: 'kDone',
      desc: '',
      args: [],
    );
  }

  /// `Available`
  String get kAvailable {
    return Intl.message(
      'Available',
      name: 'kAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Unavailable`
  String get kUnavailable {
    return Intl.message(
      'Unavailable',
      name: 'kUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `Add Time Slot`
  String get kAddTimeSlot {
    return Intl.message(
      'Add Time Slot',
      name: 'kAddTimeSlot',
      desc: '',
      args: [],
    );
  }

  /// `Time Slots`
  String get kTimeSlots {
    return Intl.message(
      'Time Slots',
      name: 'kTimeSlots',
      desc: '',
      args: [],
    );
  }

  /// `Set availability for {date}`
  String kSetAvailabilityFor(Object date) {
    return Intl.message(
      'Set availability for $date',
      name: 'kSetAvailabilityFor',
      desc: '',
      args: [date],
    );
  }

  /// `Mark unavailable for {date}`
  String kMarkUnavailableFor(Object date) {
    return Intl.message(
      'Mark unavailable for $date',
      name: 'kMarkUnavailableFor',
      desc: '',
      args: [date],
    );
  }

  /// `Please add at least one time slot`
  String get kPleaseAddAtLeastOneTimeSlot {
    return Intl.message(
      'Please add at least one time slot',
      name: 'kPleaseAddAtLeastOneTimeSlot',
      desc: '',
      args: [],
    );
  }

  /// `Please select a valid time slot`
  String get kPleaseSelectAValidTimeSlot {
    return Intl.message(
      'Please select a valid time slot',
      name: 'kPleaseSelectAValidTimeSlot',
      desc: '',
      args: [],
    );
  }

  /// `Create`
  String get kCreate {
    return Intl.message(
      'Create',
      name: 'kCreate',
      desc: '',
      args: [],
    );
  }

  /// `Edit Service`
  String get kEditService {
    return Intl.message(
      'Edit Service',
      name: 'kEditService',
      desc: '',
      args: [],
    );
  }

  /// `Sat`
  String get kSat {
    return Intl.message(
      'Sat',
      name: 'kSat',
      desc: '',
      args: [],
    );
  }

  /// `Sun`
  String get kSun {
    return Intl.message(
      'Sun',
      name: 'kSun',
      desc: '',
      args: [],
    );
  }

  /// `Mon`
  String get kMon {
    return Intl.message(
      'Mon',
      name: 'kMon',
      desc: '',
      args: [],
    );
  }

  /// `Tue`
  String get kTue {
    return Intl.message(
      'Tue',
      name: 'kTue',
      desc: '',
      args: [],
    );
  }

  /// `Wed`
  String get kWed {
    return Intl.message(
      'Wed',
      name: 'kWed',
      desc: '',
      args: [],
    );
  }

  /// `Thu`
  String get kThu {
    return Intl.message(
      'Thu',
      name: 'kThu',
      desc: '',
      args: [],
    );
  }

  /// `Fri`
  String get kFri {
    return Intl.message(
      'Fri',
      name: 'kFri',
      desc: '',
      args: [],
    );
  }

  /// `Service created successfully`
  String get kServiceCreatedSuccessfully {
    return Intl.message(
      'Service created successfully',
      name: 'kServiceCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Service updated successfully`
  String get kServiceUpdatedSuccessfully {
    return Intl.message(
      'Service updated successfully',
      name: 'kServiceUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Service deleted successfully`
  String get kServiceDeletedSuccessfully {
    return Intl.message(
      'Service deleted successfully',
      name: 'kServiceDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Owners`
  String get kOwners {
    return Intl.message(
      'Owners',
      name: 'kOwners',
      desc: '',
      args: [],
    );
  }

  /// `Services`
  String get kServices {
    return Intl.message(
      'Services',
      name: 'kServices',
      desc: '',
      args: [],
    );
  }

  /// `Branches`
  String get kBranches {
    return Intl.message(
      'Branches',
      name: 'kBranches',
      desc: '',
      args: [],
    );
  }

  /// `Create New Branch`
  String get kCreateNewBranch {
    return Intl.message(
      'Create New Branch',
      name: 'kCreateNewBranch',
      desc: '',
      args: [],
    );
  }

  /// `You must add a logo`
  String get kYouMustAddALogo {
    return Intl.message(
      'You must add a logo',
      name: 'kYouMustAddALogo',
      desc: '',
      args: [],
    );
  }

  /// `At home visits available`
  String get kAtHomeVisitsAvailable {
    return Intl.message(
      'At home visits available',
      name: 'kAtHomeVisitsAvailable',
      desc: '',
      args: [],
    );
  }

  /// `You must add at least one image`
  String get kYouMustAddAtLeastOneImage {
    return Intl.message(
      'You must add at least one image',
      name: 'kYouMustAddAtLeastOneImage',
      desc: '',
      args: [],
    );
  }

  /// `Clinic Name`
  String get kBranchName {
    return Intl.message(
      'Clinic Name',
      name: 'kBranchName',
      desc: '',
      args: [],
    );
  }

  /// `Branch created successfully`
  String get kBranchCreatedSuccessfully {
    return Intl.message(
      'Branch created successfully',
      name: 'kBranchCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Add Image`
  String get kAddImage {
    return Intl.message(
      'Add Image',
      name: 'kAddImage',
      desc: '',
      args: [],
    );
  }

  /// `Is this a package?`
  String get kIsThisAPackage {
    return Intl.message(
      'Is this a package?',
      name: 'kIsThisAPackage',
      desc: '',
      args: [],
    );
  }

  /// `Edit Branch`
  String get kEditBranch {
    return Intl.message(
      'Edit Branch',
      name: 'kEditBranch',
      desc: '',
      args: [],
    );
  }

  /// `Branch updated successfully`
  String get kBranchUpdatedSuccessfully {
    return Intl.message(
      'Branch updated successfully',
      name: 'kBranchUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Branch deleted successfully`
  String get kBranchDeletedSuccessfully {
    return Intl.message(
      'Branch deleted successfully',
      name: 'kBranchDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Delete Branch`
  String get kDeleteBranch {
    return Intl.message(
      'Delete Branch',
      name: 'kDeleteBranch',
      desc: '',
      args: [],
    );
  }

  /// `Delete Service`
  String get kDeleteService {
    return Intl.message(
      'Delete Service',
      name: 'kDeleteService',
      desc: '',
      args: [],
    );
  }

  /// `Pick a location`
  String get kPickALocation {
    return Intl.message(
      'Pick a location',
      name: 'kPickALocation',
      desc: '',
      args: [],
    );
  }

  /// `Clinic Name`
  String get kClinicName {
    return Intl.message(
      'Clinic Name',
      name: 'kClinicName',
      desc: '',
      args: [],
    );
  }

  /// `Clinic Phone Number`
  String get kClinicHotline {
    return Intl.message(
      'Clinic Phone Number',
      name: 'kClinicHotline',
      desc: '',
      args: [],
    );
  }

  /// `Clinic Secondary Phone Number`
  String get kClinicPhone {
    return Intl.message(
      'Clinic Secondary Phone Number',
      name: 'kClinicPhone',
      desc: '',
      args: [],
    );
  }

  /// `City`
  String get kCity {
    return Intl.message(
      'City',
      name: 'kCity',
      desc: '',
      args: [],
    );
  }

  /// `District`
  String get kDistrict {
    return Intl.message(
      'District',
      name: 'kDistrict',
      desc: '',
      args: [],
    );
  }

  /// `You must pick a location`
  String get kYouMustPickALocation {
    return Intl.message(
      'You must pick a location',
      name: 'kYouMustPickALocation',
      desc: '',
      args: [],
    );
  }

  /// `Could not get location`
  String get kCouldNotGetLocation {
    return Intl.message(
      'Could not get location',
      name: 'kCouldNotGetLocation',
      desc: '',
      args: [],
    );
  }

  /// `My Branches`
  String get kMyBranches {
    return Intl.message(
      'My Branches',
      name: 'kMyBranches',
      desc: '',
      args: [],
    );
  }

  /// `You don't have any branches.`
  String get kNoBranches {
    return Intl.message(
      'You don\'t have any branches.',
      name: 'kNoBranches',
      desc: '',
      args: [],
    );
  }

  /// `That's not the right code`
  String get kThatsNotTheRightCode {
    return Intl.message(
      'That\'s not the right code',
      name: 'kThatsNotTheRightCode',
      desc: '',
      args: [],
    );
  }

  /// `This field can't be empty`
  String get kThisFieldCantBeEmpty {
    return Intl.message(
      'This field can\'t be empty',
      name: 'kThisFieldCantBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Current Password`
  String get kCurrentPassword {
    return Intl.message(
      'Current Password',
      name: 'kCurrentPassword',
      desc: '',
      args: [],
    );
  }

  /// `Change Password`
  String get kChangePassword {
    return Intl.message(
      'Change Password',
      name: 'kChangePassword',
      desc: '',
      args: [],
    );
  }

  /// `No upcoming appointments`
  String get kNoUpcomingAppointments {
    return Intl.message(
      'No upcoming appointments',
      name: 'kNoUpcomingAppointments',
      desc: '',
      args: [],
    );
  }

  /// `No past appointments`
  String get kNoPastAppointments {
    return Intl.message(
      'No past appointments',
      name: 'kNoPastAppointments',
      desc: '',
      args: [],
    );
  }

  /// `Past`
  String get kPast {
    return Intl.message(
      'Past',
      name: 'kPast',
      desc: '',
      args: [],
    );
  }

  /// `Schedule`
  String get kSchedule {
    return Intl.message(
      'Schedule',
      name: 'kSchedule',
      desc: '',
      args: [],
    );
  }

  /// `Weekly hours`
  String get kWeeklyHours {
    return Intl.message(
      'Weekly hours',
      name: 'kWeeklyHours',
      desc: '',
      args: [],
    );
  }

  /// `Date-specific hours`
  String get kDateSpecificHours {
    return Intl.message(
      'Date-specific hours',
      name: 'kDateSpecificHours',
      desc: '',
      args: [],
    );
  }

  /// `Use same hours for all days`
  String get kUseSameHoursForAllDays {
    return Intl.message(
      'Use same hours for all days',
      name: 'kUseSameHoursForAllDays',
      desc: '',
      args: [],
    );
  }

  /// `Set as Available`
  String get kSetAvailable {
    return Intl.message(
      'Set as Available',
      name: 'kSetAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Set as Unavailable`
  String get kSetUnavailable {
    return Intl.message(
      'Set as Unavailable',
      name: 'kSetUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure about deleting this service?`
  String get kAreYouSureAboutDeletingThisService {
    return Intl.message(
      'Are you sure about deleting this service?',
      name: 'kAreYouSureAboutDeletingThisService',
      desc: '',
      args: [],
    );
  }

  /// `Update Service`
  String get kUpdateService {
    return Intl.message(
      'Update Service',
      name: 'kUpdateService',
      desc: '',
      args: [],
    );
  }

  /// `Error`
  String get kError {
    return Intl.message(
      'Error',
      name: 'kError',
      desc: '',
      args: [],
    );
  }

  /// `Please upload an image for the service`
  String get kServiceUploadImageValidation {
    return Intl.message(
      'Please upload an image for the service',
      name: 'kServiceUploadImageValidation',
      desc: '',
      args: [],
    );
  }

  /// `Please enter at least one price`
  String get kPleaseEnterAtLeastOnePrice {
    return Intl.message(
      'Please enter at least one price',
      name: 'kPleaseEnterAtLeastOnePrice',
      desc: '',
      args: [],
    );
  }

  /// `Max price must be greater than min price`
  String get kMaxPriceMustBeGreaterThanMinPrice {
    return Intl.message(
      'Max price must be greater than min price',
      name: 'kMaxPriceMustBeGreaterThanMinPrice',
      desc: '',
      args: [],
    );
  }

  /// `Date-Specific Scheduling`
  String get kCalendarScheduleInfo {
    return Intl.message(
      'Date-Specific Scheduling',
      name: 'kCalendarScheduleInfo',
      desc: '',
      args: [],
    );
  }

  /// `Select a date from the calendar to set or modify availability and time slots. You can mark days as available with time slots or unavailable.`
  String get kSelectDayToSetAvailabilityDescription {
    return Intl.message(
      'Select a date from the calendar to set or modify availability and time slots. You can mark days as available with time slots or unavailable.',
      name: 'kSelectDayToSetAvailabilityDescription',
      desc: '',
      args: [],
    );
  }

  /// `Available`
  String get kAvailableDays {
    return Intl.message(
      'Available',
      name: 'kAvailableDays',
      desc: '',
      args: [],
    );
  }

  /// `Unavailable`
  String get kUnavailableDays {
    return Intl.message(
      'Unavailable',
      name: 'kUnavailableDays',
      desc: '',
      args: [],
    );
  }

  /// `Not Set`
  String get kNotSetDays {
    return Intl.message(
      'Not Set',
      name: 'kNotSetDays',
      desc: '',
      args: [],
    );
  }

  /// `Please select a date first`
  String get kPleaseSelectDateFirst {
    return Intl.message(
      'Please select a date first',
      name: 'kPleaseSelectDateFirst',
      desc: '',
      args: [],
    );
  }

  /// `Edit Availability`
  String get kEditAvailability {
    return Intl.message(
      'Edit Availability',
      name: 'kEditAvailability',
      desc: '',
      args: [],
    );
  }

  /// `Set as Available`
  String get kSetAsAvailable {
    return Intl.message(
      'Set as Available',
      name: 'kSetAsAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Edit Unavailability`
  String get kEditUnavailability {
    return Intl.message(
      'Edit Unavailability',
      name: 'kEditUnavailability',
      desc: '',
      args: [],
    );
  }

  /// `Set as Unavailable`
  String get kSetAsUnavailable {
    return Intl.message(
      'Set as Unavailable',
      name: 'kSetAsUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `Appointment Time`
  String get kAppointmentTime {
    return Intl.message(
      'Appointment Time',
      name: 'kAppointmentTime',
      desc: '',
      args: [],
    );
  }

  /// `Note: After updating the Freezed models, run 'flutter pub run build_runner build --delete-conflicting-outputs' in both projects to update the generated code.`
  String get kSlotUpdateNote {
    return Intl.message(
      'Note: After updating the Freezed models, run \'flutter pub run build_runner build --delete-conflicting-outputs\' in both projects to update the generated code.',
      name: 'kSlotUpdateNote',
      desc: '',
      args: [],
    );
  }

  /// `Open Map`
  String get kOpenMap {
    return Intl.message(
      'Open Map',
      name: 'kOpenMap',
      desc: '',
      args: [],
    );
  }

  /// `Error opening map`
  String get kErrorOpeningMap {
    return Intl.message(
      'Error opening map',
      name: 'kErrorOpeningMap',
      desc: '',
      args: [],
    );
  }

  /// `Search for location`
  String get kSearchForLocation {
    return Intl.message(
      'Search for location',
      name: 'kSearchForLocation',
      desc: '',
      args: [],
    );
  }

  /// `No locations found`
  String get kNoLocationsFound {
    return Intl.message(
      'No locations found',
      name: 'kNoLocationsFound',
      desc: '',
      args: [],
    );
  }

  /// `Open with Map`
  String get kOpenWithMap {
    return Intl.message(
      'Open with Map',
      name: 'kOpenWithMap',
      desc: '',
      args: [],
    );
  }

  /// `Location not available`
  String get kLocationNotAvailable {
    return Intl.message(
      'Location not available',
      name: 'kLocationNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Contact Options`
  String get kContactOptions {
    return Intl.message(
      'Contact Options',
      name: 'kContactOptions',
      desc: '',
      args: [],
    );
  }

  /// `Call`
  String get kCallPhoneNumber {
    return Intl.message(
      'Call',
      name: 'kCallPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Copy Number`
  String get kCopyPhoneNumber {
    return Intl.message(
      'Copy Number',
      name: 'kCopyPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Phone number copied to clipboard`
  String get kPhoneNumberCopied {
    return Intl.message(
      'Phone number copied to clipboard',
      name: 'kPhoneNumberCopied',
      desc: '',
      args: [],
    );
  }

  /// `Could not copy phone number`
  String get kCouldNotCopyPhoneNumber {
    return Intl.message(
      'Could not copy phone number',
      name: 'kCouldNotCopyPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Could not make phone call`
  String get kCouldNotMakePhoneCall {
    return Intl.message(
      'Could not make phone call',
      name: 'kCouldNotMakePhoneCall',
      desc: '',
      args: [],
    );
  }

  /// `Could not initiate call directly. Showing options...`
  String get kShowingOptions {
    return Intl.message(
      'Could not initiate call directly. Showing options...',
      name: 'kShowingOptions',
      desc: '',
      args: [],
    );
  }

  /// `Would you like to call or copy this number?`
  String get kWouldYouLikeToCallOrCopy {
    return Intl.message(
      'Would you like to call or copy this number?',
      name: 'kWouldYouLikeToCallOrCopy',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
