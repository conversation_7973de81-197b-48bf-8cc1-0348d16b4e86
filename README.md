### README for Ajmal Now Doctor App

#### Project Overview
The Ajmal Now Doctor app is a cross-platform mobile application designed to enhance healthcare services. Developed using Flutter, it targets both Android and iOS platforms, adhering to clean architecture principles. This app offers robust backend support including data storage, user authentication, and push notifications, providing a seamless experience for healthcare providers and recipients.

#### Architecture
The app is structured based on features, where each feature encapsulates its own models, logic, and views. It utilizes clean architecture principles to ensure scalability and maintainability. Localization is managed using the `intl` package, facilitating dual language support with editable files located in the `l10n` folder. Navigation and routing are handled by the `go_router` package.

#### Features
1. **Multi-language Support**: Offers a bilingual interface to cater to a diverse user base.
2. **User Notifications**: Integrated notification system to keep users informed of important updates and communications.
3. **Customizable UI**: Theming and customizable user interface elements to ensure a pleasant visual experience.
4. **Advanced Routing**: Efficient navigation management within the app using a custom router setup.
5. **Initial Splash Screen**: A splash screen that enhances the user experience from the moment the app is launched.

#### Features for Doctors/Service Providers
1. **Create Services**: Doctors can create listings for the services they provide, including parameters such as service type and duration.
2. **View Appointments**: View all scheduled appointments in a calendar format, including comprehensive details.
3. **Manage Appointments**: Edit or cancel appointments, with options for automatic patient notifications.

#### Installation
1. **Install Android Studio** and **Configure Flutter**: Download and install Android Studio and the Flutter SDK. Ensure you run `flutter doctor` to check for any missing dependencies.
2. **Clone the Repository**:
   ```
   git clone https://github.com/ajmalnow/ajmal_now_doctor.git
   cd ajmal_now_doctor
   ```
3. **Install Dependencies**:
   ```
   flutter pub get
   ```
4. **Run the App**:
   ```
   flutter run
   ```

#### Dependencies
The app uses several dependencies to enhance functionality:
- `intl`: For localization support.
- `firebase_auth`, `firebase_core`, `firebase_messaging`: For Firebase services.
- `google_maps_flutter`, `geolocator`: For map functionalities.
- `dio`: For network calls and data fetching.
- `flutter_riverpod`: For state management.
- Additional UI libraries like `cached_network_image` and `carousel_slider`.

# Flutter Internationalization (i18n) Guide

This guide explains how to use internationalization (i18n) in your Flutter project.

## Setup

1. Ensure you have the following dependencies in your `pubspec.yaml`:

   ```yaml
   dependencies:
     flutter:
       sdk: flutter
     flutter_localizations:
       sdk: flutter
     intl: ^0.18.0
   dev_dependencies:
     flutter_test:
       sdk: flutter
     intl_utils: ^2.8.2

   flutter_intl:
     enabled: true
   ```

2. Run `flutter pub get` to update your dependencies.

## Adding New Strings

1. Locate the ARB files in your project:
   - `lib/l10n/intl_en.arb` (for English)
   - `lib/l10n/intl_ar.arb` (for Arabic)

2. Add your new string to both files. Use the same key, but translate the value:

   In `lib/l10n/intl_en.arb`:
   ```json
   {
     "kEnterYourPassword": "Enter Your Password"
   }
   ```

   In `lib/l10n/intl_ar.arb`:
   ```json
   {
     "kEnterYourPassword": "أدخل كلمة المرور"
   }
   ```

## Generating Localization Files

After adding or modifying strings in your ARB files, you need to regenerate the localization files:

1. Open a terminal in your project root directory.
2. Run the following command:
   ```
   flutter pub run intl_utils:generate
   ```

This command will generate the necessary Dart files for localization.

## Using Localized Strings in Your Code

To use the localized strings in your Flutter code:

1. Import the generated localization file (usually named `S.dart` or similar) in your Dart file:
   ```dart
   import 'package:your_project_name/generated/l10n.dart';
   ```

2. Use the localized string in your widget:
   ```dart
   Text(S.of(context).kEnterYourPassword)
   ```

This will automatically display the correct string based on the current locale of the device.

## Tips

- Always run the generation command after adding new strings or modifying existing ones.
- Keep your ARB files synchronized across all supported languages.
- Use descriptive keys for your strings to make them easy to identify and maintain.

By following this guide, you can effectively manage internationalization in your Flutter project, allowing your app to support multiple languages with ease.

# Flutter build_runner and Code Generation Guide

This guide explains how to use `build_runner` for code generation in your Flutter project, focusing on `freezed` and `json_serializable` packages.

## Setup

Ensure you have the following dependencies in your `pubspec.yaml`:

```yaml
dependency_overrides:
  watcher: 1.1.0
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: 2.4.9
  freezed: 2.2.1
  freezed_annotation: 2.4.4
  json_serializable: 6.8.0
```

Run `flutter pub get` to update your dependencies.

## Using build_runner

`build_runner` is a powerful tool for generating code in Dart and Flutter projects. It's commonly used with packages like `freezed` and `json_serializable`.

### Example Usage

Here's an example of a class that uses `freezed` and `json_serializable`:

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'reservations.freezed.dart';
part 'reservations.g.dart';

@freezed
class ANReservationsModel with _$ANReservationsModel {
  const factory ANReservationsModel({
    String? id,
    required String purpose,
    @JsonKey(name: 'appointment_date') required DateTime appointmentDate,
    required ANUserModel client,
    required ANServiceModel service,
    required HistoryStatus status,
    String? token,
    @JsonKey(name: 'user_comment') String? comment,
    @JsonKey(name: 'user_rating') double? rating,
  }) = _ANReservationsModel;

  factory ANReservationsModel.fromJson(Map<String, Object?> json) =>
      _$ANReservationsModelFromJson(json);
}
```

### Generated Files

When you run `build_runner`, it generates two files:

1. `reservations.freezed.dart`: 
   - Generated by `freezed`
   - Contains the implementation of the `ANReservationsModel` class
   - Provides immutability, equality comparisons, and copy methods

2. `reservations.g.dart`:
   - Generated by `json_serializable`
   - Contains JSON serialization and deserialization methods

These generated files should not be manually edited as they are overwritten each time you run the build command.

### Running build_runner

To generate or update these files, run the following command in your terminal:

```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

This command:
- Analyzes your code
- Generates the necessary `.freezed.dart` and `.g.dart` files
- Deletes any conflicting outputs (useful when you've made changes to your models)

For continuous generation during development, you can use:

```bash
flutter pub run build_runner watch --delete-conflicting-outputs
```

This will watch for changes in your files and automatically regenerate code when needed.

## Best Practices

1. Always run `build_runner` after making changes to your models.
2. Include generated files in your `.gitignore` to avoid version control conflicts.
3. Use meaningful names for your models and follow Dart naming conventions.
4. Utilize `@JsonKey` annotations for custom JSON key names or parsing logic.

By following this guide, you can effectively use `build_runner` with `freezed` and `json_serializable` in your Flutter project, allowing for robust, type-safe models with easy JSON serialization.

#### Deployment
For detailed deployment instructions for Android and iOS, refer to the official Flutter documentation:
- [Android Deployment](https://docs.flutter.dev/deployment/android)
- [iOS Deployment](https://docs.flutter.dev/deployment/ios)

#### Contributing
Contributions to the Ajmal Now Doctor app are welcome. Please fork the repository, make your changes, and submit a pull request for review.

#### Note
Ensure that the backend services, built using Payload CMS, Express.js, and MongoDB, are properly configured and operational for the app to function fully.
